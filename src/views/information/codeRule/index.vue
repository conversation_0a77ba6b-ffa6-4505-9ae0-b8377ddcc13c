<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="条码类型" prop="codeType">
              <el-select v-model="queryParams.codeType" placeholder="请选择条码类型">
                <el-option v-for="dict in code_rule" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
              <!--              <el-select v-model="queryParams.codeType" clearable class="status-select" placeholder="请选择条码类型">
                <el-option v-for="dict in barcodeTypes" :key="dict.type" :label="dict.type" :value="dict.type" />
              </el-select>-->
            </el-form-item>
            <el-form-item label="代号" prop="acnCode">
              <el-input v-model="queryParams.acnCode" placeholder="请输入代号" clearable @keyup.enter="handleQuery" />
              <!--              <el-select v-model="queryParams.acnCode" clearable class="status-select" placeholder="请选择类型">
                <el-option v-for="ancType in barcodeAncTypes" :key="ancType.type" :label="ancType.type" :value="ancType.type" />
              </el-select>-->
            </el-form-item>
            <el-form-item label="名称" prop="acnDesc">
              <el-input v-model="queryParams.acnDesc" placeholder="请输入名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="是否可用" prop="enab">
              <el-select v-model="queryParams.enab" placeholder="请选择是否可用">
                <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['information:codeRule:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['information:codeRule:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['information:codeRule:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['information:codeRule:export']" type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="codeRuleList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="true" label="ID" align="center" prop="id" />
        <el-table-column label="条码类型" align="center" prop="codeType">
          <template #default="scope">
            <dict-tag :options="code_rule" :value="scope.row.codeType" />
          </template>
        </el-table-column>
        <el-table-column label="代号" align="center" prop="acnCode" />
        <el-table-column label="名称" align="center" prop="acnDesc" />
        <el-table-column label="是否可用" align="center" prop="enab">
          <template #default="scope">
            <dict-tag :options="is_use" :value="scope.row.enab" />
          </template>
        </el-table-column>
        <el-table-column label="规则" align="center" prop="codingRule" />
        <el-table-column label="备注" align="center" prop="rem" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['information:codeRule:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['information:codeRule:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改代号设置对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" append-to-body>
      <el-form ref="codeRuleFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="条码类型" prop="codeType">
          <el-select v-model="form.codeType" placeholder="请选择条码类型">
            <el-option v-for="dict in code_rule" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="代号" prop="acnCode">
          <el-input v-model="form.acnCode" placeholder="请输入代号" />
        </el-form-item>
        <el-form-item label="名称" prop="acnDesc">
          <el-input v-model="form.acnDesc" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="是否可用" prop="enab">
          <!--          <el-input v-model="form.enab" placeholder="请输入是否可用" />-->
          <el-select v-model="form.enab" placeholder="请选择条码类型">
            <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="规则" prop="codingRule">
          <el-input v-model="form.codingRule" placeholder="请输入规则" />
        </el-form-item>
        <el-form-item label="备注" prop="rem">
          <el-input v-model="form.rem" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CodeRule" lang="ts">
import { addCodeRule, delCodeRule, getBarcodeAncTypes, getBarcodeTypes, getCodeRule, listCodeRule, updateCodeRule } from '@/api/information/codeRule';
import { CodeRuleForm, CodeRuleQuery, CodeRuleVO } from '@/api/information/codeRule/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { code_rule, is_use } = toRefs<any>(proxy?.useDict('code_rule', 'is_use'));
const codeRuleList = ref<CodeRuleVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const codeRuleFormRef = ref<ElFormInstance>();

const barcodeTypes = ref<Array<string>>([]);
const barcodeAncTypes = ref<Array<string>>([]);

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CodeRuleForm = {
  id: undefined,
  codeType: undefined,
  acnCode: undefined,
  acnDesc: undefined,
  enab: undefined,
  codingRule: undefined,
  rem: undefined
};
const data = reactive<PageData<CodeRuleForm, CodeRuleQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    codeType: undefined,
    acnCode: undefined,
    acnDesc: undefined,
    enab: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }],
    codeType: [{ required: true, message: '条码类型不能为空', trigger: 'change' }],
    acnCode: [{ required: true, message: '代号不能为空', trigger: 'blur' }],
    acnDesc: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
    enab: [{ required: true, message: '是否可用不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询代号设置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCodeRule(queryParams.value);
  codeRuleList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  codeRuleFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: CodeRuleVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加代号设置';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: CodeRuleVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getCodeRule(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改代号设置';
};

/** 提交按钮 */
const submitForm = () => {
  codeRuleFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateCodeRule(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addCodeRule(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: CodeRuleVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除代号设置编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delCodeRule(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};
/**
 * 获取条码类型下拉框
 */
const getBarcodeType = async () => {
  const res = await getBarcodeTypes();
  barcodeTypes.value = res.data;
};

/**
 * 获取条码类型下拉框
 */
const getBarcodeAncType = async () => {
  const res = await getBarcodeAncTypes();
  barcodeAncTypes.value = res.data;
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'information/codeRule/export',
    {
      ...queryParams.value
    },
    `codeRule_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
  // getBarcodeType();
  // getBarcodeAncType();
});
</script>
