<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="工号" prop="salNo">
              <el-input v-model="queryParams.salNo" placeholder="请输入工号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="姓名" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['information:sfcSalm:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['information:sfcSalm:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['information:sfcSalm:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['information:sfcSalm:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="sfcSalmList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" align="center" prop="id" v-if="true" />
        <el-table-column label="工号" align="center" prop="salNo" />
        <el-table-column label="姓名" align="center" prop="name" />
        <!--        <el-table-column label="类型" align="center" prop="sType" />-->
        <!--        <el-table-column label="分组" align="center" prop="sGroup" />-->
        <el-table-column label="部门" align="center" prop="dep" />
        <el-table-column label="离职日期" align="center" prop="dutOtD" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.dutOtD, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="操作姓名" align="center" prop="usrName" />-->
        <!--        <el-table-column label="主机" align="center" prop="host" />-->
        <!--        <el-table-column label="操作时间" align="center" prop="sysdt" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sysdt, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>-->
        <!--        <el-table-column label="标志" align="center" prop="flag" />-->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['information:sfcSalm:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['information:sfcSalm:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改人员设定记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="sfcSalmFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="工号" prop="salNo">
          <el-input v-model="form.salNo" placeholder="请输入工号" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="分组" prop="sGroup">
          <el-input v-model="form.sGroup" placeholder="请输入分组" />
        </el-form-item>
        <el-form-item label="部门" prop="dep">
          <el-input v-model="form.dep" placeholder="请输入部门" />
        </el-form-item>
        <el-form-item label="离职日期" prop="dutOtD">
          <el-date-picker clearable v-model="form.dutOtD" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择离职日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="操作姓名" prop="usrName">
          <el-input v-model="form.usrName" placeholder="请输入操作姓名" />
        </el-form-item>
        <el-form-item label="主机" prop="host">
          <el-input v-model="form.host" placeholder="请输入主机" />
        </el-form-item>
        <el-form-item label="操作时间" prop="sysdt">
          <el-date-picker clearable v-model="form.sysdt" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择操作时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="标志" prop="flag">
          <el-input v-model="form.flag" placeholder="请输入标志" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SfcSalm" lang="ts">
import { addSfcSalm, delSfcSalm, getSfcSalm, listSfcSalm, updateSfcSalm } from '@/api/information/sfcSalm';
import { SfcSalmForm, SfcSalmQuery, SfcSalmVO } from '@/api/information/sfcSalm/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const sfcSalmList = ref<SfcSalmVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const sfcSalmFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: SfcSalmForm = {
  id: undefined,
  salNo: undefined,
  name: undefined,
  sType: undefined,
  sGroup: undefined,
  dep: undefined,
  dutOtD: undefined,
  usrName: undefined,
  host: undefined,
  sysdt: undefined,
  flag: undefined
};
const data = reactive<PageData<SfcSalmForm, SfcSalmQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    salNo: undefined,
    name: undefined,
    sType: undefined,
    sGroup: undefined,
    dep: undefined,
    dutOtD: undefined,
    usrName: undefined,
    host: undefined,
    sysdt: undefined,
    flag: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询人员设定记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listSfcSalm(queryParams.value);
  sfcSalmList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  sfcSalmFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: SfcSalmVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加人员设定记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: SfcSalmVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getSfcSalm(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改人员设定记录';
};

/** 提交按钮 */
const submitForm = () => {
  sfcSalmFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateSfcSalm(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addSfcSalm(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: SfcSalmVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除人员设定记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delSfcSalm(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'information/sfcSalm/export',
    {
      ...queryParams.value
    },
    `sfcSalm_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
