<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="装配部门" prop="cntType">
              <el-select v-model="queryParams.cntType" placeholder="请选择类型" clearable>
                <el-option v-for="dict in cnt_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="统计日期" style="width: 308px">
              <el-date-picker
                v-model="dateRangeSysDt"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button type="primary" icon="Search" @click="statistic" v-hasPermi="['information:rygstjb:statistic']">统计</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['information:rygstjb:export']">下载</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <!--      <template #header>-->
      <!--        <el-row :gutter="10" class="mb8">-->
      <!--          &lt;!&ndash;          <el-col :span="1.5">-->
      <!--            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['information:rygstjb:add']">新增</el-button>-->
      <!--          </el-col>-->
      <!--          <el-col :span="1.5">-->
      <!--            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['information:rygstjb:edit']">修改</el-button>-->
      <!--          </el-col>-->
      <!--          <el-col :span="1.5">-->
      <!--            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['information:rygstjb:remove']">删除</el-button>-->
      <!--          </el-col>&ndash;&gt;-->
      <!--          <el-col :span="1.5">-->
      <!--            <el-button type="warning" plain icon="Download" @click="handleExport"-->
      <!--                       v-hasPermi="['information:rygstjb:export']">下载-->
      <!--            </el-button>-->
      <!--          </el-col>-->
      <!--          <el-col :span="1.5">-->
      <!--            <el-button type="warning" plain icon="Download" @click="statistic"-->
      <!--                       v-hasPermi="['information:rygstjb:statistic']">统计-->
      <!--            </el-button>-->
      <!--          </el-col>-->
      <!--          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>-->
      <!--        </el-row>-->
      <!--      </template>-->

      <el-table v-loading="loading" border :data="rygstjbList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!--        <el-table-column label="id" align="center" prop="id" v-if="true" />-->
        <el-table-column label="装配部门" align="center" prop="cntType">
          <template #default="scope">
            <dict-tag :options="cnt_type" :value="scope.row.cntType" />
          </template>
        </el-table-column>
        <el-table-column label="日期" align="center" prop="sysDt" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sysDt, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="直接人数" align="center" prop="zjStandRs" />
        <el-table-column label="间接人数" align="center" prop="jjStandRs" />
        <el-table-column label="总人数" align="center" prop="sumStandRs" />
        <el-table-column label="直接人力出勤总数" align="center" prop="zjSjcqRs" />
        <el-table-column label="间接人力出勤总数" align="center" prop="jjSjcqRs" />
        <el-table-column label="外援总人数" align="center" prop="wyRs" />
        <el-table-column label="出勤总人数" align="center" prop="sumSjcqRs" />
        <el-table-column label="直接人力出勤总工时(H)" align="center" prop="zjSjcqGs" />
        <el-table-column label="(直接+间接+外援)出勤总工时(H)" align="center" prop="sumSjcqGs" />
        <el-table-column label="直接扫描工时" align="center" prop="zjSmgs" />
        <el-table-column label="工时利用率" align="center" prop="zjZb" />
        <el-table-column label="损失工时" align="center" prop="ssGs" />
        <el-table-column label="未扫描人数" align="center" prop="wsmRs" />
        <el-table-column label="直接扫描人数" align="center" prop="zjSmRs" />
        <!--        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
        <!--          <template #default="scope">-->
        <!--            <el-tooltip content="修改" placement="top">-->
        <!--              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"-->
        <!--                         v-hasPermi="['information:rygstjb:edit']"></el-button>-->
        <!--            </el-tooltip>-->
        <!--            <el-tooltip content="删除" placement="top">-->
        <!--              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"-->
        <!--                         v-hasPermi="['information:rygstjb:remove']"></el-button>-->
        <!--            </el-tooltip>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改装配工时统计表对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="rygstjbFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="统计类型" prop="cntType">
          <el-select v-model="form.cntType" placeholder="请选择统计类型">
            <el-option v-for="dict in cnt_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="统计日期" prop="sysDt">
          <el-date-picker clearable v-model="form.sysDt" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择统计日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="直接标准人数" prop="zjStandRs">
          <el-input v-model="form.zjStandRs" placeholder="请输入直接标准人数" />
        </el-form-item>
        <el-form-item label="间接标准人数" prop="jjStandRs">
          <el-input v-model="form.jjStandRs" placeholder="请输入间接标准人数" />
        </el-form-item>
        <el-form-item label="标准总人数" prop="sumStandRs">
          <el-input v-model="form.sumStandRs" placeholder="请输入标准总人数" />
        </el-form-item>
        <el-form-item label="直接实际出勤人数" prop="zjSjcqRs">
          <el-input v-model="form.zjSjcqRs" placeholder="请输入直接实际出勤人数" />
        </el-form-item>
        <el-form-item label="间接实际出勤人数" prop="jjSjcqRs">
          <el-input v-model="form.jjSjcqRs" placeholder="请输入间接实际出勤人数" />
        </el-form-item>
        <el-form-item label="外援人数" prop="wyRs">
          <el-input v-model="form.wyRs" placeholder="请输入外援人数" />
        </el-form-item>
        <el-form-item label="实际出勤部人数" prop="sumSjcqRs">
          <el-input v-model="form.sumSjcqRs" placeholder="请输入实际出勤部人数" />
        </el-form-item>
        <el-form-item label="直接出勤工时" prop="zjSjcqGs">
          <el-input v-model="form.zjSjcqGs" placeholder="请输入直接出勤工时" />
        </el-form-item>
        <el-form-item label="直接扫描人数" prop="zjSmgs">
          <el-input v-model="form.zjSmgs" placeholder="请输入直接扫描人数" />
        </el-form-item>
        <el-form-item label="直接占比" prop="zjZb">
          <el-input v-model="form.zjZb" placeholder="请输入直接占比" />
        </el-form-item>
        <el-form-item label="损失工时" prop="ssGs">
          <el-input v-model="form.ssGs" placeholder="请输入损失工时" />
        </el-form-item>
        <el-form-item label="未扫描人数" prop="wsmRs">
          <el-input v-model="form.wsmRs" placeholder="请输入未扫描人数" />
        </el-form-item>
        <el-form-item label="实际出勤总工时" prop="sumSjcqGs">
          <el-input v-model="form.sumSjcqGs" placeholder="请输入实际出勤总工时" />
        </el-form-item>
        <el-form-item label="直接扫描人数" prop="zjSmRs">
          <el-input v-model="form.zjSmRs" placeholder="请输入直接扫描人数" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Rygstjb" lang="ts">
import { addRygstjb, delRygstjb, getRygstjb, listRygstjb, statisticWork, updateRygstjb } from '@/api/information/rygstjb';
import { RygstjbForm, RygstjbQuery, RygstjbVO } from '@/api/information/rygstjb/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { cnt_type } = toRefs<any>(proxy?.useDict('cnt_type'));

const rygstjbList = ref<RygstjbVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeSysDt = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const rygstjbFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RygstjbForm = {
  id: undefined,
  cntType: undefined,
  sysDt: undefined,
  zjStandRs: undefined,
  jjStandRs: undefined,
  sumStandRs: undefined,
  zjSjcqRs: undefined,
  jjSjcqRs: undefined,
  wyRs: undefined,
  sumSjcqRs: undefined,
  zjSjcqGs: undefined,
  zjSmgs: undefined,
  zjZb: undefined,
  ssGs: undefined,
  wsmRs: undefined,
  sumSjcqGs: undefined,
  zjSmRs: undefined
};
const data = reactive<PageData<RygstjbForm, RygstjbQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    cntType: undefined,
    params: {
      sysDt: undefined,
      beginSysDt: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询装配工时统计表列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeSysDt.value, 'SysDt');
  await listRygstjb(queryParams.value)
    .then((res) => {
      rygstjbList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  rygstjbFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  if (dateRangeSysDt.value[0] === '' || dateRangeSysDt.value[1] === '') {
    proxy?.$modal.msgError('查询条件不能全部为空');
    return;
  }
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeSysDt.value = ['', ''];
  queryFormRef.value?.resetFields();
  queryParams.value.cntType = '';
  rygstjbList.value = [];
  // handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: RygstjbVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加装配工时统计表';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: RygstjbVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getRygstjb(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改装配工时统计表';
};

/** 提交按钮 */
const submitForm = () => {
  rygstjbFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateRygstjb(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addRygstjb(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: RygstjbVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除装配工时统计表编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delRygstjb(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  if (dateRangeSysDt.value[0] === '' || dateRangeSysDt.value[1] === '') {
    proxy?.$modal.msgError('请选择下载日期');
    return;
  }
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeSysDt.value, 'SysDt');
  proxy?.download(
    '/sfc/information/rygstjb/export',
    {
      ...queryParams.value
    },
    `装配工时统计表.xlsx`
  );
};

/**
 * 统计按钮操作
 */
const statistic = async () => {
  if (dateRangeSysDt.value[0] === '' || dateRangeSysDt.value[1] === '') {
    proxy?.$modal.msgError('统计日期不能为空');
    return;
  }
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeSysDt.value, 'SysDt');
  statisticWork(queryParams.value)
    .then((res) => {
      if (res.code === 200) {
        handleQuery();
        // proxy?.$modal.msgSuccess("操作成功");
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
onMounted(() => {
  loading.value = false;
  // getList();
});
</script>
