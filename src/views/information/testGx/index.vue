<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="工序号" prop="tsGxNo">
              <el-input v-model="queryParams.tsGxNo" placeholder="请输入调试工序号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工序名" prop="tsGxName">
              <el-input v-model="queryParams.tsGxName" placeholder="请输入调试工序名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['information:testGx:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['information:testGx:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['information:testGx:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['information:testGx:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="testGxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="id" align="center" prop="id" v-if="false" />
        <el-table-column label="调试工序号" align="center" prop="tsGxNo" />
        <el-table-column label="调试工序名" width="140" show-overflow-tooltip align="center" prop="tsGxName" />
        <el-table-column label="标准工时" align="center" prop="basGs" />
        <el-table-column label="文件名" width="140" show-overflow-tooltip align="center" prop="sfileName" />
        <el-table-column label="备注" align="center" prop="rem" />
        <el-table-column label="操作工号" align="center" prop="usrId" />
        <el-table-column label="操作姓名" align="center" prop="usrName" />
        <el-table-column label="主机" align="center" prop="host" />
        <el-table-column label="操作时间" align="center" prop="sysdt" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sysdt, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="标志" align="center" prop="flag" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['information:testGx:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['information:testGx:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改调试SOP设置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="testGxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="调试工序号t_gx_no" prop="tsGxNo">
          <el-input v-model="form.tsGxNo" placeholder="请输入调试工序号t_gx_no" />
        </el-form-item>
        <el-form-item label="调试工序名t_gx_name" prop="tsGxName">
          <el-input v-model="form.tsGxName" placeholder="请输入调试工序名t_gx_name" />
        </el-form-item>
        <el-form-item label="标准工时" prop="basGs">
          <el-input v-model="form.basGs" placeholder="请输入标准工时" />
        </el-form-item>
        <el-form-item label="备注" prop="rem">
          <el-input v-model="form.rem" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="文件名" prop="sfileName">
          <el-input v-model="form.sfileName" placeholder="请输入文件名" />
        </el-form-item>
        <el-form-item label="操作工号" prop="usrId">
          <el-input v-model="form.usrId" placeholder="请输入操作工号" />
        </el-form-item>
        <el-form-item label="操作姓名" prop="usrName">
          <el-input v-model="form.usrName" placeholder="请输入操作姓名" />
        </el-form-item>
        <el-form-item label="主机" prop="host">
          <el-input v-model="form.host" placeholder="请输入主机" />
        </el-form-item>
        <el-form-item label="操作时间" prop="sysdt">
          <el-date-picker clearable v-model="form.sysdt" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择操作时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="标志" prop="flag">
          <el-input v-model="form.flag" placeholder="请输入标志" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="TestGx" lang="ts">
import { addTestGx, delTestGx, getTestGx, listTestGx, updateTestGx } from '@/api/information/testGx';
import { TestGxForm, TestGxQuery, TestGxVO } from '@/api/information/testGx/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const testGxList = ref<TestGxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const testGxFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: TestGxForm = {
  id: undefined,
  tsGxNo: undefined,
  tsGxName: undefined,
  basGs: undefined,
  rem: undefined,
  sfileName: undefined,
  usrId: undefined,
  usrName: undefined,
  host: undefined,
  sysdt: undefined,
  flag: undefined
};
const data = reactive<PageData<TestGxForm, TestGxQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    tsGxNo: undefined,
    tsGxName: undefined,
    basGs: undefined,
    rem: undefined,
    sfileName: undefined,
    usrId: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    tsGxNo: [{ required: true, message: '调试工序号t_gx_no不能为空', trigger: 'blur' }],
    tsGxName: [{ required: true, message: '调试工序名t_gx_name不能为空', trigger: 'blur' }],
    basGs: [{ required: true, message: '标准工时不能为空', trigger: 'blur' }],
    rem: [{ required: true, message: '备注不能为空', trigger: 'blur' }],
    sfileName: [{ required: true, message: '文件名不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询调试SOP设置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listTestGx(queryParams.value);
  testGxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  testGxFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: TestGxVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加调试SOP设置';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: TestGxVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getTestGx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改调试SOP设置';
};

/** 提交按钮 */
const submitForm = () => {
  testGxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateTestGx(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addTestGx(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: TestGxVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除调试SOP设置编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delTestGx(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'information/testGx/export',
    {
      ...queryParams.value
    },
    `testGx_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
