<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <table class="search-table">
              <tbody>
                <tr>
                  <td rowspan="2" class="search-label">查询条件</td>
                  <td>
                    <div class="cell-container">
                      <span>完成状态</span>
                      <el-select v-model="queryParams.msta" clearable class="status-select" placeholder="请选择完成状态">
                        <el-option v-for="dict in scan_status" :key="dict.value" :label="dict.label" :value="dict.label" />
                      </el-select>
                    </div>
                  </td>
                  <td>
                    <div class="cell-container">
                      <span>装配类型</span>
                      <el-select v-model="queryParams.zpType" clearable class="type-select" placeholder="请选择装配类型">
                        <el-option v-for="dict in barcode_type" :key="dict.value" :label="dict.label" :value="dict.label"></el-option>
                      </el-select>
                    </div>
                  </td>
                  <td colspan="2">
                    <div class="cell-container">
                      <el-select v-model="queryParams.selectType1" class="select-type" placeholder="请选择搜索类型">
                        <el-option value="工号">工号</el-option>
                        <el-option value="参与人员工号">参与人员工号</el-option>
                        <el-option value="楼层">楼层</el-option>
                      </el-select>
                      <el-input
                        v-if="queryParams.selectType1 !== '楼层'"
                        v-model="queryParams.selectVal1"
                        class="select-value"
                        placeholder="请输入搜索内容"
                      />
                      <el-select
                        v-else
                        v-model="queryParams.selectVal1"
                        class="select-value"
                        placeholder="请选择楼层"
                        @visible-change="loadFloorData"
                      >
                        <el-option v-for="item of floorData" :key="item.acnCode" :value="item.acnDesc" :label="item.acnDesc" />
                      </el-select>
                    </div>
                  </td>
                  <td colspan="2">
                    <div class="cell-container">
                      <el-select v-model="queryParams.selectType2" class="select-type" clearable placeholder="请选择搜索类型">
                        <el-option value="工号">工号</el-option>
                        <el-option value="参与人员工号">参与人员工号</el-option>
                        <el-option value="楼层">楼层</el-option>
                      </el-select>
                      <el-input
                        v-if="queryParams.selectType2 !== '楼层'"
                        v-model="queryParams.selectVal2"
                        class="select-value"
                        placeholder="请输入搜索内容"
                        clearable
                      />
                      <el-select
                        v-else
                        v-model="queryParams.selectVal2"
                        class="select-value"
                        placeholder="请选择楼层"
                        clearable
                        @visible-change="loadFloorData"
                      >
                        <el-option v-for="item of floorData" :key="item.acnCode" :value="item.acnDesc" :label="item.acnDesc" />
                      </el-select>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2">
                    <div class="cell-container">
                      <span>扫描日期</span>
                      <el-date-picker
                        v-model="dateRangeBDtm"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        type="datetimerange"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                        class="date-range-picker"
                      />
                    </div>
                  </td>
                  <td colspan="2">
                    <div class="cell-container">
                      <el-select v-model="queryParams.selectType3" class="select-type" clearable placeholder="请选择搜索类型">
                        <el-option value="工号">工号</el-option>
                        <el-option value="参与人员工号">参与人员工号</el-option>
                        <el-option value="楼层">楼层</el-option>
                      </el-select>
                      <el-input
                        v-if="queryParams.selectType3 !== '楼层'"
                        v-model="queryParams.selectVal3"
                        class="select-value"
                        placeholder="请输入搜索内容"
                        clearable
                      />
                      <el-select
                        v-else
                        v-model="queryParams.selectVal3"
                        class="select-value"
                        placeholder="请选择楼层"
                        clearable
                        @visible-change="loadFloorData"
                      >
                        <el-option v-for="item of floorData" :key="item.acnCode" :value="item.acnDesc" :label="item.acnDesc" />
                      </el-select>
                    </div>
                  </td>
                  <td colspan="2">
                    <div class="cell-container">
                      <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                      <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <!--          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['information:scanReport:add']">
              新增
            </el-button>
          </el-col>-->
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['information:scanReport:edit']"
              >修改
            </el-button>
          </el-col>
          <!--          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
                       v-hasPermi="['information:scanReport:remove']">删除
            </el-button>
          </el-col>-->
          <!--          <el-col :span="1.5">
                      <el-button type="warning" plain icon="Download" @click="handleExport"
                                 v-hasPermi="['information:scanReport:export']">导出
                      </el-button>
                    </el-col>-->
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="download">下载 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="exportReport">导出表格 </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="buttonSearch"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="scanReportList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!--        <el-table-column label="ID" align="center" prop="id" v-if="true"/>-->
        <!--        <el-table-column label="客户" align="center" prop="custNo" />-->
        <!--        <el-table-column label="产品类型" align="center" prop="prdType" />-->
        <!--        <el-table-column label="产品名" width="160" :show-overflow-tooltip="true" align="center" prop="cusName"/>-->
        <!--        <el-table-column label="MO号" width="140" :show-overflow-tooltip="true" align="center" prop="moNo"/>-->
        <!--        <el-table-column label="图号" width="220" :show-overflow-tooltip="true" align="center" prop="wksDwg"/>-->
        <el-table-column label="楼层" width="130" :show-overflow-tooltip="true" align="center" prop="floorNo" />
        <el-table-column label="工号" align="center" prop="empNo" />
        <el-table-column label="姓名" align="center" prop="empName" />
        <el-table-column label="参与人员工号" align="center" prop="participantNo" />
        <el-table-column label="参与人员姓名" align="center" prop="participantName" />
        <el-table-column label="工站号" align="center" prop="pType" />
        <el-table-column label="标准工时" align="center" prop="basGs" />
        <el-table-column label="标配人力" align="center" prop="bpRy" />
        <el-table-column label="条码" width="160" :show-overflow-tooltip="true" align="center" prop="barCode" />
        <!--        <el-table-column label="模组条码" align="center" prop="mzBarcode" />-->
        <el-table-column label="工站名称" align="center" prop="pName" />
        <el-table-column label="开始时间" align="center" prop="bDtm" width="180">
          <!--          <template #default="scope">-->
          <!--            <span>{{ parseTime(scope.row.bDtm, '{y}-{m}-{d}') }}</span>-->
          <!--          </template>-->
        </el-table-column>
        <el-table-column label="结束时间" align="center" prop="eDtm" width="180">
          <!--          <template #default="scope">-->
          <!--            <span>{{ parseTime(scope.row.eDtm, '{y}-{m}-{d}') }}</span>-->
          <!--          </template>-->
        </el-table-column>
        <!--        <el-table-column label="实际工时" align="center" prop="sjGs"/>-->
        <!--        <el-table-column label="良品数量" align="center" prop="qtyOk"/>-->
        <!--        <el-table-column label="不良数量" align="center" prop="qtyNg"/>-->
        <!--        <el-table-column label="不良原因" align="center" prop="reasonName"/>-->
        <el-table-column label="当前进度" align="center" prop="curProd" />
        <el-table-column label="总进度" align="center" prop="sumProd" />
        <el-table-column label="状态" align="center" prop="mSta" />
        <el-table-column label="备注" align="center" prop="rem" />
        <el-table-column label="装配类型" align="center" prop="zpType" />
        <!--        <el-table-column label="ARBPL" align="center" prop="arbpl"/>-->
        <el-table-column label="累计工时" align="center" prop="wkGs" />
        <!--        <el-table-column label="关联号" align="center" prop="hMoNo" />-->
        <!--        <el-table-column label="DR号" width="90" :show-overflow-tooltip="true" align="center" prop="drNo" />-->
        <!--        <el-table-column label="DR项" align="center" prop="drItm" />-->
        <!--        <el-table-column label="SO号" width="90" :show-overflow-tooltip="true" align="center" prop="soNo" />-->
        <!--        <el-table-column label="SO项次" align="center" prop="soItm" />-->
        <!--        <el-table-column label="品号" width="120" :show-overflow-tooltip="true" align="center" prop="prdNo" />-->
        <!--        <el-table-column label="品名" width="180" :show-overflow-tooltip="true" align="center" prop="prdName" />-->
        <!--        <el-table-column label="订单数量" align="center" prop="oQty" />
                <el-table-column label="生产数量" align="center" prop="mQty" />-->
        <!--        <el-table-column label="模组序号" align="center" prop="mzItm" />-->
        <!--        <el-table-column label="模组名称" align="center" prop="mzName" />-->
        <!--        <el-table-column label="统计类型" align="center" prop="contType" />-->
        <!--        <el-table-column label="进度工时" align="center" prop="gyGs" />-->
        <!--        <el-table-column label="不良代码" align="center" prop="reasonCode" />-->
        <!--        <el-table-column label="调试工时" align="center" prop="testGs" />-->
        <!--        <el-table-column label="关联号" align="center" prop="sopBatno" />-->
        <!--        <el-table-column label="部门" align="center" prop="dep" />-->
        <!--        <el-table-column label="人员类别" align="center" prop="sType" />-->
        <!--        <el-table-column label="间接直接" align="center" prop="jjzjRy" />-->
        <!--        <el-table-column label="MO状态" align="center" prop="act14" />-->
        <!--        <el-table-column label="出货日期" align="center" prop="act15" width="180">-->
        <!--          <template #default="scope">-->
        <!--            <span>{{ parseTime(scope.row.act15, '{y}-{m}-{d}') }}</span>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column label="标志" align="center" prop="flag" />-->

        <!--        <el-table-column label="主机" align="center" prop="host" />-->
        <!--        <el-table-column label="操作时间" align="center" prop="sysdt" width="180">
                  <template #default="scope">
                    <span>{{ parseTime(scope.row.sysdt, '{y}-{m}-{d}') }}</span>
                  </template>
                </el-table-column>-->
        <!--        <el-table-column label="产品组" align="center" prop="prdGroup" />-->
        <!--        <el-table-column label="自定义备用" align="center" prop="def0" />-->
        <!--        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                         v-hasPermi="['information:scanReport:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                         v-hasPermi="['information:scanReport:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>-->
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="buttonSearch"
      />
    </el-card>
    <!-- 添加或修改报工扫描记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="scanReportFormRef" :model="form" :rules="rules" label-width="80px">
        <!--        <el-form-item label="工作单号" prop="jobNo">-->
        <!--          <el-input v-model="form.jobNo" placeholder="请输入工作单号" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="客户" prop="custNo">-->
        <!--          <el-input v-model="form.custNo" placeholder="请输入客户" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="产品名" prop="cusName">-->
        <!--          <el-input v-model="form.cusName" placeholder="请输入产品名" />-->
        <!--        </el-form-item>-->
        <el-form-item label="MO号" prop="moNo">
          <el-input v-model="form.moNo" placeholder="请输入MO号" :disabled="true" />
        </el-form-item>
        <!--        <el-form-item label="关联号" prop="hMoNo">-->
        <!--          <el-input v-model="form.hMoNo" placeholder="请输入关联号" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="DR号 " prop="drNo">-->
        <!--          <el-input v-model="form.drNo" placeholder="请输入DR号 " />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="DR项" prop="drItm">-->
        <!--          <el-input v-model="form.drItm" placeholder="请输入DR项" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="SO号" prop="soNo">-->
        <!--          <el-input v-model="form.soNo" placeholder="请输入SO号" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="SO项次" prop="soItm">-->
        <!--          <el-input v-model="form.soItm" placeholder="请输入SO项次" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="品号" prop="prdNo">-->
        <!--          <el-input v-model="form.prdNo" placeholder="请输入品号" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="品名" prop="prdName">-->
        <!--          <el-input v-model="form.prdName" placeholder="请输入品名" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="订单数量" prop="oQty">-->
        <!--          <el-input v-model="form.oQty" placeholder="请输入订单数量" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="生产数量" prop="mQty">-->
        <!--          <el-input v-model="form.mQty" placeholder="请输入生产数量" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="楼层" prop="floorNo">-->
        <!--          <el-input v-model="form.floorNo" placeholder="请输入楼层" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="模组序号" prop="mzItm">-->
        <!--          <el-input v-model="form.mzItm" placeholder="请输入模组序号" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="模组名称" prop="mzName">-->
        <!--          <el-input v-model="form.mzName" placeholder="请输入模组名称" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="工站图号" prop="wksDwg">-->
        <!--          <el-input v-model="form.wksDwg" placeholder="请输入工站图号" />-->
        <!--        </el-form-item>-->
        <el-form-item label="工站名称" prop="pName">
          <el-input v-model="form.pName" placeholder="请输入工站名称" :disabled="true" />
        </el-form-item>
        <el-form-item label="开始时间" prop="bDtm">
          <el-date-picker clearable v-model="form.bDtm" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="eDtm">
          <el-date-picker clearable v-model="form.eDtm" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择结束时间">
          </el-date-picker>
        </el-form-item>
        <!--        <el-form-item label="工号" prop="empNo">-->
        <!--          <el-input v-model="form.empNo" placeholder="请输入工号" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="姓名" prop="empName">-->
        <!--          <el-input v-model="form.empName" placeholder="请输入姓名" />-->
        <!--        </el-form-item>-->
        <el-form-item label="总进度" prop="sumProd">
          <el-input v-model="form.sumProd" placeholder="请输入总进度" />
        </el-form-item>
        <el-form-item label="当前进度" prop="curProd">
          <el-input v-model="form.curProd" placeholder="请输入当前进度" />
        </el-form-item>
        <!--        <el-form-item label="良品数量" prop="qtyOk">-->
        <!--          <el-input v-model="form.qtyOk" placeholder="请输入良品数量" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="不良数量" prop="qtyNg">-->
        <!--          <el-input v-model="form.qtyNg" placeholder="请输入不良数量" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="进度工时" prop="gyGs">-->
        <!--          <el-input v-model="form.gyGs" placeholder="请输入进度工时" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="实际工时" prop="sjGs">-->
        <!--          <el-input v-model="form.sjGs" placeholder="请输入实际工时" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="不良代码" prop="reasonCode">-->
        <!--          <el-input v-model="form.reasonCode" placeholder="请输入不良代码" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="不良原因" prop="reasonName">-->
        <!--          <el-input v-model="form.reasonName" placeholder="请输入不良原因" />-->
        <!--        </el-form-item>-->
        <el-form-item label="条码" prop="barCode">
          <el-input v-model="form.barCode" placeholder="请输入条码" :disabled="true" />
        </el-form-item>
        <!--        <el-form-item label="调试工时" prop="testGs">-->
        <!--          <el-input v-model="form.testGs" placeholder="请输入调试工时" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="标配人力" prop="bpRy">-->
        <!--          <el-input v-model="form.bpRy" placeholder="请输入标配人力" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="标准工时" prop="basGs">-->
        <!--          <el-input v-model="form.basGs" placeholder="请输入标准工时" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="累计工时" prop="wkGs">-->
        <!--          <el-input v-model="form.wkGs" placeholder="请输入累计工时" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="备注" prop="rem">-->
        <!--          <el-input v-model="form.rem" placeholder="请输入备注" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="模组条码" prop="mzBarcode">-->
        <!--          <el-input v-model="form.mzBarcode" placeholder="请输入模组条码" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="关联号" prop="sopBatno">-->
        <!--          <el-input v-model="form.sopBatno" placeholder="请输入关联号" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="部门" prop="dep">-->
        <!--          <el-input v-model="form.dep" placeholder="请输入部门" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="间接直接" prop="jjzjRy">-->
        <!--          <el-input v-model="form.jjzjRy" placeholder="请输入间接直接" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="工作中心" prop="arbpl">-->
        <!--          <el-input v-model="form.arbpl" placeholder="请输入工作中心" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="MO状态" prop="act14">-->
        <!--          <el-input v-model="form.act14" placeholder="请输入MO状态" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="出货日期" prop="act15">-->
        <!--          <el-date-picker clearable v-model="form.act15" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"-->
        <!--                          placeholder="请选择出货日期">-->
        <!--          </el-date-picker>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="标志" prop="flag">-->
        <!--          <el-input v-model="form.flag" placeholder="请输入标志" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="装配状态" prop="mSta">-->
        <!--          <el-input v-model="form.mSta" placeholder="请输入装配状态" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="主机" prop="host">-->
        <!--          <el-input v-model="form.host" placeholder="请输入主机" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="操作时间" prop="sysdt">-->
        <!--          <el-date-picker clearable v-model="form.sysdt" type="datetime" value-format="YYYY-MM-DD HH:mm:ss"-->
        <!--                          placeholder="请选择操作时间">-->
        <!--          </el-date-picker>-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="产品组" prop="prdGroup">-->
        <!--          <el-input v-model="form.prdGroup" placeholder="请输入产品组" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="参与人员工号" prop="participantNo">-->
        <!--          <el-input v-model="form.participantNo" placeholder="请输入参与人员工号" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="参与人员姓名" prop="participantName">-->
        <!--          <el-input v-model="form.participantName" placeholder="请输入参与人员姓名" />-->
        <!--        </el-form-item>-->
        <!--        <el-form-item label="自定义备用" prop="def0">-->
        <!--          <el-input v-model="form.def0" placeholder="请输入自定义备用" />-->
        <!--        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ScanReport" lang="ts">
import {
  addScanReport,
  delScanReport,
  getCodeRuleList,
  getScanReport,
  listScanReport,
  queryScanReport,
  updateScanReport
} from '@/api/information/scanReport';
import { CodeRuleModel, ScanReportForm, ScanReportQuery, ScanReportVO } from '@/api/information/scanReport/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { scan_status, barcode_type } = toRefs<any>(proxy?.useDict('scan_status', 'barcode_type'));

const floorData = ref<CodeRuleModel[]>([]);

const scanReportList = ref<ScanReportVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeBDtm = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const scanReportFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ScanReportForm = {
  id: undefined,
  jobNo: undefined,
  custNo: undefined,
  prdType: undefined,
  cusName: undefined,
  moNo: undefined,
  hMoNo: undefined,
  drNo: undefined,
  drItm: undefined,
  soNo: undefined,
  soItm: undefined,
  prdNo: undefined,
  prdName: undefined,
  oQty: undefined,
  mQty: undefined,
  floorNo: undefined,
  mzItm: undefined,
  mzName: undefined,
  wksDwg: undefined,
  contType: undefined,
  pType: undefined,
  pName: undefined,
  bDtm: undefined,
  eDtm: undefined,
  empNo: undefined,
  empName: undefined,
  sumProd: undefined,
  curProd: undefined,
  qtyOk: undefined,
  qtyNg: undefined,
  gyGs: undefined,
  sjGs: undefined,
  reasonCode: undefined,
  reasonName: undefined,
  barCode: undefined,
  testGs: undefined,
  bpRy: undefined,
  basGs: undefined,
  wkGs: undefined,
  rem: undefined,
  mzBarcode: undefined,
  sopBatno: undefined,
  zpType: undefined,
  dep: undefined,
  sType: undefined,
  jjzjRy: undefined,
  arbpl: undefined,
  act14: undefined,
  act15: undefined,
  flag: undefined,
  mSta: undefined,
  host: undefined,
  sysdt: undefined,
  prdGroup: undefined,
  participantNo: undefined,
  participantName: undefined,
  def0: undefined
};

const data = reactive<PageData<ScanReportForm, ScanReportQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    zpType: '',
    selectType1: '',
    selectVal1: '',
    floorValue1: '',
    floorValue2: '',
    floorValue3: '',
    selectType2: '',
    selectVal2: '',
    selectType3: '',
    selectVal3: '',
    rangeValue: '',
    //完成状态
    msta: '全部',
    params: {
      bDtm: undefined,
      beginBDtm: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询报工扫描记录列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeBDtm.value, 'BDtm');
  const res = await listScanReport(queryParams.value);
  scanReportList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

// 查询按钮
const buttonSearch = async () => {
  proxy?.addDateRange(queryParams.value, dateRangeBDtm.value, 'BDtm');
  if (
    queryParams.value.zpType === '' &&
    queryParams.value.selectType1 === '' &&
    queryParams.value.selectType2 === '' &&
    queryParams.value.selectType3 === '' &&
    queryParams.value.params.beginBDtm === undefined
  ) {
    proxy?.$modal.msgError('查询条件不能全部为空');
    return;
  }
  await search();
};

const search = async () => {
  loading.value = true;
  queryParams.value.params = {};
  // queryParams.value.selectVal1 = queryParams.value.selectType1 === '楼层' ? queryParams.value.floorValue1 : queryParams.value.selectVal1;
  // queryParams.value.selectVal2 = queryParams.value.selectType2 === '楼层' ? queryParams.value.floorValue2 : queryParams.value.selectVal2;
  // queryParams.value.selectVal3 = queryParams.value.selectType3 === '楼层' ? queryParams.value.floorValue3 : queryParams.value.selectVal3;
  proxy?.addDateRange(queryParams.value, dateRangeBDtm.value, 'BDtm');
  await queryScanReport(queryParams.value)
    .then((res) => {
      scanReportList.value = res.rows;
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  scanReportFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  // getList();
  buttonSearch();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeBDtm.value = ['', ''];
  queryParams.value.selectType1 = '';
  queryParams.value.selectType2 = '';
  queryParams.value.selectType3 = '';
  queryParams.value.selectVal1 = '';
  queryParams.value.selectVal2 = '';
  queryParams.value.selectVal3 = '';
  queryParams.value.zpType = '';
  queryFormRef.value?.resetFields();
  scanReportList.value = [];
  // handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ScanReportVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加报工扫描记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ScanReportVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getScanReport(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改报工扫描记录';
};

const loadFloorData = async (vis) => {
  if (vis) {
    loading.value = true;
    const res = await getCodeRuleList('FLOR');
    floorData.value = res.data;
    loading.value = false;
  }
};

/** 提交按钮 */
const submitForm = () => {
  scanReportFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateScanReport(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addScanReport(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      // await getList();
      await buttonSearch();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ScanReportVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除报工扫描记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delScanReport(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  // await getList();
  await buttonSearch();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'information/scanReport/export',
    {
      ...queryParams.value
    },
    `scanReport_${new Date().getTime()}.xlsx`
  );
};

/**
 * 装配看板实时数据记录
 */
const exportReportKb = () => {
  proxy?.download(
    '/assembly/kanban/download',
    {
      ...queryParams.value
    },
    `报工扫描_装配看板实时数据记录.xlsx`
  );
};

/**
 * 下载按钮操作
 */
const download = async () => {
  queryParams.value.params = {};
  // queryParams.value.selectVal1 = queryParams.value.selectType1 === '楼层' ? queryParams.value.floorValue1 : queryParams.value.selectVal1;
  // queryParams.value.selectVal2 = queryParams.value.selectType2 === '楼层' ? queryParams.value.floorValue2 : queryParams.value.selectVal2;
  // queryParams.value.selectVal3 = queryParams.value.selectType3 === '楼层' ? queryParams.value.floorValue3 : queryParams.value.selectVal3;
  proxy?.addDateRange(queryParams.value, dateRangeBDtm.value, 'BDtm');
  proxy?.download(
    '/sfc/information/scanReport/download',
    {
      ...queryParams.value
    },
    `报工扫描_报工扫描记录下载.xlsx`
  );
};

/**
 * 导出表格按钮操作
 */
const exportReport = async () => {
  queryParams.value.params = {};
  // queryParams.value.selectVal1 = queryParams.value.selectType1 === '楼层' ? queryParams.value.floorValue1 : queryParams.value.selectVal1;
  // queryParams.value.selectVal2 = queryParams.value.selectType2 === '楼层' ? queryParams.value.floorValue2 : queryParams.value.selectVal2;
  // queryParams.value.selectVal3 = queryParams.value.selectType3 === '楼层' ? queryParams.value.floorValue3 : queryParams.value.selectVal3;
  proxy?.addDateRange(queryParams.value, dateRangeBDtm.value, 'BDtm');
  proxy?.download(
    '/sfc/information/scanReport/report/export',
    {
      ...queryParams.value
    },
    `报工扫描_导出表格数据.xlsx`
  );
};
onMounted(() => {
  loading.value = false;
  // getList();
});
</script>

<style lang="scss" scoped>
.search-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;

  td {
    padding: 12px;
    border: 1px solid #ebeef5;
    vertical-align: middle;
    white-space: nowrap;

    &.search-label {
      width: 80px;
      text-align: center;
      font-weight: bold;
      background-color: #f5f7fa;
    }

    .cell-container {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;

      span {
        margin-right: 8px;
        font-weight: 500;
        flex-shrink: 0;
        min-width: 60px;
      }

      .status-select,
      .type-select {
        min-width: 150px;
      }

      .select-type {
        min-width: 150px;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .select-value {
        flex: 1;
        min-width: 200px;
      }

      .date-range-picker {
        width: 100%;
        min-width: 300px;
      }
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-select),
:deep(.el-input),
:deep(.el-date-editor) {
  width: auto !important;
}

:deep(.el-date-editor.el-input__wrapper) {
  width: 400px !important;
}

:deep(.el-select .el-input) {
  width: 100% !important;
}
</style>
