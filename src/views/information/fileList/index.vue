<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="序号" prop="fileItm">
              <el-input v-model="queryParams.fileItm" placeholder="请输入序号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="文件名" prop="fileListName">
              <el-input v-model="queryParams.fileListName" placeholder="请输入文件名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="标识" prop="flag">
              <el-input v-model="queryParams.flag" placeholder="请输入标识" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['information:fileList:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['information:fileList:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['information:fileList:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['information:fileList:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="fileListList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!--        <el-table-column label="id" align="center" prop="id" v-if="true" />-->
        <el-table-column label="序号" align="center" prop="fileItm" />
        <el-table-column label="文件名" align="center" prop="fileListName" />
        <el-table-column label="类别" align="center" prop="fileType" />
        <el-table-column label="标识" align="center" prop="flag" />
        <el-table-column label="备注" align="center" prop="rem" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['information:fileList:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['information:fileList:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改文件清单设置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="fileListFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="序号" prop="fileItm">
          <el-input v-model="form.fileItm" placeholder="请输入序号" />
        </el-form-item>
        <el-form-item label="文件名" prop="fileListName">
          <el-input v-model="form.fileListName" placeholder="请输入文件名" />
        </el-form-item>
        <el-form-item label="标识" prop="flag">
          <el-input v-model="form.flag" placeholder="请输入标识" />
        </el-form-item>
        <el-form-item label="备注" prop="rem">
          <el-input v-model="form.rem" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="FileList" lang="ts">
import { addFileList, delFileList, getFileList, listFileList, updateFileList } from '@/api/information/fileList';
import { FileListForm, FileListQuery, FileListVO } from '@/api/information/fileList/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const fileListList = ref<FileListVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const fileListFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: FileListForm = {
  id: undefined,
  fileItm: undefined,
  fileListName: undefined,
  fileType: undefined,
  flag: undefined,
  rem: undefined
};
const data = reactive<PageData<FileListForm, FileListQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    fileItm: undefined,
    fileListName: undefined,
    fileType: undefined,
    flag: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    fileItm: [{ required: true, message: '序号不能为空', trigger: 'blur' }],
    fileListName: [{ required: true, message: '文件名不能为空', trigger: 'blur' }],
    fileType: [{ required: true, message: '类别不能为空', trigger: 'change' }]
    // flag: [{ required: true, message: '标识不能为空', trigger: 'blur' }],
    // rem: [{ required: true, message: '备注不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询文件清单设置列表 */
const getList = async () => {
  loading.value = true;
  const res = await listFileList(queryParams.value);
  fileListList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  fileListFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: FileListVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加文件清单设置';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: FileListVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getFileList(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改文件清单设置';
};

/** 提交按钮 */
const submitForm = () => {
  fileListFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateFileList(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addFileList(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: FileListVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除文件清单设置编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delFileList(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'information/fileList/export',
    {
      ...queryParams.value
    },
    `fileList_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
