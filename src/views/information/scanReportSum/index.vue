<template>
  <div class="scan-report-container">
    <!-- 搜索区域 -->
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
                :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search-section">
        <el-card shadow="hover" class="search-card">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form">
            <table class="search-table">
              <tbody>
              <tr>
                <td rowspan="2" class="search-label">查询条件</td>
                <td>
                  <div class="cell-container">
                    <span>完成状态</span>
                    <el-select v-model="queryParams.msta" clearable class="status-select" placeholder="请选择完成状态">
                      <el-option v-for="dict in scan_status" :key="dict.value" :label="dict.label"
                                 :value="dict.value" />
                    </el-select>
                  </div>
                </td>
                <td>
                  <div class="cell-container">
                    <span>装配类型</span>
                    <el-select v-model="queryParams.zpType" clearable class="type-select" placeholder="请选择装配类型">
                      <el-option v-for="item in zpData" :key="item.acnDesc" :label="item.acnDesc"
                                 :value="item.acnDesc" />
                    </el-select>
                  </div>
                </td>
                <td colspan="2">
                  <div class="cell-container">
                    <el-select v-model="queryParams.selectType1" class="select-type" clearable
                               placeholder="请选择搜索类型">
                      <el-option value="工号">工号</el-option>
                      <el-option value="参与人员工号">参与人员工号</el-option>
                      <el-option value="管理工号">管理工号</el-option>
                      <el-option value="产品名称">产品名称</el-option>
                      <el-option value="图号">图号</el-option>
                      <el-option value="MO号">MO号</el-option>
                      <el-option value="设备项次">设备项次</el-option>
                      <el-option value="条码">条码</el-option>
                      <el-option value="设备SN">设备SN</el-option>
                      <el-option value="WD_SN">WD_SN</el-option>
                      <el-option value="工站名称">工站名称</el-option>
                      <el-option value="楼层">楼层</el-option>
                    </el-select>
                    <el-input
                      v-if="queryParams.selectType1 !== '楼层'"
                      v-model="queryParams.selectVal1"
                      class="select-value"
                      placeholder="请输入搜索内容"
                      clearable
                    />
                    <el-select v-else v-model="queryParams.selectVal1" class="select-value" size="default"
                               placeholder="请选择楼层" @visible-change="loadFloorData">
                      <el-option v-for="item of floorData" :key="item.acnCode" :value="item.acnDesc"
                                 :label="item.acnDesc" />
                    </el-select>
                  </div>
                </td>
                <td colspan="2">
                  <div class="cell-container">
                    <el-select v-model="queryParams.selectType2" class="select-type" clearable
                               placeholder="请选择搜索类型">
                      <el-option value="工号">工号</el-option>
                      <el-option value="参与人员工号">参与人员工号</el-option>
                      <el-option value="管理工号">管理工号</el-option>
                      <el-option value="产品名称">产品名称</el-option>
                      <el-option value="图号">图号</el-option>
                      <el-option value="MO号">MO号</el-option>
                      <el-option value="设备项次">设备项次</el-option>
                      <el-option value="条码">条码</el-option>
                      <el-option value="设备SN">设备SN</el-option>
                      <el-option value="WD_SN">WD_SN</el-option>
                      <el-option value="工站名称">工站名称</el-option>
                      <el-option value="楼层">楼层</el-option>
                    </el-select>
                    <el-input
                      v-if="queryParams.selectType2 !== '楼层'"
                      v-model="queryParams.selectVal2"
                      class="select-value"
                      placeholder="请输入搜索内容"
                      clearable
                    />
                    <el-select
                      v-else
                      v-model="queryParams.selectVal2"
                      class="select-value"
                      placeholder="请选择楼层"
                      clearable
                      size="default"
                      @visible-change="loadFloorData"
                    >
                      <el-option v-for="item of floorData" :key="item.acnCode" :value="item.acnDesc"
                                 :label="item.acnDesc" />
                    </el-select>
                  </div>
                </td>
              </tr>
              <tr>
                <td colspan="2">
                  <div class="cell-container">
                    <span>扫描日期</span>
                    <el-date-picker
                      v-model="dateRangeBDtm"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      type="datetimerange"
                      range-separator="-"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                      class="date-range-picker"
                    />
                  </div>
                </td>
                <td colspan="2">
                  <div class="cell-container">
                    <el-select v-model="queryParams.selectType3" class="select-type" clearable size="default"
                               placeholder="请选择搜索类型">
                      <el-option value="工号">工号</el-option>
                      <el-option value="参与人员工号">参与人员工号</el-option>
                      <el-option value="管理工号">管理工号</el-option>
                      <el-option value="产品名称">产品名称</el-option>
                      <el-option value="图号">图号</el-option>
                      <el-option value="MO号">MO号</el-option>
                      <el-option value="设备项次">设备项次</el-option>
                      <el-option value="条码">条码</el-option>
                      <el-option value="设备SN">设备SN</el-option>
                      <el-option value="WD_SN">WD_SN</el-option>
                      <el-option value="工站名称">工站名称</el-option>
                      <el-option value="楼层">楼层</el-option>
                    </el-select>
                    <el-input
                      v-if="queryParams.selectType3 !== '楼层'"
                      v-model="queryParams.selectVal3"
                      class="select-value"
                      placeholder="请输入搜索内容"
                      clearable
                    />
                    <el-select
                      v-else
                      v-model="queryParams.selectVal3"
                      class="select-value"
                      placeholder="请选择楼层"
                      clearable
                      size="default"
                      @visible-change="loadFloorData"
                    >
                      <el-option v-for="item of floorData" :key="item.acnCode" :value="item.acnDesc"
                                 :label="item.acnDesc" />
                    </el-select>
                  </div>
                </td>
                <td colspan="2">
                  <div class="cell-container">
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
<!--                    <el-button type="success" icon="Download" @click="exportReport">导出</el-button>-->
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
          </el-form>
        </el-card>
      </div>
    </transition>

    <!-- 表格区域 -->
    <el-card shadow="never" class="table-card">
      <el-table
        v-loading="loading"
        :data="paginatedData"
        @selection-change="handleSelectionChange"
        @row-dblclick="handleRowDbClick"
        border
        stripe
        highlight-current-row
      >
        <!--        <el-table-column type="selection" width="55" align="center" />-->
        <el-table-column label="MO号" width="140" :show-overflow-tooltip="true" align="center" prop="moNo">
          <!--          <template #default="{ row }">
            <el-button type="text" @click="showMoDetails(row)">{{ row.moNo }}</el-button>
          </template>-->
        </el-table-column>
        <el-table-column label="装配类型" align="center" prop="zpType" />
        <el-table-column label="全部标准工时" align="center" prop="basGsAll" />
        <el-table-column label="扫描的全部工时" align="center" prop="smGsAll" />
        <el-table-column label="未扫描的全部工时" align="center" prop="syGsAll" />
        <!--        <el-table-column label="扫描工时" align="center" prop="sjGs" width="100" :formatter="(row) => Number(row.sjGs).toFixed(2)" />-->
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handlePagination"
        class="pagination"
      />
    </el-card>

    <!-- MO详情对话框 -->
    <el-dialog
      v-model="moDialog.visible"
      align-center
      :title="`MO号: ${moDialog.moNo} 扫描`"
      width="1000px"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <el-table v-loading="moDialog.loading" :data="moDialogPaginatedData" border stripe height="auto"
                @row-dblclick="handleMoDetailRowDbClick">
        <el-table-column label="MO号" align="center" prop="moNo" width="140" />
        <el-table-column label="装配类型" align="center" prop="zpType" width="120" />
        <el-table-column label="工序名称" align="center" prop="pname" min-width="120" show-overflow-tooltip />
        <el-table-column label="全部标准工时" align="center" prop="basGsAll" width="120" />
        <el-table-column label="已扫描工时" align="center" prop="smGsAll" width="120" />
        <el-table-column label="未扫描工时" align="center" prop="syGsAll" width="120" />
      </el-table>
      <pagination
        v-show="moDialog.total > 0"
        :total="moDialog.total"
        v-model:page="moDialog.pageNum"
        v-model:limit="moDialog.pageSize"
        @pagination="handleMoDialogPagination"
      />
    </el-dialog>

    <!-- 扫描详情对话框 -->
    <el-dialog v-model="scanDialog.visible" align-center :title="`扫描详情`" width="auto" destroy-on-close
               :close-on-click-modal="false">
      <el-table v-loading="scanDialog.loading" :data="scanDialogPaginatedData" border stripe height="auto">
        <el-table-column label="MO号" align="center" prop="moNo" width="140" />
        <el-table-column label="装配类型" align="center" prop="zpType" width="120" />
        <el-table-column label="工序名称" align="center" prop="pname" width="120" />
        <el-table-column label="扫描开始时间" align="center" prop="bdtm" width="160" />
        <el-table-column label="扫描结束时间" align="center" prop="edtm" width="160" />
        <el-table-column label="扫描工时" align="center" prop="sjGs" width="100"
                         :formatter="(row) => Number(row.sjGs).toFixed(2)" />
        <el-table-column label="操作工号" align="center" prop="empNo" width="120" />
        <el-table-column label="操作姓名" align="center" prop="empName" width="120" />
      </el-table>
      <pagination
        v-show="scanDialog.total > 0"
        :total="scanDialog.total"
        v-model:page="scanDialog.pageNum"
        v-model:limit="scanDialog.pageSize"
        @pagination="handleScanDialogPagination"
      />
    </el-dialog>
  </div>
</template>

<script setup name="ScanReport" lang="ts">
import {
  addScanReport,
  delScanReport,
  getCodeRuleList,
  getScanReport,
  listScanReport,
  queryScanReportAll,
  queryScanReportAll2,
  queryScanReportAll3,
  updateScanReport
} from '@/api/information/scanReport';
import {
  CodeRuleModel,
  InfoReportGsVO,
  MoDetailVO,
  ScanMoDetailVO,
  ScanReportForm,
  ScanReportQuery,
  ScanReportVO
} from '@/api/information/scanReport/types';
import { computed, onMounted, ref } from 'vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { scan_status } = toRefs<any>(proxy?.useDict('scan_status'));

const floorData = ref<CodeRuleModel[]>([]);
const zpData = ref<CodeRuleModel[]>([]);

const scanReportList = ref<ScanReportVO[]>([]);
const infoReportGsList = ref<InfoReportGsVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeBDtm = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const scanReportFormRef = ref<ElFormInstance>();

interface MoDialogState {
  visible: boolean;
  loading: boolean;
  moNo: string;
  detailList: ScanReportVO[];
  total: number;
  pageNum: number;
  pageSize: number;
}

const moDialog = reactive<MoDialogState>({
  visible: false,
  loading: false,
  moNo: '',
  detailList: [],
  total: 0,
  pageNum: 1,
  pageSize: 10
});

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ScanReportForm = {
  id: undefined,
  jobNo: undefined,
  custNo: undefined,
  prdType: undefined,
  cusName: undefined,
  moNo: undefined,
  hMoNo: undefined,
  drNo: undefined,
  drItm: undefined,
  soNo: undefined,
  soItm: undefined,
  prdNo: undefined,
  prdName: undefined,
  oQty: undefined,
  mQty: undefined,
  floorNo: undefined,
  mzItm: undefined,
  mzName: undefined,
  wksDwg: undefined,
  contType: undefined,
  pType: undefined,
  pName: undefined,
  bDtm: undefined,
  eDtm: undefined,
  empNo: undefined,
  empName: undefined,
  sumProd: undefined,
  curProd: undefined,
  qtyOk: undefined,
  qtyNg: undefined,
  gyGs: undefined,
  sjGs: undefined,
  reasonCode: undefined,
  reasonName: undefined,
  barCode: undefined,
  testGs: undefined,
  bpRy: undefined,
  basGs: undefined,
  wkGs: undefined,
  rem: undefined,
  mzBarcode: undefined,
  sopBatno: undefined,
  zpType: undefined,
  dep: undefined,
  sType: undefined,
  jjzjRy: undefined,
  arbpl: undefined,
  act14: undefined,
  act15: undefined,
  flag: undefined,
  mSta: undefined,
  host: undefined,
  sysdt: undefined,
  prdGroup: undefined,
  participantNo: undefined,
  participantName: undefined,
  def0: undefined
};

const data = reactive<PageData<ScanReportForm, ScanReportQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    zpType: '',
    selectType1: '',
    selectVal1: '',
    floorValue1: '',
    floorValue2: '',
    floorValue3: '',
    selectType2: '',
    selectVal2: '',
    selectType3: '',
    selectVal3: '',
    rangeValue: '',
    //完成状态
    msta: '全部',
    params: {
      bDtm: undefined,
      beginBDtm: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询报工扫描记录列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeBDtm.value, 'BDtm');
  const res = await listScanReport(queryParams.value);
  scanReportList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

// 查询按钮
const buttonSearch = async () => {
  proxy?.addDateRange(queryParams.value, dateRangeBDtm.value, 'BDtm');
  if (
    queryParams.value.zpType === '' &&
    queryParams.value.selectType1 === '' &&
    queryParams.value.selectType2 === '' &&
    queryParams.value.selectType3 === '' &&
    queryParams.value.params.beginBDtm === ''
  ) {
    proxy?.$modal.msgError('查询条件不能全部为空');
    return;
  }
  await search();
};

const search = async () => {
  loading.value = true;
  queryParams.value.params = {};
  // queryParams.value.selectVal1 = queryParams.value.selectType1 === '楼层' ? queryParams.value.floorValue1 : queryParams.value.selectVal1;
  // queryParams.value.selectVal2 = queryParams.value.selectType2 === '楼层' ? queryParams.value.floorValue2 : queryParams.value.selectVal2;
  // queryParams.value.selectVal3 = queryParams.value.selectType3 === '楼层' ? queryParams.value.floorValue3 : queryParams.value.selectVal3;
  proxy?.addDateRange(queryParams.value, dateRangeBDtm.value, 'BDtm');
  await queryScanReportAll(queryParams.value).then(res => {
    infoReportGsList.value = res.rows;
    total.value = res.total;
  }).finally(() => {
    loading.value = false;
  });
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  scanReportFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  buttonSearch();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeBDtm.value = ['', ''];
  queryParams.value.selectType1 = '';
  queryParams.value.selectType2 = '';
  queryParams.value.selectType3 = '';
  queryParams.value.selectVal1 = '';
  queryParams.value.selectVal2 = '';
  queryParams.value.selectVal3 = '';
  queryParams.value.zpType = '';
  infoReportGsList.value = [];
  scanReportList.value = [];
  queryFormRef.value?.resetFields();
  // handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ScanReportVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加报工扫描记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ScanReportVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getScanReport(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改报工扫描记录';
};

const loadFloorData = async (vis) => {
  if (vis) {
    loading.value = true;
    const res = await getCodeRuleList('FLOR');
    floorData.value = res.data;
    loading.value = false;
  }
};

const loadZpData = async () => {
  loading.value = true;
  const res = await getCodeRuleList('ZPTP');
  zpData.value = res.data;
  loading.value = false;
};

/** 提交按钮 */
const submitForm = () => {
  scanReportFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateScanReport(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addScanReport(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await buttonSearch();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ScanReportVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除报工扫描记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delScanReport(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await buttonSearch();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'information/scanReport/export',
    {
      ...queryParams.value
    },
    `scanReport_${new Date().getTime()}.xlsx`
  );
};

/**
 * 装配看板实时数据记录
 */
const exportReportKb = () => {
  proxy?.download(
    '/assembly/kanban/download',
    {
      ...queryParams.value
    },
    `报工扫描_装配看板实时数据记录.xlsx`
  );
};

/**
 * 下载按钮操作
 */
const download = async () => {
  queryParams.value.params = {};
  // queryParams.value.selectVal1 = queryParams.value.selectType1 === '楼层' ? queryParams.value.floorValue1 : queryParams.value.selectVal1;
  // queryParams.value.selectVal2 = queryParams.value.selectType2 === '楼层' ? queryParams.value.floorValue2 : queryParams.value.selectVal2;
  // queryParams.value.selectVal3 = queryParams.value.selectType3 === '楼层' ? queryParams.value.floorValue3 : queryParams.value.selectVal3;
  proxy?.addDateRange(queryParams.value, dateRangeBDtm.value, 'BDtm');
  proxy?.download(
    'information/scanReport/download',
    {
      ...queryParams.value
    },
    `报工扫描_报工扫描记录下载.xlsx`
  );
};

/**
 * 导出表格按钮操作
 */
const exportReport = async () => {
  queryParams.value.params = {};
  // queryParams.value.selectVal1 = queryParams.value.selectType1 === '楼层' ? queryParams.value.floorValue1 : queryParams.value.selectVal1;
  // queryParams.value.selectVal2 = queryParams.value.selectType2 === '楼层' ? queryParams.value.floorValue2 : queryParams.value.selectVal2;
  // queryParams.value.selectVal3 = queryParams.value.selectType3 === '楼层' ? queryParams.value.floorValue3 : queryParams.value.selectVal3;
  proxy?.addDateRange(queryParams.value, dateRangeBDtm.value, 'BDtm');
  proxy?.download(
    'information/scanReport/report/export',
    {
      ...queryParams.value
    },
    `报工扫描_导出表格数据_${new Date().getTime()}.xlsx`
  );
};

// 双击行处理
const handleRowDbClick = async (row: MoDetailVO) => {
  // 显示详情对话框
  moDialog.visible = true;
  moDialog.loading = true;
  moDialog.moNo = row.moNo;
  // 保存装配类型
  queryParams.value.zpType = row.zpType;
  // 重置分页
  moDialog.pageNum = 1;
  moDialog.pageSize = 10;
  await loadMoDetails();
};

// 双击第一页MO详情分页数据加载
const loadMoDetails = async () => {
  moDialog.loading = true;
  const keyName = moDialog.moNo.includes('-') ? '设备项次' : 'MO号';
  // 查询数据 - 一次性加载所有数据
  const initParm = {
    msta: queryParams.value.msta,
    zpType: queryParams.value.zpType,
    selectType1: queryParams.value.selectType1,
    selectVal1: queryParams.value.selectVal1,
    selectType2: queryParams.value.selectType2,
    selectVal2: queryParams.value.selectVal2,
    selectType3: queryParams.value.selectType3,
    selectVal3: queryParams.value.selectVal3,
    pageNum: 1,
    pageSize: 9999,
    params: {
      [keyName]: moDialog.moNo,
    }
  };
  proxy?.addDateRange(initParm, dateRangeBDtm.value, 'BDtm');
  try {
    const res = await queryScanReportAll2(initParm);
    moDialog.detailList = res.rows;
    moDialog.total = res.rows.length; // 使用实际数据长度
    moDialog.pageNum = 1; // 重置到第一页
  } catch (error) {
    proxy?.$modal.msgError('获取MO详情失败');
  } finally {
    moDialog.loading = false;
  }
};

// MO详情对话框分页处理
const handleMoDialogPagination = () => {
  // 前端分页不需要重新请求数据
  moDialog.loading = false;
};

interface ScanDialogState {
  visible: boolean;
  loading: boolean;
  detailList: ScanMoDetailVO[];
  total: number;
  pageNum: number;
  pageSize: number;
  moNo: string;
  zpType: string;
  pname: string;
}

const scanDialog = reactive<ScanDialogState>({
  visible: false,
  loading: false,
  detailList: [],
  total: 0,
  pageNum: 1,
  pageSize: 10,
  moNo: '',
  zpType: '',
  pname: ''
});

// 处理MO详情表格行双击事件
const handleMoDetailRowDbClick = async (row: any) => {
  scanDialog.visible = true;
  scanDialog.loading = true;
  scanDialog.moNo = row.moNo;
  scanDialog.zpType = row.zpType;
  scanDialog.pname = row.pname;
  scanDialog.pageNum = 1;
  scanDialog.pageSize = 10;
  await loadScanDetails();
};

// 加载扫描详情数据
const loadScanDetails = async () => {
  scanDialog.loading = true;
  const keyName = moDialog.moNo.includes('-') ? '设备项次' : 'MO号';
  // 查询数据 - 一次性加载所有数据
  const initParm = {
    msta: queryParams.value.msta,
    zpType: queryParams.value.zpType,
    selectType1: queryParams.value.selectType1,
    selectVal1: queryParams.value.selectVal1,
    selectType2: queryParams.value.selectType2,
    selectVal2: queryParams.value.selectVal2,
    selectType3: queryParams.value.selectType3,
    selectVal3: queryParams.value.selectVal3,
    pageNum: 1,
    pageSize: 9999,
    params: {
      [keyName]: scanDialog.moNo,
      '工站名称': scanDialog.pname
    }
  };
  proxy?.addDateRange(initParm, dateRangeBDtm.value, 'BDtm');

  try {
    const res = await queryScanReportAll3(initParm);
    scanDialog.detailList = res.rows;
    scanDialog.total = res.rows.length; // 使用实际数据长度
    scanDialog.pageNum = 1; // 重置到第一页
  } catch (error) {
    proxy?.$modal.msgError('获取扫描详情失败');
  } finally {
    scanDialog.loading = false;
  }
};

// 扫描详情对话框分页处理
const handleScanDialogPagination = () => {
  // 前端分页不需要重新请求数据
  scanDialog.loading = false;
};

// 添加计算属性处理分页数据
const paginatedData = computed(() => {
  const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize;
  const end = start + queryParams.value.pageSize;
  return infoReportGsList.value.slice(start, end);
});

// MO详情分页数据计算属性
const moDialogPaginatedData = computed(() => {
  const start = (moDialog.pageNum - 1) * moDialog.pageSize;
  const end = start + moDialog.pageSize;
  return moDialog.detailList.slice(start, end);
});

// 扫描详情分页数据计算属性
const scanDialogPaginatedData = computed(() => {
  const start = (scanDialog.pageNum - 1) * scanDialog.pageSize;
  const end = start + scanDialog.pageSize;
  return scanDialog.detailList.slice(start, end);
});

// 修改分页处理函数
const handlePagination = () => {
  // 前端分页不需要重新请求数据
  loading.value = false;
};

onMounted(() => {
  loading.value = false;
  loadZpData();
});
</script>

<style lang="scss" scoped>
.scan-report-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .search-section {
    margin-bottom: 20px;

    .search-card {
      .search-form {
        .search-table {
          width: 100%;
          border-collapse: collapse;
          table-layout: fixed;

          td {
            padding: 12px;
            border: 1px solid #ebeef5;
            vertical-align: middle;
            white-space: nowrap;

            &.search-label {
              width: 80px;
              text-align: center;
              font-weight: bold;
              background-color: #f5f7fa;
            }

            .cell-container {
              display: flex;
              align-items: center;
              flex-wrap: nowrap;

              span {
                margin-right: 8px;
                font-weight: 500;
                flex-shrink: 0;
                min-width: 60px;
              }

              .status-select,
              .type-select {
                min-width: 150px;
              }

              .select-type {
                min-width: 150px;
                margin-right: 8px;
                flex-shrink: 0;
              }

              .select-value {
                flex: 1;
                min-width: 200px;
              }

              .date-range-picker {
                width: 100%;
                min-width: 300px;
              }
            }
          }
        }
      }
    }
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;

      .left-buttons,
      .right-buttons {
        display: flex;
        gap: 10px;
      }
    }

    .el-table {
      margin-bottom: 20px;
    }

    .pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
      padding: 10px 0;
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-select),
:deep(.el-input),
:deep(.el-date-editor) {
  width: auto !important;
}

:deep(.el-date-editor.el-input__wrapper) {
  width: 400px !important;
}

:deep(.el-select .el-input) {
  width: 100% !important;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
  }
}

:deep(.el-dialog__body) {
  padding: 20px 30px;
}

:deep(.el-dialog__footer) {
  padding: 20px 30px;
  border-top: 1px solid #ebeef5;
}
</style>
