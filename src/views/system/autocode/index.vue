<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="规则编码" prop="ruleCode">
              <el-input v-model="queryParams.ruleCode" placeholder="请输入规则编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="queryParams.ruleName" placeholder="请输入规则名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="启用状态" prop="enableFlag">
              <el-select v-model="queryParams.enableFlag" placeholder="请选择启用状态" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:autocode:rule:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:autocode:rule:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:autocode:rule:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:autocode:rule:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="autocodeList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <!--        <el-table-column label="规则ID" align="center" prop="ruleId" v-if="true" />-->
        <el-table-column label="规则编码" align="center" prop="ruleCode">
          <template v-slot="scope">
            <router-link :to="'/system/autocode-part/index/' + scope.row.ruleId" class="link-type">
              <span>{{ scope.row.ruleCode }}</span>
            </router-link>
          </template>
        </el-table-column>
        <el-table-column label="规则名称" align="center" prop="ruleName" />
        <el-table-column label="最大长度" align="center" prop="maxLength" />
        <el-table-column label="是否补齐" align="center" prop="isPadded" />
        <el-table-column label="启用状态" align="center" prop="enableFlag">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.enableFlag" />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:autocode:rule:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:autocode:rule:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改编码生成规则对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="autocodeFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="规则编码" prop="ruleCode">
          <el-input v-model="form.ruleCode" placeholder="请输入规则编码" />
        </el-form-item>
        <el-form-item label="规则名称" prop="ruleName">
          <el-input v-model="form.ruleName" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="描述" prop="ruleDesc">
          <el-input v-model="form.ruleDesc" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="最大长度" prop="maxLength">
          <el-input v-model="form.maxLength" placeholder="请输入最大长度" />
        </el-form-item>
        <el-form-item label="是否补齐" prop="isPadded">
          <el-radio-group v-model="form.isPadded">
            <el-radio label="Y">是</el-radio>
            <el-radio label="N">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="补齐字符" prop="paddedChar">
          <el-input v-model="form.paddedChar" placeholder="请输入补齐字符" />
        </el-form-item>
        <el-form-item label="补齐方式" prop="paddedMethod" v-if="form.isPadded == 'Y'">
          <el-radio-group v-model="form.paddedMethod">
            <el-radio label="L">左补齐</el-radio>
            <el-radio label="R">右补齐</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否启用" prop="enableFlag">
          <el-select v-model="form.enableFlag" placeholder="请选择启用状态">
            <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Autocode" lang="ts">
import { addAutocode, delAutocode, getAutocode, listAutocode, updateAutocode } from '@/api/system/autocode';
import { AutocodeForm, AutocodeQuery, AutocodeVO } from '@/api/system/autocode/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no } = toRefs<any>(proxy?.useDict('sys_yes_no'));

const autocodeList = ref<AutocodeVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const autocodeFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: AutocodeForm = {
  ruleId: undefined,
  ruleCode: undefined,
  ruleName: undefined,
  ruleDesc: undefined,
  maxLength: undefined,
  isPadded: undefined,
  paddedChar: undefined,
  paddedMethod: undefined,
  enableFlag: undefined,
  remark: undefined,
  attr1: undefined,
  attr2: undefined,
  attr3: undefined,
  attr4: undefined
};
const data = reactive<PageData<AutocodeForm, AutocodeQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    ruleCode: undefined,
    ruleName: undefined,
    enableFlag: undefined,
    attr4: undefined,
    params: {}
  },
  rules: {
    ruleId: [{ required: true, message: '规则ID不能为空', trigger: 'blur' }],
    ruleCode: [{ required: true, message: '规则编码不能为空', trigger: 'blur' }],
    ruleName: [{ required: true, message: '规则名称不能为空', trigger: 'blur' }],
    enableFlag: [{ required: true, message: '启用状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询编码生成规则列表 */
const getList = async () => {
  loading.value = true;
  const res = await listAutocode(queryParams.value);
  autocodeList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  autocodeFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: AutocodeVO[]) => {
  ids.value = selection.map((item) => item.ruleId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加编码生成规则';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: AutocodeVO) => {
  reset();
  const _ruleId = row?.ruleId || ids.value[0];
  const res = await getAutocode(_ruleId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改编码生成规则';
};

/** 提交按钮 */
const submitForm = () => {
  autocodeFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.ruleId) {
        await updateAutocode(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addAutocode(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: AutocodeVO) => {
  const _ruleIds = row?.ruleId || ids.value;
  await proxy?.$modal.confirm('是否确认删除编码生成规则编号为"' + _ruleIds + '"的数据项？').finally(() => (loading.value = false));
  await delAutocode(_ruleIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/autocode/rule/export',
    {
      ...queryParams.value
    },
    `autocode_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
