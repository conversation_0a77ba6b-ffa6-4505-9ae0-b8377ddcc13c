<template>
  <div class="p-2">
    <el-row :gutter="20">
      <!-- 部门树 -->
      <el-col :lg="4" :xs="24" style="">
        <el-card shadow="hover">
          <el-input v-model="deptName" :placeholder="proxy.$t('message.system.enterDeptName')" prefix-icon="Search" clearable />
          <el-tree
            ref="deptTreeRef"
            class="mt-2"
            node-key="id"
            :data="deptOptions"
            :props="{ label: 'label', children: 'children' } as any"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
          />
        </el-card>
      </el-col>
      <el-col :lg="20" :xs="24">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
          <div v-show="showSearch" class="mb-[10px]">
            <el-card shadow="hover">
              <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                <el-form-item :label="proxy.$t('message.system.userName')" prop="userName">
                  <el-input
                    v-model="queryParams.userName"
                    :placeholder="proxy.$t('message.system.enterUserName')"
                    clearable
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item :label="proxy.$t('message.system.phonenumber')" prop="phonenumber">
                  <el-input
                    v-model="queryParams.phonenumber"
                    :placeholder="proxy.$t('message.system.enterPhonenumber')"
                    clearable
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>

                <el-form-item :label="proxy.$t('message.system.status')" prop="status">
                  <el-select v-model="queryParams.status" :placeholder="proxy.$t('message.system.userStatus')" clearable>
                    <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
                  </el-select>
                </el-form-item>
                <el-form-item :label="proxy.$t('message.system.createTime')" style="width: 308px">
                  <el-date-picker
                    v-model="dateRange"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    type="daterange"
                    range-separator="-"
                    :start-placeholder="proxy.$t('message.system.startDate')"
                    :end-placeholder="proxy.$t('message.system.endDate')"
                    :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">{{ proxy.$t('message.system.search') }}</el-button>
                  <el-button icon="Refresh" @click="resetQuery">{{ proxy.$t('message.system.refresh') }}</el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </transition>

        <el-card shadow="hover">
          <template #header>
            <el-row :gutter="10">
              <!-- 原有添加按钮 -->
              <el-col :span="1.5">
                <el-button v-has-permi="['system:user:add']" type="primary" plain icon="Plus" @click="handleAdd()">{{
                  proxy.$t('message.system.add')
                }}</el-button>
              </el-col>

              <!-- 原有编辑按钮 -->
              <el-col :span="1.5">
                <el-button v-has-permi="['system:user:edit']" type="success" plain :disabled="single" icon="Edit" @click="handleUpdate()">{{
                  proxy.$t('message.system.edit')
                }}</el-button>
              </el-col>

              <!-- 原有删除按钮 -->
              <el-col :span="1.5">
                <el-button v-has-permi="['system:user:remove']" type="danger" plain :disabled="multiple" icon="Delete" @click="handleDelete()">{{
                  proxy.$t('message.system.delete')
                }}</el-button>
              </el-col>

              <!-- 新增导出模板按钮 -->
              <el-col :span="1.5">
                <el-button v-has-permi="['system:user:exportTemplate']" type="primary" plain icon="Document" @click="importTemplate">{{
                  proxy.$t('导出模板')
                }}</el-button>
              </el-col>

              <!-- 原有导入模板按钮 -->
              <el-col :span="1.5">
                <el-button v-has-permi="['system:user:importTemplate']" type="primary" plain icon="Upload" @click="handleImport">{{
                  proxy.$t('导入模板')
                }}</el-button>
              </el-col>

              <!-- 原有更多操作下拉菜单 -->
              <el-col :span="1.5">
                <el-dropdown class="mt-[1px]">
                  <el-button plain type="info">
                    {{ proxy.$t('message.system.more') }}
                    <el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item v-if="checkPermi(['system:user:export'])" icon="Download" @click="handleExport">{{
                        proxy.$t('message.system.export')
                      }}</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </el-col>

              <right-toolbar v-model:show-search="showSearch" :columns="columns" :search="true" @query-table="getList"></right-toolbar>
            </el-row>
          </template>

          <el-table v-loading="loading" border :data="userList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column v-if="columns[0].visible" key="userId" :label="proxy.$t('message.system.id')" align="center" prop="userId" />
            <el-table-column
              v-if="columns[1].visible"
              key="userName"
              :label="proxy.$t('message.system.userName')"
              align="center"
              prop="userName"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              v-if="columns[2].visible"
              key="userCode"
              :label="proxy.$t('message.system.userCode')"
              align="center"
              prop="userCode"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              v-if="columns[3].visible"
              key="nickName"
              :label="proxy.$t('message.system.nickName')"
              align="center"
              prop="nickName"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              v-if="columns[4].visible"
              key="deptName"
              :label="proxy.$t('message.system.deptName')"
              align="center"
              prop="deptName"
              :show-overflow-tooltip="true"
            />
            <el-table-column v-if="columns[4].visible" key="postIds" label="岗位" align="center" prop="postNames" :show-overflow-tooltip="true" />
            <el-table-column
              v-if="columns[5].visible"
              key="phonenumber"
              :label="proxy.$t('message.system.phonenumber')"
              align="center"
              prop="phonenumber"
              width="120"
            />
            <el-table-column v-if="columns[6].visible" key="status" :label="proxy.$t('message.system.status')" align="center">
              <template #default="scope">
                <el-switch v-model="scope.row.status" active-value="0" inactive-value="1" @change="handleStatusChange(scope.row)" />
              </template>
            </el-table-column>
            <el-table-column v-if="columns[7].visible" :label="proxy.$t('message.system.createTime')" align="center" prop="createTime" width="160">
              <template #default="scope">
                <span>{{ scope.row.createTime }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="proxy.$t('message.system.action')" fixed="right" width="180" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-tooltip v-if="scope.row.userId !== 1" :content="proxy.$t('message.system.edit')" placement="top">
                  <el-button v-has-permi="['system:user:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
                </el-tooltip>
                <el-tooltip v-if="scope.row.userId !== 1" :content="proxy.$t('message.system.delete')" placement="top">
                  <el-button v-has-permi="['system:user:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
                </el-tooltip>
                <el-tooltip v-if="scope.row.userId !== 1" :content="proxy.$t('message.system.resetPwd')" placement="top">
                  <el-button v-has-permi="['system:user:resetPwd']" link type="primary" icon="Key" @click="handleResetPwd(scope.row)"></el-button>
                </el-tooltip>
                <el-tooltip v-if="scope.row.userId !== 1" :content="proxy.$t('message.system.authRole')" placement="top">
                  <el-button v-has-permi="['system:user:edit']" link type="primary" icon="CircleCheck" @click="handleAuthRole(scope.row)"></el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            :total="total"
            @pagination="getList"
          />
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog ref="formDialogRef" v-model="dialog.visible" :title="dialog.title" width="600px" append-to-body @close="closeDialog">
      <el-form ref="userFormRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="proxy.$t('message.system.nickName')" prop="nickName">
              <el-input v-model="form.nickName" :placeholder="proxy.$t('message.system.enterNickName')" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="proxy.$t('message.system.userCode')" prop="userCode">
              <el-input v-model="form.userCode" :placeholder="proxy.$t('message.system.enterUserCode')" maxlength="11" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-if="form.userId == null || form.userId != useUserStore().userId">
            <el-form-item :label="proxy.$t('message.system.deptName')" prop="deptId">
              <el-tree-select
                v-model="form.deptId"
                :data="enabledDeptOptions"
                :props="{ value: 'id', label: 'label', children: 'children' }"
                value-key="id"
                :placeholder="proxy.$t('message.system.selectDept')"
                check-strictly
                @change="handleDeptChange"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="proxy.$t('message.system.email')" prop="email">
              <el-input disabled v-model="form.email" :placeholder="proxy.$t('message.system.enterEmail')" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" :label="proxy.$t('message.system.userName')" prop="userName">
              <el-input v-model="form.userName" :placeholder="proxy.$t('message.system.enterUserName')" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" :label="proxy.$t('message.system.phonenumber')" prop="userName">
              <el-input v-model="form.phonenumber" :placeholder="proxy.$t('message.system.enterPhonenumber')" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="proxy.$t('message.system.sex')">
              <el-select v-model="form.sex" :placeholder="proxy.$t('message.system.select')">
                <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="proxy.$t('message.system.status')">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-if="form.userId == null || form.userId != useUserStore().userId">
            <el-form-item :label="proxy.$t('message.system.post')">
              <el-select v-model="form.postIds" multiple :placeholder="proxy.$t('message.system.select')">
                <el-option
                  v-for="item in postOptions"
                  :key="item.postId"
                  :label="item.postName"
                  :value="item.postId"
                  :disabled="item.status == '1'"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.userId == null || form.userId != useUserStore().userId">
            <el-form-item :label="proxy.$t('message.system.role')" prop="roleIds">
              <el-select v-model="form.roleIds" filterable multiple :placeholder="proxy.$t('message.system.select')">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.status == '1'"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="proxy.$t('message.system.remark')">
              <el-input v-model="form.remark" type="textarea" :placeholder="proxy.$t('message.system.enterContent')" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ proxy.$t('message.system.submit') }} </el-button>
          <el-button @click="cancel()">{{ proxy.$t('message.system.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport + '&deptId=' + upload.deptId"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <i-ep-upload-filled />
        </el-icon>
        <div class="el-upload__text">{{ proxy.$t('message.system.uploadText') }}</div>
        <template #tip>
          <div class="text-center el-upload__tip">
            <!--            <div class="el-upload__tip">-->
            <!--              <el-checkbox v-model="upload.updateSupport" />-->
            <!--              {{ proxy.$t('message.system.updateSupport') }}-->
            <!--            </div>-->
            <span>{{ proxy.$t('message.system.fileFormat') }}</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">{{
              proxy.$t('message.system.downloadTemplate')
            }}</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">{{ proxy.$t('message.system.submit') }} </el-button>
          <el-button @click="upload.open = false">{{ proxy.$t('message.system.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User" lang="ts">
import api from '@/api/system/user';
import { UserForm, UserQuery, UserVO } from '@/api/system/user/types';
import { DeptTreeVO, DeptVO } from '@/api/system/dept/types';
import { RoleVO } from '@/api/system/role/types';
import { PostVO } from '@/api/system/post/types';
import { globalHeaders } from '@/utils/request';
import { to } from 'await-to-js';
import { optionselect } from '@/api/system/post';
import { checkPermi } from '@/utils/permission';
import { useUserStore } from '@/store/modules/user';
import { ElMessageBox, UploadFile } from 'element-plus';

const router = useRouter();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_normal_disable, sys_user_sex } = toRefs<any>(proxy?.useDict('sys_normal_disable', 'sys_user_sex'));
const userList = ref<UserVO[]>();
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<number | string>>([]);
const userNames = ref<Array<string | string>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<[DateModelType, DateModelType]>(['', '']);
const deptName = ref('');
const deptOptions = ref<DeptTreeVO[]>([]);
const enabledDeptOptions = ref<DeptTreeVO[]>([]);
const initPassword = ref<string>('');
const postOptions = ref<PostVO[]>([]);
const roleOptions = ref<RoleVO[]>([]);

/*** 用户导入参数 */
const upload = reactive<ImportOption>({
  open: false,
  title: '',
  isUploading: false,
  updateSupport: 0,
  headers: globalHeaders(),
  url: import.meta.env.VITE_APP_BASE_API + '/system/user/importTemplateData',
  deptId: '' // 新增：存储当前选中的部门ID
});

// 列显隐信息
const columns = ref<FieldOption[]>([
  { key: 0, label: `ID`, visible: false, children: [] },
  { key: 1, label: `工号`, visible: true, children: [] },
  { key: 2, label: `登陆名`, visible: true, children: [] },
  { key: 3, label: `姓名`, visible: true, children: [] },
  { key: 4, label: `部门`, visible: true, children: [] },
  { key: 5, label: `手机号码`, visible: true, children: [] },
  { key: 6, label: `状态`, visible: true, children: [] },
  { key: 7, label: `创建时间`, visible: true, children: [] }
]);

const deptTreeRef = ref<ElTreeInstance>();
const queryFormRef = ref<ElFormInstance>();
const userFormRef = ref<ElFormInstance>();
const uploadRef = ref<ElUploadInstance>();
const formDialogRef = ref<ElDialogInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: UserForm = {
  userId: undefined,
  deptId: undefined,
  userName: '',
  userCode: '',
  nickName: undefined,
  password: '',
  phonenumber: undefined,
  email: undefined,
  sex: undefined,
  status: '0',
  remark: '',
  postIds: [],
  roleIds: []
};

const initData: PageData<UserForm, UserQuery> = {
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: '',
    phonenumber: '',
    status: '0',
    deptId: '',
    roleId: ''
  },
  rules: {
    userName: [
      { required: true, message: proxy.$t('message.system.userNameRequired'), trigger: 'blur' },
      { min: 2, max: 20, message: proxy.$t('message.system.userNameLength'), trigger: 'blur' }
    ],
    nickName: [{ required: true, message: proxy.$t('message.system.nickNameRequired'), trigger: 'blur' }],
    email: [{ type: 'email', message: proxy.$t('message.system.emailFormat'), trigger: ['blur', 'change'] }],
    phonenumber: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: proxy.$t('message.system.phonenumberFormat'), trigger: 'blur' }],
    roleIds: [{ required: true, message: proxy.$t('message.system.roleIdsRequired'), trigger: 'blur' }]
  }
};

const data = reactive<PageData<UserForm, UserQuery>>(initData);

const { queryParams, form, rules } = toRefs<PageData<UserForm, UserQuery>>(data);

/** 通过条件过滤节点  */
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};

/** 根据名称筛选部门树 */
watchEffect(
  () => {
    deptTreeRef.value?.filter(deptName.value);
  },
  {
    flush: 'post' // watchEffect会在DOM挂载或者更新之前就会触发，此属性控制在DOM元素更新后运行
  }
);

/** 查询用户列表 */
const getList = async () => {
  loading.value = true;
  const res = await api.listUser(proxy?.addDateRange(queryParams.value, dateRange.value));
  loading.value = false;
  userList.value = res.rows;
  total.value = res.total;
};

/** 查询部门下拉树结构 */
const getDeptTree = async () => {
  const res = await api.deptTreeSelect();
  deptOptions.value = res.data;
  enabledDeptOptions.value = filterDisabledDept(res.data);
};

/** 过滤禁用的部门 */
const filterDisabledDept = (deptList: DeptTreeVO[]) => {
  return deptList.filter((dept) => {
    if (dept.disabled) {
      return false;
    }
    if (dept.children && dept.children.length) {
      dept.children = filterDisabledDept(dept.children);
    }
    return true;
  });
};

/** 节点单击事件 */
const handleNodeClick = (data: DeptVO) => {
  queryParams.value.deptId = data.id;
  handleQuery();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRange.value = ['', ''];
  queryFormRef.value?.resetFields();
  queryParams.value.pageNum = 1;
  queryParams.value.deptId = undefined;
  deptTreeRef.value?.setCurrentKey(undefined);
  handleQuery();
};

/** 删除按钮操作 */
const handleDelete = async (row?: UserVO) => {
  const userIds = row?.userId || ids.value;
  const userNameList = row?.userName || userNames.value;
  const [err] = await to(proxy?.$modal.confirm(proxy.$t('message.system.confirmDelete', [userNameList])));
  if (!err) {
    await api.delUser(userIds);
    await getList();
    proxy?.$modal.msgSuccess(proxy.$t('message.system.success'));
  }
};

/** 用户状态修改  */
const handleStatusChange = async (row: UserVO) => {
  const text = row.status === '0' ? proxy.$t('message.system.enable') : proxy.$t('message.system.disable');
  try {
    await proxy?.$modal.confirm(proxy.$t('message.system.confirmEnableDisable', [text, row.userName]));
    await api.changeUserStatus(row.userId, row.status);
    proxy?.$modal.msgSuccess(proxy.$t('message.system.enableDisableSuccess', [text]));
  } catch (err) {
    row.status = row.status === '0' ? '1' : '0';
  }
};

/** 跳转角色分配 */
const handleAuthRole = (row: UserVO) => {
  const userId = row.userId;
  router.push('/system/user-auth/role/' + userId);
};

/** 重置密码按钮操作 */
const handleResetPwd = async (row: UserVO) => {
  const [err, res] = await to(
    ElMessageBox.prompt(proxy.$t('message.system.newPassword', [row.userName]), proxy.$t('message.system.prompt'), {
      confirmButtonText: proxy.$t('message.system.submit'),
      cancelButtonText: proxy.$t('message.system.cancel'),
      closeOnClickModal: false,
      inputPattern: /^.{5,20}$/,
      inputErrorMessage: proxy.$t('message.system.passwordLength'),
      inputValidator: (value) => {
        if (/<|>|"|'|\||\\/.test(value)) {
          return proxy.$t('message.system.passwordPattern');
        }
      }
    })
  );
  if (!err && res) {
    await api.resetUserPwd(row.userId, res.value);
    proxy?.$modal.msgSuccess(proxy.$t('message.system.resetSuccess', [res.value]));
  }
};

/** 选择条数  */
const handleSelectionChange = (selection: UserVO[]) => {
  ids.value = selection.map((item) => item.userId);
  userNames.value = selection.map((item) => item.userName);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 导入按钮操作 */
const handleImport = () => {
  // 校验是否已选择部门
  if (!queryParams.value.deptId) {
    proxy?.$modal.msgWarning(proxy.$t('请先选择部门'));
    return;
  }
  // 同步当前选中的部门ID到上传参数
  upload.deptId = queryParams.value.deptId;
  upload.title = proxy.$t('message.system.import');
  upload.open = true;
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/user/export',
    {
      ...queryParams.value
    },
    `user_${new Date().getTime()}.xlsx`
  );
};

/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('system/user/importTemplate', {}, `user_template_${new Date().getTime()}.xlsx`);
};

/** 文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.handleRemove(file);
  ElMessageBox.alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>',
    proxy.$t('message.system.importResult'),
    {
      dangerouslyUseHTMLString: true
    }
  );
  getList();
};

/** 提交上传文件 */
const submitFileForm = () => {
  uploadRef.value?.submit();
};

/** 重置操作表单 */
const reset = () => {
  userFormRef.value?.resetFields();
};

/** 取消按钮 */
const cancel = () => {
  dialog.visible = false;
  reset();
};

/** 新增按钮操作 */
const handleAdd = async () => {
  reset();
  const { data } = await api.getUser();
  dialog.visible = true;
  dialog.title = proxy.$t('message.system.add');
  postOptions.value = data.posts;
  roleOptions.value = data.roles;
  form.value.password = initPassword.value.toString();
};

/** 修改按钮操作 */
const handleUpdate = async (row?: UserForm) => {
  reset();
  const userId = row?.userId || ids.value[0];
  const { data } = await api.getUser(userId);
  dialog.visible = true;
  dialog.title = proxy.$t('message.system.edit');
  Object.assign(form.value, data.user);
  postOptions.value = data.posts;
  roleOptions.value = data.roles;
  roleOptions.value = Array.from(
    new Map([...data.roles, ...data.user.roles].map(role => [role.roleId, role])).values()
  );
  form.value.postIds = data.postIds;
  form.value.roleIds = data.roleIds;
  form.value.password = '';
};

/** 提交按钮 */
const submitForm = () => {
  userFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      form.value.userId ? await api.updateUser(form.value) : await api.addUser(form.value);
      proxy?.$modal.msgSuccess(proxy.$t('message.system.success'));
      dialog.visible = false;
      await getList();
    }
  });
};

/**
 * 关闭用户弹窗
 */
const closeDialog = () => {
  dialog.visible = false;
  resetForm();
};

/**
 * 重置表单
 */
const resetForm = () => {
  userFormRef.value?.resetFields();
  userFormRef.value?.clearValidate();

  form.value.userId = undefined;
  form.value.status = '1';
};

onMounted(() => {
  getDeptTree(); // 初始化部门数据
  getList(); // 初始化列表数据
  proxy?.getConfigKey('sys.user.initPassword').then((response) => {
    initPassword.value = response.data;
  });
});

async function handleDeptChange(value: number | string) {
  const response = await optionselect(value);
  postOptions.value = response.data;
  form.value.postIds = [];
}
</script>

<style lang="scss" scoped>
/* 你的样式代码 */
</style>
