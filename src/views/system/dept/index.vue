<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item :label="proxy.$t('system.dept.deptName')" prop="deptName">
              <el-input
                id="deptName"
                v-model="queryParams.deptName"
                :placeholder="proxy.$t('system.dept.enterDeptName')"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item :label="proxy.$t('system.dept.deptCategory')" prop="deptCategory">
              <el-input
                id="deptCategory"
                v-model="queryParams.deptCategory"
                :placeholder="proxy.$t('system.dept.enterDeptCategory')"
                clearable
                style="width: 240px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item :label="proxy.$t('system.dept.status')" prop="status">
              <el-select id="status" v-model="queryParams.status" :placeholder="proxy.$t('system.dept.departmentStatus')" clearable>
                <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">{{ proxy.$t('system.dept.search') }}</el-button>
              <el-button icon="Refresh" @click="resetQuery">{{ proxy.$t('system.dept.reset') }}</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="hover">
      <template #header>
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:dept:add']" type="primary" plain icon="Plus" @click="handleAdd()">
              {{ proxy.$t('system.dept.add') }}
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="handleToggleExpandAll">
              {{ proxy.$t('system.dept.expandCollapse') }}
            </el-button>
          </el-col>
          <right-toolbar v-model:show-search="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table
        ref="deptTableRef"
        v-loading="loading"
        :data="deptList"
        row-key="deptId"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="isExpandAll"
      >
        <el-table-column prop="deptName" :label="proxy.$t('system.dept.deptName')" width="260"></el-table-column>
        <el-table-column prop="deptCategory" align="center" :label="proxy.$t('system.dept.deptCategory')" width="200"></el-table-column>
        <el-table-column prop="orderNum" align="center" :label="proxy.$t('system.dept.order')" width="200"></el-table-column>
        <el-table-column prop="status" align="center" :label="proxy.$t('system.dept.status')" width="100">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column :label="proxy.$t('system.dept.createTime')" align="center" prop="createTime" width="200">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" :label="proxy.$t('system.dept.operation')">
          <template #default="scope">
            <el-tooltip :content="proxy.$t('system.dept.edit')" placement="top">
              <el-button v-hasPermi="['system:dept:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)" />
            </el-tooltip>
            <el-tooltip :content="proxy.$t('system.dept.add')" placement="top">
              <el-button v-hasPermi="['system:dept:add']" link type="primary" icon="Plus" @click="handleAdd(scope.row)" />
            </el-tooltip>
            <el-tooltip :content="proxy.$t('system.dept.delete')" placement="top">
              <el-button v-hasPermi="['system:dept:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)" />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-dialog v-model="dialog.visible" :title="dialog.title" destroy-on-close append-to-body width="600px">
      <el-form ref="deptFormRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col v-if="form.parentId !== 0" :span="24">
            <el-form-item :label="proxy.$t('system.dept.parentDept')" prop="parentId">
              <el-tree-select
                id="parentId"
                v-model="form.parentId"
                :data="deptOptions"
                :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
                value-key="deptId"
                :placeholder="proxy.$t('system.dept.selectParentDept')"
                check-strictly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="proxy.$t('system.dept.deptName')" prop="deptName">
              <el-input id="formDeptName" v-model="form.deptName" :placeholder="proxy.$t('system.dept.enterDeptName')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="proxy.$t('system.dept.deptCategory')" prop="deptCategory">
              <el-input id="formDeptCategory" v-model="form.deptCategory" :placeholder="proxy.$t('system.dept.enterDeptCategory')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="proxy.$t('system.dept.order')" prop="orderNum">
              <el-input-number id="orderNum" v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="proxy.$t('system.dept.leader')" prop="leader">
              <el-select id="leader" v-model="form.leader" :placeholder="proxy.$t('system.dept.selectLeader')">
                <el-option v-for="item in deptUserList" :key="item.userId" :label="item.userName" :value="item.userId" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="proxy.$t('system.dept.phone')" prop="phone">
              <el-input id="phone" v-model="form.phone" :placeholder="proxy.$t('system.dept.enterPhone')" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="proxy.$t('system.dept.email')" prop="email">
              <el-input id="email" v-model="form.email" :placeholder="proxy.$t('system.dept.enterEmail')" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="proxy.$t('system.dept.deptStatus')">
              <el-radio-group id="deptStatus" v-model="form.status">
                <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ proxy.$t('system.dept.submit') }}</el-button>
          <el-button @click="cancel">{{ proxy.$t('system.dept.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Dept" lang="ts">
import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
import { addDept, delDept, getDept, listDept, listDeptExcludeChild, updateDept } from '@/api/system/dept';
import { DeptForm, DeptQuery, DeptVO } from '@/api/system/dept/types';
import { UserVO } from '@/api/system/user/types';
import { listUserByDeptId } from '@/api/system/user';

interface DeptOptionsType {
  deptId: number | string;
  deptName: string;
  children: DeptOptionsType[];
}

interface DialogOption {
  visible: boolean;
  title: string;
}

interface PageData<T, U> {
  form: T;
  queryParams: U;
  rules: any;
}

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_normal_disable } = toRefs<any>(proxy?.useDict('sys_normal_disable'));

const deptList = ref<DeptVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const deptOptions = ref<DeptOptionsType[]>([]);
const isExpandAll = ref(true);
const deptUserList = ref<UserVO[]>([]);

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const deptTableRef = ref<ElTableInstance>();
const queryFormRef = ref<ElFormInstance>();
const deptFormRef = ref<ElFormInstance>();

const initFormData: DeptForm = {
  deptId: undefined,
  parentId: undefined,
  deptName: undefined,
  deptCategory: undefined,
  orderNum: 0,
  leader: undefined,
  phone: undefined,
  email: undefined,
  status: '0'
};

const initData: PageData<DeptForm, DeptQuery> = {
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    deptName: undefined,
    deptCategory: undefined,
    status: undefined
  },
  rules: {
    parentId: [{ required: true, message: proxy.$t('system.dept.parentDeptRequired'), trigger: 'blur' }],
    deptName: [{ required: true, message: proxy.$t('system.dept.deptNameRequired'), trigger: 'blur' }],
    orderNum: [{ required: true, message: proxy.$t('system.dept.orderRequired'), trigger: 'blur' }],
    email: [{ type: 'email', message: proxy.$t('system.dept.emailInvalid'), trigger: ['blur', 'change'] }],
    phone: [{ pattern: /^1[3-9]\d{9}$/, message: proxy.$t('system.dept.phoneInvalid'), trigger: 'blur' }]
  }
};

const data = reactive<PageData<DeptForm, DeptQuery>>(initData);

const { queryParams, form, rules } = toRefs<PageData<DeptForm, DeptQuery>>(data);

/** 查询菜单列表 */
const getList = async () => {
  loading.value = true;
  try {
    const res = await listDept(queryParams.value);
    const data = proxy?.handleTree<DeptVO>(res.data, 'deptId');
    if (data) {
      deptList.value = data;
    }
  } catch (error) {
    console.error('Error fetching department list:', error);
    proxy?.$modal.msgError(proxy.$t('system.dept.fetchDeptListError'));
  } finally {
    loading.value = false;
  }
};

/** 查询当前部门的所有用户 */
async function getDeptAllUser(deptId: any) {
  if (deptId !== null && deptId !== '' && deptId !== undefined) {
    try {
      const res = await listUserByDeptId(deptId);
      deptUserList.value = res.data;
    } catch (error) {
      console.error('Error fetching users for department:', error);
      proxy?.$modal.msgError(proxy.$t('system.dept.fetchUsersError'));
    }
  }
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};
/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  deptFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 展开/折叠操作 */
const handleToggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
  toggleExpandAll(deptList.value, isExpandAll.value);
};
/** 展开/折叠所有 */
const toggleExpandAll = (data: DeptVO[], status: boolean) => {
  data.forEach((item) => {
    deptTableRef.value?.toggleRowExpansion(item, status);
    if (item.children && item.children.length > 0) toggleExpandAll(item.children, status);
  });
};

/** 新增按钮操作 */
const handleAdd = async (row?: DeptVO) => {
  reset();
  try {
    const res = await listDept();
    const data = proxy?.handleTree<DeptOptionsType>(res.data, 'deptId');
    if (data) {
      deptOptions.value = data;
      if (row && row.deptId) {
        form.value.parentId = row?.deptId;
      }
      dialog.visible = true;
      dialog.title = proxy.$t('system.dept.addDept');
    }
  } catch (error) {
    console.error('Error fetching department options for add:', error);
    proxy?.$modal.msgError(proxy.$t('system.dept.fetchDeptOptionsError'));
  }
};

/** 修改按钮操作 */
const handleUpdate = async (row: DeptVO) => {
  reset();
  // 查询当前部门所有用户
  getDeptAllUser(row.deptId);
  try {
    const res = await getDept(row.deptId);
    form.value = res.data;
    const response = await listDeptExcludeChild(row.deptId);
    const data = proxy?.handleTree<DeptOptionsType>(response.data, 'deptId');
    if (data) {
      deptOptions.value = data;
      if (data.length === 0) {
        const noResultsOptions: DeptOptionsType = {
          deptId: res.data.parentId,
          deptName: res.data.parentName,
          children: []
        };
        deptOptions.value.push(noResultsOptions);
      }
    }
    dialog.visible = true;
    dialog.title = proxy.$t('system.dept.editDept');
  } catch (error) {
    console.error('Error fetching department data for update:', error);
    proxy?.$modal.msgError(proxy.$t('system.dept.fetchDeptDataError'));
  }
};

/** 提交按钮 */
const submitForm = () => {
  deptFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        form.value.deptId ? await updateDept(form.value) : await addDept(form.value);
        proxy?.$modal.msgSuccess(proxy.$t('system.dept.operationSuccess'));
      } catch (error) {
        console.error('Error submitting form:', error);
        proxy?.$modal.msgError(proxy.$t('system.dept.submitFormError'));
      } finally {
        dialog.visible = false;
        await getList();
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row: DeptVO) => {
  try {
    await proxy?.$modal.confirm(proxy.$t('system.dept.confirmDelete', [row.deptName]));
    await delDept(row.deptId);
    proxy?.$modal.msgSuccess(proxy.$t('system.dept.deleteSuccess'));
  } catch (error) {
    console.error('Error deleting department:', error);
    proxy?.$modal.msgError(proxy.$t('system.dept.deleteFailed'));
  } finally {
    await getList();
  }
};

onMounted(() => {
  getList();
});
</script>
