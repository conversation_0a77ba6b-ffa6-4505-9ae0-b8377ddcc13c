<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目来源" prop="projectSource">
              <el-input v-model="queryParams.projectSource" placeholder="请输入项目来源" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="计划开始日期" prop="startDate">
              <el-date-picker clearable v-model="queryParams.startDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择计划开始日期" />
            </el-form-item>
            <el-form-item label="计划完成日期" prop="endDate">
              <el-date-picker clearable v-model="queryParams.endDate" type="date" value-format="YYYY-MM-DD" placeholder="请选择计划完成日期" />
            </el-form-item>
            <el-form-item label="项目计划人" prop="projectPlanner">
              <el-input v-model="queryParams.projectPlanner" placeholder="请输入项目计划人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目经理" prop="projectManager">
              <el-input v-model="queryParams.projectManager" placeholder="请输入项目经理" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="负责内部机构" prop="internalOrganization">
              <el-input v-model="queryParams.internalOrganization" placeholder="请输入负责内部机构" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户代码" prop="customerCode">
              <el-input v-model="queryParams.customerCode" placeholder="请输入客户代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目阶段" prop="projectPhase">
              <el-input v-model="queryParams.projectPhase" placeholder="请输入项目阶段" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目等级" prop="projectLevel">
              <el-input v-model="queryParams.projectLevel" placeholder="请输入项目等级" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目类别" prop="projectCategory">
              <el-input v-model="queryParams.projectCategory" placeholder="请输入项目类别" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="数量" prop="quantity">
              <el-input v-model="queryParams.quantity" placeholder="请输入数量" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工厂" prop="factory">
              <el-input v-model="queryParams.factory" placeholder="请输入工厂" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="系统立项时间" prop="systemInitiationDate">
              <el-date-picker
                clearable
                v-model="queryParams.systemInitiationDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择系统立项时间"
              />
            </el-form-item>
            <el-form-item label="项目成本红线" prop="projectCostLimit">
              <el-input v-model="queryParams.projectCostLimit" placeholder="请输入项目成本红线" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目售后成本红线" prop="afterSalesCostLimit">
              <el-input v-model="queryParams.afterSalesCostLimit" placeholder="请输入项目售后成本红线" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目成本超红线原因" prop="costOverrunReason">
              <el-input v-model="queryParams.costOverrunReason" placeholder="请输入项目成本超红线原因" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="区域" prop="region">
              <el-input v-model="queryParams.region" placeholder="请输入区域" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
                <el-option v-for="dict in project_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:info:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['system:info:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['system:info:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:info:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="infoList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="项目ID" align="center" prop="projectId" v-if="true" />
        <el-table-column label="项目类型" align="center" prop="projectType" />
        <el-table-column label="名称" align="center" prop="name" />
        <el-table-column label="项目来源" align="center" prop="projectSource" />
        <el-table-column label="计划开始日期" align="center" prop="startDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="计划完成日期" align="center" prop="endDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目计划人" align="center" prop="projectPlanner" />
        <el-table-column label="项目经理" align="center" prop="projectManager" />
        <el-table-column label="负责内部机构" align="center" prop="internalOrganization" />
        <el-table-column label="客户代码" align="center" prop="customerCode" />
        <el-table-column label="项目阶段" align="center" prop="projectPhase" />
        <el-table-column label="项目等级" align="center" prop="projectLevel" />
        <el-table-column label="项目类别" align="center" prop="projectCategory" />
        <el-table-column label="数量" align="center" prop="quantity" />
        <el-table-column label="工厂" align="center" prop="factory" />
        <el-table-column label="行业类型" align="center" prop="industryType" />
        <el-table-column label="产品类型" align="center" prop="productType" />
        <el-table-column label="系统立项时间" align="center" prop="systemInitiationDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.systemInitiationDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="项目成本红线" align="center" prop="projectCostLimit" />
        <el-table-column label="项目售后成本红线" align="center" prop="afterSalesCostLimit" />
        <el-table-column label="项目成本超红线原因" align="center" prop="costOverrunReason" />
        <el-table-column label="区域" align="center" prop="region" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="project_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:info:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:info:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改项目信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="infoFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="项目来源" prop="projectSource">
          <el-input v-model="form.projectSource" placeholder="请输入项目来源" />
        </el-form-item>
        <el-form-item label="计划开始日期" prop="startDate">
          <el-date-picker clearable v-model="form.startDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择计划开始日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划完成日期" prop="endDate">
          <el-date-picker clearable v-model="form.endDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择计划完成日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="项目计划人" prop="projectPlanner">
          <el-input v-model="form.projectPlanner" placeholder="请输入项目计划人" />
        </el-form-item>
        <el-form-item label="项目经理" prop="projectManager">
          <el-input v-model="form.projectManager" placeholder="请输入项目经理" />
        </el-form-item>
        <el-form-item label="负责内部机构" prop="internalOrganization">
          <el-input v-model="form.internalOrganization" placeholder="请输入负责内部机构" />
        </el-form-item>
        <el-form-item label="客户代码" prop="customerCode">
          <el-input v-model="form.customerCode" placeholder="请输入客户代码" />
        </el-form-item>
        <el-form-item label="项目阶段" prop="projectPhase">
          <el-input v-model="form.projectPhase" placeholder="请输入项目阶段" />
        </el-form-item>
        <el-form-item label="项目等级" prop="projectLevel">
          <el-input v-model="form.projectLevel" placeholder="请输入项目等级" />
        </el-form-item>
        <el-form-item label="项目类别" prop="projectCategory">
          <el-input v-model="form.projectCategory" placeholder="请输入项目类别" />
        </el-form-item>
        <el-form-item label="数量" prop="quantity">
          <el-input v-model="form.quantity" placeholder="请输入数量" />
        </el-form-item>
        <el-form-item label="工厂" prop="factory">
          <el-input v-model="form.factory" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="系统立项时间" prop="systemInitiationDate">
          <el-date-picker
            clearable
            v-model="form.systemInitiationDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择系统立项时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="项目成本红线" prop="projectCostLimit">
          <el-input v-model="form.projectCostLimit" placeholder="请输入项目成本红线" />
        </el-form-item>
        <el-form-item label="项目售后成本红线" prop="afterSalesCostLimit">
          <el-input v-model="form.afterSalesCostLimit" placeholder="请输入项目售后成本红线" />
        </el-form-item>
        <el-form-item label="项目成本超红线原因" prop="costOverrunReason">
          <el-input v-model="form.costOverrunReason" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="区域" prop="region">
          <el-input v-model="form.region" placeholder="请输入区域" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio v-for="dict in project_status" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Info" lang="ts">
import { addInfo, delInfo, getInfo, listInfo, updateInfo } from '@/api/system/info';
import { InfoForm, InfoQuery, InfoVO } from '@/api/system/info/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { project_status } = toRefs<any>(proxy?.useDict('project_status'));

const infoList = ref<InfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const infoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: InfoForm = {
  projectId: undefined,
  projectType: undefined,
  name: undefined,
  projectSource: undefined,
  startDate: undefined,
  endDate: undefined,
  projectPlanner: undefined,
  projectManager: undefined,
  internalOrganization: undefined,
  customerCode: undefined,
  projectPhase: undefined,
  projectLevel: undefined,
  projectCategory: undefined,
  quantity: undefined,
  factory: undefined,
  industryType: undefined,
  productType: undefined,
  systemInitiationDate: undefined,
  projectCostLimit: undefined,
  afterSalesCostLimit: undefined,
  costOverrunReason: undefined,
  region: undefined,
  status: undefined
};
const data = reactive<PageData<InfoForm, InfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectType: undefined,
    name: undefined,
    projectSource: undefined,
    startDate: undefined,
    endDate: undefined,
    projectPlanner: undefined,
    projectManager: undefined,
    internalOrganization: undefined,
    customerCode: undefined,
    projectPhase: undefined,
    projectLevel: undefined,
    projectCategory: undefined,
    quantity: undefined,
    factory: undefined,
    industryType: undefined,
    productType: undefined,
    systemInitiationDate: undefined,
    projectCostLimit: undefined,
    afterSalesCostLimit: undefined,
    costOverrunReason: undefined,
    region: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    projectId: [{ required: true, message: '项目ID不能为空', trigger: 'blur' }],
    projectType: [{ required: true, message: '项目类型不能为空', trigger: 'change' }],
    name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
    projectSource: [{ required: true, message: '项目来源不能为空', trigger: 'blur' }],
    startDate: [{ required: true, message: '计划开始日期不能为空', trigger: 'blur' }],
    endDate: [{ required: true, message: '计划完成日期不能为空', trigger: 'blur' }],
    projectPlanner: [{ required: true, message: '项目计划人不能为空', trigger: 'blur' }],
    projectManager: [{ required: true, message: '项目经理不能为空', trigger: 'blur' }],
    internalOrganization: [{ required: true, message: '负责内部机构不能为空', trigger: 'blur' }],
    customerCode: [{ required: true, message: '客户代码不能为空', trigger: 'blur' }],
    projectPhase: [{ required: true, message: '项目阶段不能为空', trigger: 'blur' }],
    projectLevel: [{ required: true, message: '项目等级不能为空', trigger: 'blur' }],
    projectCategory: [{ required: true, message: '项目类别不能为空', trigger: 'blur' }],
    quantity: [{ required: true, message: '数量不能为空', trigger: 'blur' }],
    factory: [{ required: true, message: '工厂不能为空', trigger: 'blur' }],
    industryType: [{ required: true, message: '行业类型不能为空', trigger: 'change' }],
    productType: [{ required: true, message: '产品类型不能为空', trigger: 'change' }],
    systemInitiationDate: [{ required: true, message: '系统立项时间不能为空', trigger: 'blur' }],
    projectCostLimit: [{ required: true, message: '项目成本红线不能为空', trigger: 'blur' }],
    afterSalesCostLimit: [{ required: true, message: '项目售后成本红线不能为空', trigger: 'blur' }],
    costOverrunReason: [{ required: true, message: '项目成本超红线原因不能为空', trigger: 'blur' }],
    region: [{ required: true, message: '区域不能为空', trigger: 'blur' }],
    status: [{ required: true, message: '状态不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询项目信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listInfo(queryParams.value);
  infoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  infoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: InfoVO[]) => {
  ids.value = selection.map((item) => item.projectId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加项目信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: InfoVO) => {
  reset();
  const _projectId = row?.projectId || ids.value[0];
  const res = await getInfo(_projectId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改项目信息';
};

/** 提交按钮 */
const submitForm = () => {
  infoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.projectId) {
        await updateInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: InfoVO) => {
  const _projectIds = row?.projectId || ids.value;
  await proxy?.$modal.confirm('是否确认删除项目信息编号为"' + _projectIds + '"的数据项？').finally(() => (loading.value = false));
  await delInfo(_projectIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'system/info/export',
    {
      ...queryParams.value
    },
    `info_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
