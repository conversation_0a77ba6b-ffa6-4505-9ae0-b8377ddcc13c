<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="代号" prop="code">
              <el-input v-model="queryParams.code" placeholder="请输入代号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="说明" prop="description">
              <el-input v-model="queryParams.description" placeholder="请输入说明" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mes:workcenter:add']">新增</el-button>
          </el-col>

          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mes:workcenter:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mes:workcenter:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="primary"
              :disabled="refreshLoading"
              :loading="refreshLoading"
              plain
              icon="Refresh"
              @click="syncUpdateSap"
              v-hasPermi="['mes:workcenter:refresh']"
              >同步SAP</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mes:workcenter:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table
        v-loading="loading"
        :data="workcenterList"
        :default-sort="defaultSort"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="代号" align="center" prop="code" />
        <el-table-column label="说明" align="center" prop="description" width="230" :show-overflow-tooltip="true" />
        <el-table-column label="类型" align="center" prop="type" />
        <!--        <el-table-column label="公司代码" align="center" prop="companyCode" />-->
        <el-table-column label="工厂代码" align="center" prop="factoryCode" />
        <el-table-column label="人工费用" align="center" prop="laborCost" />
        <el-table-column label="制造费用" align="center" prop="manufacturingCost" />
        <el-table-column label="是否委外" align="center" prop="isOutsourced" />
        <!--        <el-table-column label="单价" align="center" prop="unitPrice" />-->
        <el-table-column label="部门" align="center" prop="completionDepartment" />
        <el-table-column label="备注信息" align="center" prop="remarks" />
        <el-table-column label="同步时间" align="center" prop="createTime" width="180" sortable="custom" :sort-orders="['descending', 'ascending']">
          <template #default="scope">
            <span>{{ proxy.parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mes:workcenter:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mes:workcenter:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改工作中心对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="workcenterFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="代号" prop="code">
          <el-input v-model="form.code" placeholder="请输入代号" />
        </el-form-item>
        <el-form-item label="说明" prop="description">
          <el-input v-model="form.description" placeholder="请输入说明" />
        </el-form-item>
        <el-form-item label="公司代码" prop="companyCode">
          <el-input v-model="form.companyCode" placeholder="请输入公司代码" />
        </el-form-item>
        <el-form-item label="工厂代码" prop="factoryCode">
          <el-input v-model="form.factoryCode" placeholder="请输入工厂代码" />
        </el-form-item>
        <el-form-item label="人工费用" prop="laborCost">
          <el-input v-model="form.laborCost" placeholder="请输入人工费用" />
        </el-form-item>
        <el-form-item label="制造费用" prop="manufacturingCost">
          <el-input v-model="form.manufacturingCost" placeholder="请输入制造费用" />
        </el-form-item>
        <el-form-item label="是否委外" prop="isOutsourced">
          <el-input v-model="form.isOutsourced" placeholder="请输入是否委外" />
        </el-form-item>
        <el-form-item label="单价" prop="unitPrice">
          <el-input v-model="form.unitPrice" placeholder="请输入单价" />
        </el-form-item>
        <el-form-item label="部门" prop="completionDepartment">
          <el-input v-model="form.completionDepartment" placeholder="请输入部门" />
        </el-form-item>
        <el-form-item label="备注信息" prop="remarks">
          <el-input v-model="form.remarks" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Workcenter" lang="ts">
import { addWorkcenter, delWorkcenter, getWorkcenter, listWorkcenter, syncSapToWorkCenter, updateWorkcenter } from '@/api/mes/workcenter';
import { WorkcenterForm, WorkcenterQuery, WorkcenterVO } from '@/api/mes/workcenter/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const workcenterList = ref<WorkcenterVO[]>([]);
const buttonLoading = ref(false);
const refreshLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const defaultSort = ref<any>({ prop: 'createTime', order: 'descending' });

const queryFormRef = ref<ElFormInstance>();
const workcenterFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: WorkcenterForm = {
  id: undefined,
  code: undefined,
  description: undefined,
  type: undefined,
  companyCode: undefined,
  factoryCode: undefined,
  laborCost: undefined,
  manufacturingCost: undefined,
  isOutsourced: undefined,
  unitPrice: undefined,
  completionDepartment: undefined,
  remarks: undefined
};
const data = reactive<PageData<WorkcenterForm, WorkcenterQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    code: undefined,
    description: undefined,
    orderByColumn: defaultSort.value.prop,
    isAsc: defaultSort.value.order,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键ID不能为空', trigger: 'blur' }],
    code: [{ required: true, message: '代号不能为空', trigger: 'blur' }],
    description: [{ required: true, message: '说明不能为空', trigger: 'blur' }],
    type: [{ required: true, message: '类型不能为空', trigger: 'change' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询工作中心列表 */
const getList = async () => {
  loading.value = true;
  const res = await listWorkcenter(queryParams.value);
  workcenterList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  workcenterFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};
/** 排序触发事件 */
const handleSortChange = (column: any) => {
  queryParams.value.orderByColumn = column.prop;
  queryParams.value.isAsc = column.order;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
  queryFormRef.value?.sort(defaultSort.value.prop, defaultSort.value.order);
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WorkcenterVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加工作中心';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: WorkcenterVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getWorkcenter(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改工作中心';
};

/** 提交按钮 */
const submitForm = () => {
  workcenterFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateWorkcenter(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addWorkcenter(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: WorkcenterVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除工作中心编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delWorkcenter(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 同步sap按钮操作 */
const syncUpdateSap = async () => {
  refreshLoading.value = true;
  await syncSapToWorkCenter().finally(() => (refreshLoading.value = false));
  proxy?.$modal.msgSuccess('同步成功');
  getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mes/workcenter/export',
    {
      ...queryParams.value
    },
    `workcenter_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
