<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover" class="search-card">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form">
            <el-form-item label="MO" prop="mo">
              <el-input v-model="queryParams.mo" placeholder="请输入MO" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申报品名" prop="declarationName">
              <el-input v-model="queryParams.declarationName" placeholder="请输入申报品名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="HS.CODE" prop="hsCode">
              <el-input v-model="queryParams.hsCode" placeholder="请输入HS CODE" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申报要素" prop="declarationElements">
              <el-input v-model="queryParams.declarationElements" placeholder="请输入申报要素" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="料号" prop="materialNumber">
              <el-input v-model="queryParams.materialNumber" placeholder="请输入料号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="零件号" prop="partNumber">
              <el-input v-model="queryParams.partNumber" placeholder="请输入零件号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="中文品名" prop="chineseName">
              <el-input v-model="queryParams.chineseName" placeholder="请输入中文品名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="品牌" prop="brand">
              <el-input v-model="queryParams.brand" placeholder="请输入品牌" clearable @keyup.enter="handleQuery" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery" class="search-btn"> 搜索 </el-button>
              <el-button icon="Refresh" @click="resetQuery" class="reset-btn"> 重置 </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never" class="main-card">
      <template #header>
        <div class="card-header">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="success"
                :disabled="ids.length === 0"
                @click="handleBatchArchive()"
                v-hasPermi="['mes:declaration:archive']"
                class="action-btn"
              >
                <el-icon><Check /></el-icon>
                归档
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                :disabled="ids.length === 0"
                @click="openBatchRejectDialog()"
                v-hasPermi="['mes:declaration:reject']"
                class="action-btn"
              >
                <el-icon><Close /></el-icon>
                退回
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="primary"
                :disabled="single"
                @click="openEditDialog(declarationList.find((item) => item.id === ids[0]))"
                v-hasPermi="['mes:declaration:edit']"
                class="action-btn"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>
        </div>
      </template>

      <div class="table-wrapper">
        <el-table
          v-loading="loading"
          border
          :data="declarationList"
          @selection-change="handleSelectionChange"
          class="approval-table"
          :height="tableHeight"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="主键ID" align="center" prop="id" v-if="false" />
          <el-table-column label="MO" align="center" prop="mo" min-width="120" show-overflow-tooltip />
          <el-table-column label="单台净重" align="center" prop="netWeight" />
          <el-table-column label="货物图片" min-width="120" align="center" prop="goodsPicture">
            <template #default="scope">
              <ImagePreview
                v-if="previewListResource && checkFileSuffix('.jpg')"
                :width="100"
                :height="100"
                :src="scope.row.goodsPicture"
                :preview-src-list="[scope.row.goodsPicture]"
              />
              <span v-if="!checkFileSuffix('.jpg') || !previewListResource" v-text="scope.row.goodsPicture" />
            </template>
          </el-table-column>
          <el-table-column label="型号图片" min-width="120" align="center" prop="modelPicture">
            <template #default="scope">
              <ImagePreview
                v-if="previewListResource && checkFileSuffix('.jpg')"
                :width="100"
                :height="100"
                :src="scope.row.modelPicture"
                :preview-src-list="[scope.row.modelPicture]"
              />
              <span v-if="!checkFileSuffix('.jpg') || !previewListResource" v-text="scope.row.modelPicture" />
            </template>
          </el-table-column>
          <el-table-column label="标签/名牌" min-width="120" align="center" prop="nameplatePicture">
            <template #default="scope">
              <ImagePreview
                v-if="previewListResource && checkFileSuffix('.jpg')"
                :width="100"
                :height="100"
                :src="scope.row.nameplatePicture"
                :preview-src-list="[scope.row.nameplatePicture]"
              />
              <span v-if="!checkFileSuffix('.jpg') || !previewListResource" v-text="scope.row.nameplatePicture" />
            </template>
          </el-table-column>

          <el-table-column label="料号" align="center" prop="materialNumber" min-width="120" show-overflow-tooltip />
          <el-table-column label="零件号" align="center" prop="partNumber" />
          <el-table-column label="中文品名" align="center" prop="chineseName" />
          <el-table-column label="英文品名" align="center" prop="englishName" />
          <el-table-column label="生产原厂" align="center" prop="manufacturer" />
          <el-table-column label="品牌" align="center" prop="brand" />
          <el-table-column label="型号" align="center" prop="model" />
          <el-table-column label="序列 " align="center" prop="serialNumber" />
          <el-table-column label="设备情况 " align="center" prop="equipmentCondition" />
          <el-table-column label="原产地" align="center" prop="origin" />
          <el-table-column label="生产/购买年月" align="center" prop="productionDate" />
          <el-table-column label="数量" align="center" prop="quantity" />
          <el-table-column label="单位" align="center" prop="unit" />
          <el-table-column label="单价" align="center" prop="unitPrice" />
          <el-table-column prop="totalAmount" label="总价">
            <template #default="{ row }">
              {{ row.quantity != null && row.unitPrice != null ? (row.unitPrice * row.quantity).toFixed(2) : '' }}
            </template>
          </el-table-column>
          <el-table-column label="材质(非设备) " align="center" prop="material" min-width="120" show-overflow-tooltip />
          <el-table-column label="尺寸" align="center" prop="dimension" min-width="120" show-overflow-tooltip />
          <el-table-column label="工作原理" align="center" prop="workingPrinciple" />
          <el-table-column label="用途" align="center" prop="functionEn" min-width="120" show-overflow-tooltip />
          <el-table-column label="功能" align="center" prop="functionality" min-width="120" show-overflow-tooltip />
          <el-table-column label="功率" align="center" prop="power" />
          <el-table-column label="电压" align="center" prop="voltage" />
          <el-table-column label="加工方法" align="center" prop="processingMethod" />
          <el-table-column label="是否有接头 " align="center" prop="hasConnector" />
          <el-table-column label="结构类型" align="center" prop="structureType" />
          <el-table-column label="总净重/KG" align="center" prop="totalNetWeight" />
          <el-table-column label="胶管类的需确认以下5项" align="center" prop="hydraulicHose" />
          <el-table-column label="橡胶类的需确认以下5项" align="center" prop="rubberMaterial" />
          <el-table-column label="申报品名" align="center" prop="declarationName" />
          <el-table-column label="HS CODE" align="center" prop="hsCode" min-width="120" show-overflow-tooltip />
          <el-table-column label="申报要素" align="center" prop="declarationElements" min-width="150" show-overflow-tooltip />
          <el-table-column label="备注" align="center" prop="remarks" />
          <!--        <el-table-column label="审批状态" align="center" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="getApprovalStatusType(scope.row.status)" class="status-tag">
              {{ getApprovalStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>-->
          <el-table-column label="创建者" align="center" prop="createByName" />
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{m}:{s}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
            <template #default="scope">
              <el-tooltip content="归档审批" placement="top">
                <el-button
                  link
                  type="success"
                  icon="Check"
                  @click="handleBatchArchive(scope.row)"
                  v-hasPermi="['mes:declaration:archive']"
                  class="action-link-btn"
                ></el-button>
              </el-tooltip>
              <el-tooltip content="退回" placement="top">
                <el-button
                  link
                  type="danger"
                  icon="Close"
                  @click="openRejectDialog(scope.row)"
                  v-hasPermi="['mes:declaration:reject']"
                  class="action-link-btn"
                ></el-button>
              </el-tooltip>
              <el-tooltip content="修改申报信息" placement="top">
                <el-button
                  link
                  type="primary"
                  icon="Edit"
                  @click="openEditDialog(scope.row)"
                  v-hasPermi="['mes:declaration:editDeclaration']"
                  class="action-link-btn"
                ></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 审批退回对话框 -->
    <el-dialog v-model="rejectDialog.visible" title="审批退回" width="500px" append-to-body class="reject-dialog">
      <el-form ref="rejectFormRef" :model="rejectForm" :rules="rejectRules" label-width="80px" class="reject-form">
        <el-form-item label="退回原因" prop="reason">
          <el-input v-model="rejectForm.reason" type="textarea" :rows="4" placeholder="请输入退回原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="rejectLoading" @click="submitReject" class="submit-btn">
            <el-icon><Check /></el-icon>
            确 定
          </el-button>
          <el-button @click="rejectDialog.visible = false" class="cancel-btn">
            <el-icon><Close /></el-icon>
            取 消
          </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog :title="editDialog.title" v-model="editDialog.visible" width="90%" append-to-body>
      <el-form ref="editDeclarationFormRef" :model="editForm" :rules="editRules" label-width="120px" class="declaration-form">
        <!-- 申报信息（可编辑） -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Edit /></el-icon>
            <span>申报信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="申报品名" prop="declarationName">
                <el-input v-model="editForm.declarationName" placeholder="请输入申报品名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="HS CODE" prop="hsCode">
                <el-input v-model="editForm.hsCode" placeholder="请输入HS CODE" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="申报要素" prop="declarationElements">
                <el-input v-model="editForm.declarationElements" type="textarea" :rows="3" placeholder="请输入申报要素" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 基础信息（只读展示） -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>基础信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="MO">
                <el-input v-model="editForm.mo" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="料号">
                <el-input v-model="editForm.materialNumber" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="零件号">
                <el-input v-model="editForm.partNumber" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="中文品名">
                <el-input v-model="editForm.chineseName" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="英文品名">
                <el-input v-model="editForm.englishName" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="品牌">
                <el-input v-model="editForm.brand" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="型号">
                <el-input v-model="editForm.model" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="生产原厂">
                <el-input v-model="editForm.manufacturer" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="序列">
                <el-input v-model="editForm.serialNumber" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="设备情况">
                <el-input v-model="editForm.equipmentCondition" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="原产地">
                <el-input v-model="editForm.origin" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="生产/购买年月">
                <el-input v-model="editForm.productionDate" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="数量">
                <el-input v-model="editForm.quantity" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="单位">
                <el-input v-model="editForm.unit" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="单价">
                <el-input v-model="editForm.unitPrice" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="总价">
                <el-input
                  :model-value="
                    editForm.quantity != null && editForm.unitPrice != null ? (Number(editForm.unitPrice) * Number(editForm.quantity)).toFixed(2) : ''
                  "
                  readonly
                  class="readonly-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="单台净重">
                <el-input v-model="editForm.netWeight" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="总净重/KG">
                <el-input v-model="editForm.totalNetWeight" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 参数与说明（只读展示） -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Setting /></el-icon>
            <span>参数与说明</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="材质(非设备)">
                <el-input v-model="editForm.material" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="尺寸">
                <el-input v-model="editForm.dimension" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="工作原理">
                <el-input v-model="editForm.workingPrinciple" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="用途">
                <el-input v-model="editForm.functionEn" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="功能">
                <el-input v-model="editForm.functionality" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="功率">
                <el-input v-model="editForm.power" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="电压">
                <el-input v-model="editForm.voltage" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="加工方法">
                <el-input v-model="editForm.processingMethod" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否有接头">
                <el-input v-model="editForm.hasConnector" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="结构类型">
                <el-input v-model="editForm.structureType" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="胶管类5项">
                <el-input v-model="editForm.hydraulicHose" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="橡胶类5项">
                <el-input v-model="editForm.rubberMaterial" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input v-model="editForm.remarks" readonly type="textarea" :rows="2" class="readonly-input" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 图片信息（只读展示） -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Picture /></el-icon>
            <span>图片信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="货物图片">
                <ImagePreview
                  v-if="previewListResource && checkFileSuffix('.jpg') && editForm.goodsPicture"
                  :width="100"
                  :height="100"
                  :src="String(editForm.goodsPicture || '')"
                  :preview-src-list="[String(editForm.goodsPicture || '')]"
                />
                <span v-else class="text-ellipsis">{{ editForm.goodsPicture }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="型号图片">
                <ImagePreview
                  v-if="previewListResource && checkFileSuffix('.jpg') && editForm.modelPicture"
                  :width="100"
                  :height="100"
                  :src="String(editForm.modelPicture || '')"
                  :preview-src-list="[String(editForm.modelPicture || '')]"
                />
                <span v-else class="text-ellipsis">{{ editForm.modelPicture }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="标签/名牌">
                <ImagePreview
                  v-if="previewListResource && checkFileSuffix('.jpg') && editForm.nameplatePicture"
                  :width="100"
                  :height="100"
                  :src="String(editForm.nameplatePicture || '')"
                  :preview-src-list="[String(editForm.nameplatePicture || '')]"
                />
                <span v-else class="text-ellipsis">{{ editForm.nameplatePicture }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 创建信息（只读展示） -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Box /></el-icon>
            <span>创建信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="创建者">
                <el-input v-model="editForm.createByName" readonly class="readonly-input" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="创建时间">
                <el-input
                  :model-value="
                    parseTime && editForm.createTime ? parseTime(editForm.createTime, '{y}-{m}-{d} {h}:{m}:{s}') : editForm.createTime || ''
                  "
                  readonly
                  class="readonly-input"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="editButtonLoading" type="primary" @click="submitEditForm" size="large">
            <el-icon><Check /></el-icon>
            确 定
          </el-button>
          <el-button @click="cancelEdit" size="large">
            <el-icon><Close /></el-icon>
            取 消
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Declaration" lang="ts">
import { listDeclaration, archiveDeclaration, rejectDeclaration, editDeclaration } from '@/api/mes/declaration';
import { DeclarationVO, DeclarationQuery, DeclarationForm } from '@/api/mes/declaration/types';
import ImagePreview from '@/components/ImagePreview/index.vue';
import { Check, Close, Search, Refresh, Picture, Box, Edit, Setting, Warning, Document } from '@element-plus/icons-vue';
import ImageUpload from '@/components/ImageUpload/index.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const declarationList = ref<DeclarationVO[]>([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const mos = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const tableHeight = ref(520);

const previewListResource = ref(true);

const queryFormRef = ref<ElFormInstance>();

// 审批相关
const rejectDialog = reactive<DialogOption>({
  visible: false,
  title: '审批退回'
});

const rejectForm = reactive({
  reason: ''
});

const rejectRules = {
  reason: [{ required: true, message: '请输入退回原因', trigger: 'blur' }]
};

const rejectFormRef = ref<ElFormInstance>();
const rejectLoading = ref(false);
const currentRejectId = ref<Array<string | number> | string | number>('');

// 2. 新增弹窗所需变量（避免与主表单变量冲突）
const editDialog = reactive({
  visible: false,
  title: ''
});
const editDeclarationFormRef = ref<ElFormInstance>();
const editForm = reactive<Partial<DeclarationVO>>({});
const editRules = {
  declarationName: [{ required: true, message: '申报品名不能为空', trigger: 'blur' }],
  hsCode: [{ required: true, message: 'HS CODE不能为空', trigger: 'blur' }],
  declarationElements: [{ required: true, message: '申报要素不能为空', trigger: 'blur' }]
};
const editButtonLoading = ref(false);

const data = reactive<PageData<DeclarationForm, DeclarationQuery>>({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    mo: undefined,
    declarationName: undefined,
    hsCode: undefined,
    declarationElements: undefined,
    materialNumber: undefined,
    partNumber: undefined,
    chineseName: undefined,
    brand: undefined,
    hydraulicHose: undefined,
    rubberMaterial: undefined,
    status: '1',
    params: {}
  },
  rules: {
    mo: [{ required: true, message: 'MO不能为空', trigger: 'blur' }],
    declarationName: [{ required: true, message: '申报品名不能为空', trigger: 'blur' }],
    hsCode: [{ required: true, message: 'HS CODE不能为空', trigger: 'blur' }],
    declarationElements: [{ required: true, message: '申报要素不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询备件备案信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDeclaration(queryParams.value);
  declarationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};
// 打开退回对话框
const openRejectDialog = (row: DeclarationVO) => {
  currentRejectId.value = row.id;
  rejectForm.reason = '';
  rejectDialog.visible = true;
};
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: DeclarationVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

function checkFileSuffix(fileSuffix: string | string[]) {
  const arr = ['.png', '.jpg', '.jpeg'];
  const suffixArray = Array.isArray(fileSuffix) ? fileSuffix : [fileSuffix];
  return suffixArray.some((suffix) => arr.includes(suffix.toLowerCase()));
}

/** 批量归档 */
const handleBatchArchive = async (row?: DeclarationVO) => {
  const _ids = row?.id || ids.value;
  try {
    await proxy?.$modal.confirm('确认归档编号为"' + _ids + '"的数据项？');
    await archiveDeclaration(_ids);
    proxy?.$modal.msgSuccess('归档成功');
    await getList();
  } catch (error) {
    // User cancelled the operation or other error occurred
    console.log('Archive operation cancelled or failed:', error);
  }
};

// 批量退回
const openBatchRejectDialog = (row?: DeclarationVO) => {
  currentRejectId.value = row?.id ? [row.id] : [...ids.value];
  rejectForm.reason = '';
  rejectDialog.visible = true;
};

/** 提交退回 */
const submitReject = () => {
  rejectFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      rejectLoading.value = true;
      try {
        const idList = Array.isArray(currentRejectId.value) ? currentRejectId.value : [currentRejectId.value];
        await rejectDeclaration(idList, rejectForm.reason);
        proxy?.$modal.msgSuccess(idList.length > 1 ? '批量退回成功' : '审批退回成功');
        rejectDialog.visible = false;
        await getList();
      } catch (error) {
        console.error('审批退回失败:', error);
      } finally {
        rejectLoading.value = false;
      }
    }
  });
};

// 3. openEditDialog方法
const openEditDialog = (row: DeclarationVO) => {
  Object.assign(editForm, row);
  editDialog.title = '修改申报信息';
  editDialog.visible = true;
};

// 4. submitEditForm方法
const submitEditForm = () => {
  editDeclarationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      editButtonLoading.value = true;
      try {
        await editDeclaration({
          id: editForm.id,
          declarationName: editForm.declarationName,
          hsCode: editForm.hsCode,
          declarationElements: editForm.declarationElements
        });
        proxy?.$modal.msgSuccess('修改成功');
        editDialog.visible = false;
        await getList();
      } finally {
        editButtonLoading.value = false;
      }
    }
  });
};

// 5. cancelEdit方法
const cancelEdit = () => {
  editDialog.visible = false;
};

onMounted(() => {
  const calcTableHeight = () => {
    const offset = 350; // 头部搜索、按钮和分页等占位近似高度
    tableHeight.value = Math.max(320, window.innerHeight - offset);
  };
  calcTableHeight();
  window.addEventListener('resize', calcTableHeight);
  getList();
});

onUnmounted(() => {
  // 与 onMounted 中的监听成对移除
  const calcTableHeight = () => {};
  window.removeEventListener('resize', calcTableHeight);
});
</script>

<style lang="scss" scoped>
// 搜索区域样式
.search-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: none;
  margin-bottom: 16px;

  .search-form {
    .el-form-item {
      margin-bottom: 16px;
      margin-right: 16px;

      .el-input__wrapper {
        border-radius: 6px;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 0 0 1px #409eff;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px #409eff;
        }
      }
    }

    .search-btn,
    .reset-btn {
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;

      .el-icon {
        margin-right: 4px;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }

    .search-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

      &:hover {
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }
    }

    .reset-btn {
      &:hover {
        background-color: #f5f7fa;
        border-color: #409eff;
      }
    }
  }
}

// 主卡片样式
.main-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: none;

  .card-header {
    .header-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      color: #303133;
      font-weight: 600;
      font-size: 18px;

      .el-icon {
        margin-right: 8px;
        color: #409eff;
        font-size: 20px;
      }
    }

    .action-btn {
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;

      .el-icon {
        margin-right: 4px;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      &.el-button--success {
        background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);

        &:hover {
          box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
        }
      }

      &.el-button--danger {
        background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);

        &:hover {
          box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
        }
      }
    }
  }
}

// 表格样式优化
.table-wrapper {
  /* 作为安全兜底，确保横向滚动条位于视口内 */
  max-height: calc(100vh - 260px);
  overflow: auto;
}

.approval-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  :deep(.el-table__header) {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

    th {
      background: transparent;
      color: #303133;
      font-weight: 600;
      border-bottom: 2px solid #e4e7ed;
    }
  }

  :deep(.el-table__row) {
    transition: all 0.3s;

    &:hover {
      background-color: #f0f9ff;
      transform: scale(1.001);
    }
  }

  :deep(.el-table__cell) {
    border-bottom: 1px solid #f0f0f0;
  }
}

// 状态标签样式
.status-tag {
  border-radius: 4px;
  font-weight: 500;
  padding: 4px 8px;
  transition: all 0.3s;

  &:hover {
    transform: scale(1.05);
  }
}

// 操作按钮样式
.action-link-btn {
  transition: all 0.3s;
  border-radius: 4px;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.reject-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px 24px;
      margin: 0;
      .el-dialog__title {
        color: white;
        font-weight: 600;
        font-size: 20px;
        letter-spacing: 1px;
      }
      .el-dialog__headerbtn .el-dialog__close {
        color: white;
        font-size: 18px;
        &:hover {
          color: #f0f0f0;
        }
      }
    }
    .el-dialog__body {
      padding: 32px 32px 12px 32px;
      background: #fafbff;
    }
  }
}

.reject-form {
  .el-form-item {
    margin-bottom: 24px;
    .el-textarea__inner {
      border-radius: 8px;
      border: 1.5px solid #e0e7ef;
      transition: all 0.3s;
      &:hover,
      &:focus {
        border-color: #764ba2;
        box-shadow: 0 0 0 1.5px #764ba2;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  padding: 20px 32px;
  background: #fafbff;
  border-top: 1px solid #ebeef5;
  .submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    padding: 8px 28px;
    margin-right: 12px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
    transition: all 0.2s;
    &:hover {
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.25);
    }
  }
  .cancel-btn {
    border-radius: 6px;
    font-weight: 500;
    color: #764ba2;
    background: #f5f7fa;
    border: 1px solid #e0e7ef;
    padding: 8px 24px;
    &:hover {
      background: #ede7f6;
    }
  }
}

@media (max-width: 600px) {
  .reject-dialog :deep(.el-dialog) {
    width: 95vw !important;
    .el-dialog__body,
    .dialog-footer {
      padding: 12px !important;
    }
  }
}

// 分页样式
:deep(.el-pagination) {
  margin-top: 20px;
  text-align: center;

  .el-pager li {
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      background-color: #409eff;
      color: white;
    }

    &.active {
      background-color: #409eff;
      color: white;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-card {
    .card-header {
      .header-title {
        font-size: 16px;
      }
    }
  }
}

@media (max-width: 768px) {
  .search-card {
    .search-form {
      .el-form-item {
        margin-right: 8px;
        margin-bottom: 12px;
      }
    }
  }

  .main-card {
    .card-header {
      .header-title {
        font-size: 14px;
      }

      .action-btn {
        font-size: 12px;
        padding: 8px 12px;
      }
    }
  }
}
</style>
