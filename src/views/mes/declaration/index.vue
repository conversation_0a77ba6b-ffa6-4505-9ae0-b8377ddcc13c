<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover" class="search-card">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form">
            <el-form-item label="MO" prop="mo">
              <el-input v-model="queryParams.mo" placeholder="请输入MO" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申报品名" prop="declarationName">
              <el-input v-model="queryParams.declarationName" placeholder="请输入申报品名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="HS.CODE" prop="hsCode">
              <el-input v-model="queryParams.hsCode" placeholder="请输入HS CODE" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="申报要素" prop="declarationElements">
              <el-input v-model="queryParams.declarationElements" placeholder="请输入申报要素" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="料号" prop="materialNumber">
              <el-input v-model="queryParams.materialNumber" placeholder="请输入料号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="零件号" prop="partNumber">
              <el-input v-model="queryParams.partNumber" placeholder="请输入零件号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="中文品名" prop="chineseName">
              <el-input v-model="queryParams.chineseName" placeholder="请输入中文品名" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="品牌" prop="brand">
              <el-input v-model="queryParams.brand" placeholder="请输入品牌" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="审批状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择审批状态" clearable>
                <el-option label="草稿" :value="0" />
                <el-option label="已提交" :value="1" />
                <el-option label="归档" :value="2" />
                <el-option label="退回" :value="3" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never" class="main-card">
      <template #header>
        <div class="card-header">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mes:declaration:add']" class="action-btn">
                新增
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="Edit"
                :disabled="single"
                @click="handleUpdate()"
                v-hasPermi="['mes:declaration:edit']"
                class="action-btn"
              >
                修改
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="Delete"
                :disabled="multiple"
                @click="handleDelete()"
                v-hasPermi="['mes:declaration:remove']"
                class="action-btn"
              >
                删除
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="Upload"
                :disabled="multiple"
                @click="handleBatchSubmitApproval()"
                v-hasPermi="['mes:declaration:submit']"
                class="action-btn"
              >
                提交审批
              </el-button>
            </el-col>
            <!-- 原有导入模板按钮 -->
            <el-col :span="1.5">
              <el-button v-hasPermi="['mes:declaration:importTemplate']" type="primary" plain icon="Upload" @click="handleImport" class="action-btn">
                导入数据
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mes:declaration:export']" class="action-btn">
                导出
              </el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>
        </div>
      </template>

      <div class="table-wrapper">
        <el-table v-loading="loading" border :data="declarationList" @selection-change="handleSelectionChange" :height="tableHeight">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="主键ID" align="center" prop="id" v-if="false" />
          <el-table-column label="序列 " align="center" prop="serialNumber" />
          <el-table-column label="MO" align="center" prop="mo" min-width="120" show-overflow-tooltip />
          <el-table-column label="单台净重" align="center" prop="netWeight" />
          <el-table-column label="货物图片" min-width="120" align="center" prop="goodsPicture">
            <template #default="scope">
              <ImagePreview
                v-if="previewListResource && checkFileSuffix('.jpg')"
                :width="100"
                :height="100"
                :src="scope.row.goodsPicture"
                :preview-src-list="[scope.row.goodsPicture]"
              />
              <span v-if="!checkFileSuffix('.jpg') || !previewListResource" v-text="scope.row.goodsPicture" />
            </template>
          </el-table-column>
          <el-table-column label="型号图片" min-width="120" align="center" prop="modelPicture">
            <template #default="scope">
              <ImagePreview
                v-if="previewListResource && checkFileSuffix('.jpg')"
                :width="100"
                :height="100"
                :src="scope.row.modelPicture"
                :preview-src-list="[scope.row.modelPicture]"
              />
              <span v-if="!checkFileSuffix('.jpg') || !previewListResource" v-text="scope.row.modelPicture" />
            </template>
          </el-table-column>
          <el-table-column label="标签/名牌" min-width="120" align="center" prop="nameplatePicture">
            <template #default="scope">
              <ImagePreview
                v-if="previewListResource && checkFileSuffix('.jpg')"
                :width="100"
                :height="100"
                :src="scope.row.nameplatePicture"
                :preview-src-list="[scope.row.nameplatePicture]"
              />
              <span v-if="!checkFileSuffix('.jpg') || !previewListResource" v-text="scope.row.nameplatePicture" />
            </template>
          </el-table-column>
          <el-table-column label="料号" align="center" prop="materialNumber" min-width="120" show-overflow-tooltip />
          <el-table-column label="零件号" align="center" prop="partNumber" />
          <el-table-column label="中文品名" align="center" prop="chineseName" />
          <el-table-column label="英文品名" align="center" prop="englishName" />
          <el-table-column label="生产原厂" align="center" prop="manufacturer" />
          <el-table-column label="品牌" align="center" prop="brand" />
          <el-table-column label="型号" align="center" prop="model" />
          <el-table-column label="设备情况 " align="center" prop="equipmentCondition" />
          <el-table-column label="原产地" align="center" prop="origin" />
          <el-table-column label="生产/购买年月" align="center" prop="productionDate" />
          <el-table-column label="数量" align="center" prop="quantity" />
          <el-table-column label="单价" align="center" prop="unitPrice" />
          <el-table-column prop="totalAmount" label="总价">
            <template #default="{ row }">
              {{ row.quantity != null && row.unitPrice != null ? (row.unitPrice * row.quantity).toFixed(2) : '' }}
            </template>
          </el-table-column>
          <el-table-column label="单位" align="center" prop="unit" />
          <el-table-column label="材质(非设备) " align="center" prop="material" min-width="120" show-overflow-tooltip />
          <el-table-column label="尺寸" align="center" prop="dimension" min-width="120" show-overflow-tooltip />
          <el-table-column label="工作原理" align="center" prop="workingPrinciple" />
          <el-table-column label="用途" align="center" prop="functionEn" min-width="120" show-overflow-tooltip />
          <el-table-column label="功能" align="center" prop="functionality" min-width="120" show-overflow-tooltip />
          <el-table-column label="功率" align="center" prop="power" />
          <el-table-column label="电压" align="center" prop="voltage" />
          <el-table-column label="加工方法" align="center" prop="processingMethod" />
          <el-table-column label="是否有接头 " align="center" prop="hasConnector" />
          <el-table-column label="结构类型" align="center" prop="structureType" />
          <el-table-column label="总净重/KG" align="center" prop="totalNetWeight" />
          <el-table-column label="胶管类的需确认以下5项" align="center" prop="hydraulicHose" />
          <el-table-column label="橡胶类的需确认以下5项" align="center" prop="rubberMaterial" />
          <el-table-column label="申报品名" align="center" prop="declarationName" />
          <el-table-column label="HS CODE" align="center" prop="hsCode" min-width="120" show-overflow-tooltip />
          <el-table-column label="申报要素" align="center" prop="declarationElements" min-width="150" show-overflow-tooltip />
          <el-table-column label="备注" align="center" prop="remarks" />
          <el-table-column label="审批状态" align="center" prop="status" width="100">
            <template #default="scope">
              <el-tag :type="getApprovalStatusType(scope.row.status)">
                {{ getApprovalStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建者" align="center" prop="createByName" />
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
            <template #default="scope">
              <el-tooltip content="修改" placement="top">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mes:declaration:edit']"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mes:declaration:remove']"></el-button>
              </el-tooltip>
              <el-tooltip content="提交审批" placement="top" v-if="scope.row.status === 0 || scope.row.status === '0'">
                <el-button
                  link
                  type="success"
                  icon="Upload"
                  @click="handleSubmitApproval(scope.row)"
                  v-hasPermi="['mes:declaration:submit']"
                ></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>

    <!-- 添加或修改备件备案信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="90%" append-to-body>
      <el-form ref="declarationFormRef" :model="form" :rules="rules" label-width="120px" class="declaration-form">
        <!-- 基本信息分组 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>基本信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="MO" prop="mo">
                <el-input v-model="form.mo" placeholder="请输入MO" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单台净重" prop="netWeight">
                <el-input v-model="form.netWeight" placeholder="请输入单台净重" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 图片信息分组 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Picture /></el-icon>
            <span>图片信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="货物图片" prop="goodsPicture">
                <div class="image-upload-wrapper">
                  <ImageUpload
                    v-model="form.goodsPicture"
                    :limit="1"
                    :file-size="5"
                    :file-type="['png', 'jpg', 'jpeg']"
                    :is-show-tip="true"
                    :compress-support="true"
                    :compress-target-size="300"
                  />
                  <div class="upload-tip">建议尺寸：800x600px，大小不超过5MB</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="型号图片" prop="modelPicture">
                <div class="image-upload-wrapper">
                  <ImageUpload
                    v-model="form.modelPicture"
                    :limit="1"
                    :file-size="5"
                    :file-type="['png', 'jpg', 'jpeg']"
                    :is-show-tip="true"
                    :compress-support="true"
                    :compress-target-size="300"
                  />
                  <div class="upload-tip">建议尺寸：800x600px，大小不超过5MB</div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="标签/名牌图片" prop="nameplatePicture">
                <div class="image-upload-wrapper">
                  <ImageUpload
                    v-model="form.nameplatePicture"
                    :limit="1"
                    :file-size="5"
                    :file-type="['png', 'jpg', 'jpeg']"
                    :is-show-tip="true"
                    :compress-support="true"
                    :compress-target-size="300"
                  />
                  <div class="upload-tip">建议尺寸：800x600px，大小不超过5MB</div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- 产品信息分组 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Box /></el-icon>
            <span>产品信息</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="料号" prop="materialNumber">
                <el-input v-model="form.materialNumber" placeholder="请输入料号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="零件号" prop="partNumber">
                <el-input v-model="form.partNumber" placeholder="请输入零件号" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="中文品名" prop="chineseName">
                <el-input v-model="form.chineseName" placeholder="请输入中文品名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="英文品名" prop="englishName">
                <el-input v-model="form.englishName" placeholder="请输入英文品名" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="生产原厂" prop="manufacturer">
                <el-input v-model="form.manufacturer" placeholder="请输入生产原厂" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="品牌" prop="brand">
                <el-input v-model="form.brand" placeholder="请输入品牌" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="型号" prop="model">
                <el-input v-model="form.model" placeholder="请输入型号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="序列" prop="serialNumber">
                <el-input v-model="form.serialNumber" placeholder="请输入序列" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 规格参数分组 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Setting /></el-icon>
            <span>规格参数</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="设备情况" prop="equipmentCondition">
                <el-select v-model="form.equipmentCondition" placeholder="请选择设备情况" style="width: 100%">
                  <el-option label="新设备" value="新" />
                  <el-option label="旧设备" value="旧" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="原产地" prop="origin">
                <el-input v-model="form.origin" placeholder="请输入原产地" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="生产/购买年月" prop="productionDate">
                <el-date-picker
                  v-model="form.productionDate"
                  type="month"
                  placeholder="请选择生产/购买年月"
                  format="YYYY-MM"
                  value-format="YYYY-MM"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="数量" prop="quantity">
                <el-input-number v-model="form.quantity" :min="0" placeholder="请输入数量" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="单位" prop="unit">
                <el-input v-model="form.unit" placeholder="请输入单位" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单价" prop="unitPrice">
                <el-input-number v-model="form.unitPrice" :min="0" :precision="2" placeholder="请输入单价" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="材质(非设备)" prop="material">
                <el-input v-model="form.material" placeholder="请输入材质" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="尺寸" prop="dimension">
                <el-input v-model="form.dimension" placeholder="请输入尺寸" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="工作原理" prop="workingPrinciple">
                <el-input v-model="form.workingPrinciple" placeholder="请输入工作原理" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用途" prop="functionEn">
                <el-input v-model="form.functionEn" placeholder="请输入用途" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="功能" prop="functionality">
                <el-input v-model="form.functionality" placeholder="请输入功能" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="功率" prop="power">
                <el-input v-model="form.power" placeholder="请输入功率" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="电压" prop="voltage">
                <el-input v-model="form.voltage" placeholder="请输入电压" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="加工方法" prop="processingMethod">
                <el-input v-model="form.processingMethod" placeholder="请输入加工方法" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="是否有接头" prop="hasConnector">
                <el-select v-model="form.hasConnector" placeholder="请选择是否有接头" style="width: 100%">
                  <el-option label="是" value="是" />
                  <el-option label="否" value="否" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="结构类型" prop="structureType">
                <el-input v-model="form.structureType" placeholder="请输入结构类型" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="总净重/KG" prop="totalNetWeight">
                <el-input-number v-model="form.totalNetWeight" :min="0" :precision="2" placeholder="请输入总净重" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 特殊要求分组 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Warning /></el-icon>
            <span>特殊要求</span>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="胶管类确认项" prop="hydraulicHose">
                <el-input v-model="form.hydraulicHose" type="textarea" :rows="3" placeholder="请输入胶管类的需确认以下5项" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="橡胶类确认项" prop="rubberMaterial">
                <el-input v-model="form.rubberMaterial" type="textarea" :rows="3" placeholder="请输入橡胶类的需确认以下5项" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="备注" prop="remarks">
                <el-input v-model="form.remarks" type="textarea" :rows="3" placeholder="请输入备注" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm" size="large">
            <el-icon><Check /></el-icon>
            确 定
          </el-button>
          <el-button @click="cancel" size="large">
            <el-icon><Close /></el-icon>
            取 消
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport + '&deptId=' + upload.deptId"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <i-ep-upload-filled />
        </el-icon>
        <div class="el-upload__text">{{ proxy.$t('message.system.uploadText') }}</div>
        <template #tip>
          <div class="text-center el-upload__tip">
            <span>{{ proxy.$t('message.system.fileFormat') }}</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">{{
              proxy.$t('message.system.downloadTemplate')
            }}</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="importButtonLoading" @click="submitFileForm">{{ proxy.$t('message.system.submit') }} </el-button>
          <el-button @click="upload.open = false">{{ proxy.$t('message.system.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MesDeclaration" lang="ts">
import { listDeclarationAll, getDeclaration, delDeclaration, addDeclaration, updateDeclaration, submitApproval } from '@/api/mes/declaration';
import { DeclarationVO, DeclarationQuery, DeclarationForm } from '@/api/mes/declaration/types';
import { globalHeaders } from '@/utils/request';
import ImagePreview from '@/components/ImagePreview/index.vue';
import ImageUpload from '@/components/ImageUpload/index.vue';
import { Document, Picture, Edit, Box, Setting, Warning, Check, Close, Plus, Delete, Upload, Download, Search } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const declarationList = ref<DeclarationVO[]>([]);
const buttonLoading = ref(false);
const importButtonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const tableHeight = ref(350);
const calcTableHeight = () => {
  const offset = 340; // 头部搜索、按钮和分页等占位高度
  tableHeight.value = Math.max(340, window.innerHeight - offset);
};

const previewListResource = ref(true);

const queryFormRef = ref<ElFormInstance>();
const declarationFormRef = ref<ElFormInstance>();

const uploadRef = ref<ElUploadInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: DeclarationForm = {
  id: undefined,
  mo: undefined,
  netWeight: undefined,
  goodsPicture: '',
  modelPicture: '',
  nameplatePicture: '',
  remarks: undefined,
  declarationName: undefined,
  hsCode: undefined,
  declarationElements: undefined,
  materialNumber: undefined,
  partNumber: undefined,
  chineseName: undefined,
  englishName: undefined,
  manufacturer: undefined,
  brand: undefined,
  model: undefined,
  serialNumber: undefined,
  equipmentCondition: undefined,
  origin: undefined,
  productionDate: undefined,
  quantity: undefined,
  unit: undefined,
  unitPrice: undefined,
  material: undefined,
  dimension: undefined,
  workingPrinciple: undefined,
  functionEn: undefined,
  functionality: undefined,
  power: undefined,
  voltage: undefined,
  processingMethod: undefined,
  hasConnector: undefined,
  structureType: undefined,
  totalNetWeight: undefined,
  hydraulicHose: undefined,
  rubberMaterial: undefined,
  status: '0'
};
const data = reactive<PageData<DeclarationForm, DeclarationQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    mo: undefined,
    declarationName: undefined,
    hsCode: undefined,
    declarationElements: undefined,
    materialNumber: undefined,
    partNumber: undefined,
    chineseName: undefined,
    brand: undefined,
    hydraulicHose: undefined,
    rubberMaterial: undefined,
    status: undefined,
    params: {}
  },
  rules: {
    // mo: [{ required: true, message: 'MO不能为空', trigger: 'blur' }],
    // declarationName: [{ required: true, message: '申报品名不能为空', trigger: 'blur' }],
    // hsCode: [{ required: true, message: 'HS CODE不能为空', trigger: 'blur' }],
    declarationElements: [{ required: true, message: '申报要素不能为空', trigger: 'blur' }],
    goodsPicture: [{ required: false, message: '请上传货物图片', trigger: 'change' }],
    modelPicture: [{ required: false, message: '请上传型号图片', trigger: 'change' }],
    nameplatePicture: [{ required: false, message: '请上传标签/名牌图片', trigger: 'change' }]
  }
});

/*** 导入参数 */
const upload = reactive<ImportOption>({
  open: false,
  title: '',
  isUploading: false,
  updateSupport: 0,
  headers: globalHeaders(),
  url: import.meta.env.VITE_APP_BASE_API + '/mes/declaration/importData',
  deptId: '' // 新增：存储当前选中的部门ID
});

const { queryParams, form, rules } = toRefs(data);

/** 查询备件备案信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listDeclarationAll(queryParams.value);
  declarationList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  declarationFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 导入按钮操作 */
const handleImport = () => {
  upload.title = proxy.$t('message.system.import');
  upload.open = true;
};

/** 文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
  importButtonLoading.value = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false;
  upload.isUploading = false;
  importButtonLoading.value = false;
  uploadRef.value?.handleRemove(file);
  ElMessageBox.alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>',
    proxy.$t('message.system.importResult'),
    {
      dangerouslyUseHTMLString: true
    }
  );
  getList();
};

/** 提交上传文件 */
const submitFileForm = () => {
  uploadRef.value?.submit();
};
/** 多选框选中数据 */
const handleSelectionChange = (selection: DeclarationVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加备件备案信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: DeclarationVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getDeclaration(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改备件备案信息';
};

/** 提交按钮 */
const submitForm = () => {
  declarationFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateDeclaration(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addDeclaration(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: DeclarationVO) => {
  const _ids = row?.id || ids.value;
  try {
    await proxy?.$modal.confirm('是否确认删除备件备案信息编号为"' + _ids + '"的数据项？');
    await delDeclaration(_ids);
    proxy?.$modal.msgSuccess('删除成功');
    await getList();
  } catch (error) {
    // User cancelled the operation or other error occurred
    console.log('Delete operation cancelled or failed:', error);
  }
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mes/declaration/export',
    {
      ...queryParams.value
    },
    `备件备案信息_${new Date().getTime()}.xlsx`
  );
};

/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('mes/declaration/importTemplate', {}, `备件备案模板_${new Date().getTime()}.xlsx`);
};

/** 获取审批状态类型 */
const getApprovalStatusType = (status: string | number) => {
  const statusStr = String(status);
  switch (statusStr) {
    case '0':
      return 'info';
    case '1':
      return 'warning';
    case '2':
      return 'success';
    case '3':
      return 'danger';
    default:
      return 'info';
  }
};

/** 获取审批状态文本 */
const getApprovalStatusText = (status: string | number) => {
  const statusStr = String(status);
  switch (statusStr) {
    case '0':
      return '草稿';
    case '1':
      return '已提交';
    case '2':
      return '归档';
    case '3':
      return '退回';
    default:
      return '草稿';
  }
};

/** 提交审批 */
const handleSubmitApproval = async (row: DeclarationVO) => {
  try {
    await proxy?.$modal.confirm('确认提交审批吗？');
    await submitApproval(row.id);
    proxy?.$modal.msgSuccess('提交审批成功');
    await getList();
  } catch (error) {
    console.error('提交审批失败:', error);
  }
};

/** 批量提交审批 */
const handleBatchSubmitApproval = async () => {
  if (!ids.value.length) return;
  try {
    await proxy?.$modal.confirm('确认提交选中数据项吗？');
    await submitApproval(ids.value);
    proxy?.$modal.msgSuccess('批量提交审批成功');
    await getList();
  } catch (error) {
    console.error('批量提交审批失败:', error);
  }
};

function checkFileSuffix(fileSuffix: string | string[]) {
  const arr = ['.png', '.jpg', '.jpeg'];
  const suffixArray = Array.isArray(fileSuffix) ? fileSuffix : [fileSuffix];
  return suffixArray.some((suffix) => arr.includes(suffix.toLowerCase()));
}

onMounted(() => {
  calcTableHeight();
  window.addEventListener('resize', calcTableHeight);
  getList();
});

onUnmounted(() => {
  window.removeEventListener('resize', calcTableHeight);
});
</script>

<style lang="scss" scoped>
.declaration-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  .el-input-number {
    width: 100%;
  }

  .el-date-picker {
    width: 100%;
  }

  .el-select {
    width: 100%;
  }
}

// 表单分组样式
.form-section {
  background: #fafafa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  &:last-child {
    margin-bottom: 0;
  }

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid #409eff;
    color: #303133;
    font-weight: 600;
    font-size: 16px;

    .el-icon {
      margin-right: 8px;
      color: #409eff;
      font-size: 18px;
    }
  }
}

// 图片上传区域样式
.image-upload-wrapper {
  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    text-align: center;
    line-height: 1.4;
  }
}

// 图片上传组件样式优化
:deep(.component-upload-image) {
  .el-upload--picture-card {
    width: 140px;
    height: 140px;
    line-height: 140px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
      background-color: #f0f9ff;
    }
  }

  .el-upload-list--picture-card .el-upload-list__item {
    width: 140px;
    height: 140px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  .avatar-uploader-icon {
    font-size: 32px;
    color: #c0c4cc;
    width: 140px;
    height: 140px;
    line-height: 140px;
    text-align: center;
    transition: all 0.3s;

    &:hover {
      color: #409eff;
    }
  }

  .el-upload__tip {
    margin-top: 8px;
    color: #909399;
    font-size: 12px;
  }
}

// 对话框样式优化
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    margin: 0;

    .el-dialog__title {
      color: white;
      font-weight: 600;
      font-size: 18px;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 18px;

        &:hover {
          color: #f0f0f0;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
  }
}

// 表单输入框样式优化
:deep(.el-input) {
  .el-input__wrapper {
    border-radius: 6px;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 0 0 1px #409eff;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px #409eff;
    }
  }
}

:deep(.el-textarea) {
  .el-textarea__inner {
    border-radius: 6px;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
    }

    &:focus {
      border-color: #409eff;
      box-shadow: 0 0 0 1px #409eff;
    }
  }
}

:deep(.el-select) {
  .el-input__wrapper {
    border-radius: 6px;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 0 0 1px #409eff;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px #409eff;
    }
  }
}

:deep(.el-input-number) {
  .el-input__wrapper {
    border-radius: 6px;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 0 0 1px #409eff;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px #409eff;
    }
  }
}

:deep(.el-date-editor) {
  .el-input__wrapper {
    border-radius: 6px;
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 0 0 1px #409eff;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px #409eff;
    }
  }
}

// 按钮样式优化
.dialog-footer {
  text-align: right;
  padding: 20px 24px;
  border-top: 1px solid #ebeef5;
  background: #fafafa;

  .el-button {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s;

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .el-icon {
      margin-right: 4px;
    }
  }
}

// 主卡片样式
.main-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: none;

  .card-header {
    .header-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      color: #303133;
      font-weight: 600;
      font-size: 18px;

      .el-icon {
        margin-right: 8px;
        color: #409eff;
        font-size: 20px;
      }
    }

    .action-btn {
      border-radius: 6px;
      font-weight: 500;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;

      .el-icon {
        margin-right: 4px;
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }
  }
}

// 表格样式优化
:deep(.table-wrapper) {
  max-height: calc(100vh - 260px);
  overflow: auto;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .el-table__header {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

    th {
      background: transparent;
      color: #303133;
      font-weight: 600;
      border-bottom: 2px solid #e4e7ed;
    }
  }

  .el-table__row {
    transition: all 0.3s;

    &:hover {
      background-color: #f0f9ff;
      transform: scale(1.001);
    }
  }

  .el-table__cell {
    border-bottom: 1px solid #f0f0f0;
  }
}

// 搜索区域样式
.search-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: none;
  margin-bottom: 16px;

  .search-header {
    display: flex;
    align-items: center;
    color: #303133;
    font-weight: 600;
    font-size: 16px;

    .el-icon {
      margin-right: 8px;
      color: #409eff;
      font-size: 18px;
    }
  }

  .search-form {
    .el-form-item {
      margin-bottom: 16px;
      margin-right: 16px;

      .el-input__wrapper {
        border-radius: 6px;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 0 0 1px #409eff;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px #409eff;
        }
      }

      .el-select .el-input__wrapper {
        border-radius: 6px;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 0 0 1px #409eff;
        }

        &.is-focus {
          box-shadow: 0 0 0 1px #409eff;
        }
      }
    }
  }
}

// 分页样式
:deep(.el-pagination) {
  margin-top: 20px;
  text-align: center;

  .el-pager li {
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      background-color: #409eff;
      color: white;
    }

    &.active {
      background-color: #409eff;
      color: white;
    }
  }
}

// 标签样式优化
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
  padding: 4px 8px;
}

// 响应式设计
@media (max-width: 1200px) {
  .form-section {
    padding: 16px;
  }

  :deep(.component-upload-image) {
    .el-upload--picture-card,
    .el-upload-list--picture-card .el-upload-list__item,
    .avatar-uploader-icon {
      width: 120px;
      height: 120px;
      line-height: 120px;
    }
  }
}

@media (max-width: 768px) {
  .form-section {
    padding: 12px;
  }

  :deep(.component-upload-image) {
    .el-upload--picture-card,
    .el-upload-list--picture-card .el-upload-list__item,
    .avatar-uploader-icon {
      width: 100px;
      height: 100px;
      line-height: 100px;
    }
  }

  .main-card {
    .card-header {
      .header-title {
        font-size: 16px;
      }
    }
  }
}
</style>
