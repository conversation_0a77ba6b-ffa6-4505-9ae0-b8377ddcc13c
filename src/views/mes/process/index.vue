<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="工序编码" prop="processCode">
              <el-input v-model="queryParams.processCode" placeholder="请输入工序编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工序名称" prop="processName">
              <el-input v-model="queryParams.processName" placeholder="请输入工序名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工作中心" prop="workCenter">
              <el-input v-model="queryParams.workCenter" placeholder="请输入工作中心" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="生成描述" prop="descFlag">
              <el-select v-model="queryParams.descFlag" placeholder="请选择是否生成描述" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="外托运" prop="invoiceFlag">
              <el-select v-model="queryParams.invoiceFlag" placeholder="请选择是否外托运" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="电镀" prop="platingFlag">
              <el-select v-model="queryParams.platingFlag" placeholder="请选择是否电镀" clearable>
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mes:process:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mes:process:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mes:process:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mes:process:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="processList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="工序编码" align="center" prop="processCode" min-width="100" />
        <el-table-column
          class-name="column-bg-color-editable"
          show-overflow-tooltip
          label="工序名称"
          align="center"
          prop="processName"
          min-width="120"
        >
          <template #default="{ row }">
            <div v-if="!row.editing">{{ row.processName }}</div>
            <el-input v-else v-model="row.processName" placeholder="请输入名称"></el-input>
          </template>
          <!--          <template v-slot="scope">
            <div class="input-box">
              <el-input size="small" @blur="handleInputBlur" v-model="scope.row.processName"></el-input>
            </div>
          </template>-->
        </el-table-column>
        <el-table-column label="工作中心" align="center" prop="workCenter" min-width="100" />
        <el-table-column label="工厂代码" align="center" prop="tenantId" min-width="100" />
        <el-table-column label="启用" align="center" prop="enableFlag" width="80">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.enableFlag" />
          </template>
        </el-table-column>
        <el-table-column label="生成描述" align="center" prop="descFlag" width="100">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.descFlag" />
          </template>
        </el-table-column>
        <el-table-column label="外托运" align="center" prop="invoiceFlag" width="100">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.invoiceFlag" />
          </template>
        </el-table-column>
        <el-table-column label="电镀" align="center" prop="platingFlag" width="80">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.platingFlag" />
          </template>
        </el-table-column>
        <el-table-column label="总机时为0" align="center" prop="noYesHours" width="120">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.noYesHours" />
          </template>
        </el-table-column>
        <el-table-column label="电镀类型" align="center" prop="platingType" min-width="100" />
        <el-table-column label="描述" align="center" prop="description" min-width="150" show-overflow-tooltip />
        <!--        <el-table-column label="急单等待时间" align="center" prop="urgWaitTime" min-width="100" />-->
        <!--        <el-table-column label="等待时间" align="center" prop="waitTime" min-width="100" />-->
        <!--        <el-table-column label="批量等待时间" align="center" prop="batchWaitTime" min-width="100" />-->
        <el-table-column label="备注" align="center" prop="remark" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作" align="center" width="120" fixed="right">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mes:process:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mes:process:remove']"></el-button>
            </el-tooltip>
          </template>
          <!--          <template #default="{ row }">
            <div v-if="row.editing">
              <el-button type="primary" size="small" @click="saveRow(row)">保存</el-button>
              <el-button @click="cancelRowEdit(row)">取消</el-button>
            </div>
            <div v-else>
              <el-button type="warning" size="small" @click="startEdit(row)">编辑</el-button>
            </div>
          </template>-->
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改工序管理对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="800px" append-to-body class="process-dialog">
      <el-form ref="processFormRef" :model="form" :rules="rules" label-width="120px" class="process-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工序编码" prop="processCode">
              <div class="flex items-center">
                <el-input v-model="form.processCode" placeholder="请输入工序编码" class="custom-input flex-1" />
                <el-switch
                  v-model="autoGenFlag"
                  active-color="#13ce66"
                  active-text="自动生成"
                  @change="handleAutoGenChange(autoGenFlag)"
                  v-if="optType != 'view'"
                  class="ml-2"
                >
                </el-switch>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工序名称" prop="processName">
              <el-input v-model="form.processName" placeholder="请输入工序名称" class="custom-input" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工作中心" prop="workCenter">
              <div class="flex items-center">
                <el-input v-model="form.workCenter" placeholder="请选择工作中心" readonly class="flex-1 custom-input" clearable />
                <el-button type="primary" icon="Search" @click="openWorkCenterSelect" class="ml-2" v-if="optType != 'view'">选择</el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否启用" prop="enableFlag">
              <el-select v-model="form.enableFlag" placeholder="请选择是否启用" style="width: 100%" class="custom-select">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left" class="custom-divider">工序特性设置</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否生成描述" prop="descFlag">
              <el-select v-model="form.descFlag" placeholder="请选择是否生成描述" style="width: 100%" class="custom-select">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否外托运" prop="invoiceFlag">
              <el-select v-model="form.invoiceFlag" placeholder="请选择是否外托运" style="width: 100%" class="custom-select">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否电镀" prop="platingFlag">
              <el-select v-model="form.platingFlag" placeholder="请选择是否电镀" style="width: 100%" class="custom-select">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="电镀类型" prop="platingType">
              <el-input v-model="form.platingType" placeholder="请输入电镀类型" class="custom-input" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="总机时是否为0" prop="noYesHours">
              <el-select v-model="form.noYesHours" placeholder="请选择总机时是否为0" style="width: 100%" class="custom-select">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left" class="custom-divider">时间设置</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="急单等待时间" prop="urgWaitTime">
              <el-input v-model="form.urgWaitTime" placeholder="请输入急单等待时间" class="custom-input" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="等待时间" prop="waitTime">
              <el-input v-model="form.waitTime" placeholder="请输入等待时间" class="custom-input" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="批量等待时间" prop="batchWaitTime">
              <el-input v-model="form.batchWaitTime" placeholder="请输入批量等待时间" class="custom-input" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left" class="custom-divider">其他信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="描述" prop="description">
              <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入描述内容" class="custom-textarea" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注内容" class="custom-textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm" class="submit-btn">确 定</el-button>
          <el-button @click="cancel" class="cancel-btn">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 工作中心选择弹窗 -->
    <el-dialog title="选择工作中心" v-model="workCenterDialog.visible" width="800px" append-to-body class="custom-dialog">
      <el-form :inline="true" class="search-form mb-4">
        <el-form-item label="代号">
          <el-input v-model="workCenterQuery.code" placeholder="请输入工作中心代号" clearable />
        </el-form-item>
        <el-form-item label="说明">
          <el-input v-model="workCenterQuery.description" placeholder="请输入工作中心说明" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleWorkCenterSearch">搜索</el-button>
          <el-button icon="Refresh" @click="resetWorkCenterSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="workCenterLoading"
        :data="workCenterList"
        @row-click="handleWorkCenterSelect"
        @row-dblclick="handleWorkCenterDblclick"
        highlight-current-row
        :row-class-name="workCenterRowClassName"
        class="custom-table"
      >
        <el-table-column label="代号" align="center" prop="code" />
        <el-table-column label="说明" align="center" prop="description" width="230" :show-overflow-tooltip="true" />
        <el-table-column label="类型" align="center" prop="type" />
        <el-table-column label="公司代码" align="center" prop="companyCode" />
        <el-table-column label="工厂代码" align="center" prop="factoryCode" />
      </el-table>

      <pagination
        v-show="workCenterTotal > 0"
        :total="workCenterTotal"
        v-model:page="workCenterQuery.pageNum"
        v-model:limit="workCenterQuery.pageSize"
        @pagination="getWorkCenterList"
      />
    </el-dialog>
  </div>
</template>

<script setup name="Process" lang="ts">
import { addProcess, delProcess, getProcess, listProcess, updateProcess } from '@/api/mes/process';
import { listWorkcenter } from '@/api/mes/workcenter';
import { ProcessForm, ProcessQuery, ProcessVO } from '@/api/mes/process/types';
import { genCode } from '@/api/system/autocode';
import { WorkcenterVO } from '@/api/mes/workcenter/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no } = toRefs<any>(proxy?.useDict('sys_yes_no'));

const processList = ref<ProcessVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const autoGenFlag = ref(false);
const optType = ref<string>();

const queryFormRef = ref<ElFormInstance>();
const processFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const workCenterDialog = reactive({
  visible: false
});

const workCenterQuery = reactive({
  pageNum: 1,
  pageSize: 10,
  code: '',
  description: ''
});

const workCenterList = ref<WorkcenterVO[]>([]);
const workCenterLoading = ref(false);
const workCenterTotal = ref(0);

const initFormData: ProcessForm = {
  processId: undefined,
  processCode: undefined,
  processName: undefined,
  workCenter: undefined,
  enableFlag: 'Y',
  descFlag: 'N',
  invoiceFlag: 'N',
  platingFlag: 'N',
  noYesHours: 'N',
  platingType: undefined,
  description: undefined,
  urgWaitTime: undefined,
  waitTime: undefined,
  batchWaitTime: undefined,
  remark: undefined
};
const data = reactive<PageData<ProcessForm, ProcessQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    processCode: undefined,
    processName: undefined,
    workCenter: undefined,
    enableFlag: undefined,
    descFlag: undefined,
    invoiceFlag: undefined,
    platingFlag: undefined,
    noYesHours: undefined,
    params: {}
  },
  rules: {
    processId: [{ required: true, message: '工序ID不能为空', trigger: 'blur' }],
    processCode: [{ required: true, message: '工序编码不能为空', trigger: 'blur' }],
    processName: [{ required: true, message: '工序名称不能为空', trigger: 'blur' }],
    workCenter: [{ required: true, message: '工作中心不能为空', trigger: 'blur' }],
    enableFlag: [{ required: true, message: '是否启用不能为空', trigger: 'change' }],
    descFlag: [{ required: true, message: '是否生成描述不能为空', trigger: 'change' }],
    invoiceFlag: [{ required: true, message: '是否外托运不能为空', trigger: 'change' }],
    platingFlag: [{ required: true, message: '是否电镀不能为空', trigger: 'change' }],
    noYesHours: [{ required: true, message: '总机时是否为0不能为空', trigger: 'change' }],
    description: [{ required: true, message: '描述不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 开始编辑某行
const startEdit = async (row) => {
  row.editing = true; // 标记当前行为编辑状态
};
// 保存行数据
const saveRow = async (row) => {
  console.log('保存成功:', row);
  row.editing = false; // 关闭编辑状态

  // 可在此处发送 API 请求保存数据
};
// 取消编辑
const cancelRowEdit = async (row) => {
  // 从缓存或重新获取数据还原（示例直接反转数值）
  row.age = 100 - row.age; // 模拟回滚逻辑
  row.editing = false;
};

/** 查询工序管理列表 */
const getList = async () => {
  loading.value = true;
  const res = await listProcess(queryParams.value);
  processList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  autoGenFlag.value = false;
  processFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ProcessVO[]) => {
  ids.value = selection.map((item) => item.processId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加工序管理';
};

/** 自动生成编码 */
const handleAutoGenChange = async (autoGenFlag: boolean) => {
  if (autoGenFlag) {
    const response = await genCode('PROCESS_CODE');
    form.value.processCode = response.data;
  } else {
    form.value.processCode = undefined;
  }
};
/** 修改按钮操作 */
const handleUpdate = async (row?: ProcessVO) => {
  reset();
  const _processId = row?.processId || ids.value[0];
  const res = await getProcess(_processId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改工序管理';
};

/** 提交按钮 */
const submitForm = () => {
  processFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.processId) {
        await updateProcess(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addProcess(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ProcessVO) => {
  const _processIds = row?.processId || ids.value;
  await proxy?.$modal.confirm('是否确认删除工序管理编号为"' + _processIds + '"的数据项？').finally(() => (loading.value = false));
  await delProcess(_processIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mes/process/export',
    {
      ...queryParams.value
    },
    `process_${new Date().getTime()}.xlsx`
  );
};
//input框失去焦点事件
/*const handleInputBlur = (event) => {
  const _event = event;
  setTimeout(function () {
    const _inputNode = _event.target;
    if (emptransfer.getParentElement(_inputNode, 4) !== 0) {
      const _cellNode = emptransfer.getParentElement(_inputNode, 4);
      emptransfer.removeClass(_cellNode, 'current-cell');
      emptransfer.removeClass(_cellNode, 'current-cell2');
    }
  }, 200);
};*/

/** 打开工作中心选择弹窗 */
const openWorkCenterSelect = () => {
  workCenterDialog.visible = true;
  getWorkCenterList();
};
/** 获取工作中心列表 */
const getWorkCenterList = async () => {
  workCenterLoading.value = true;
  try {
    const res = await listWorkcenter(workCenterQuery);
    workCenterList.value = res.rows;
    workCenterTotal.value = res.total;
  } finally {
    workCenterLoading.value = false;
  }
};

/** 搜索工作中心 */
const handleWorkCenterSearch = () => {
  workCenterQuery.pageNum = 1;
  getWorkCenterList();
};

/** 重置工作中心搜索 */
const resetWorkCenterSearch = () => {
  workCenterQuery.code = '';
  workCenterQuery.description = '';
  handleWorkCenterSearch();
};

/** 选择工作中心 */
const handleWorkCenterSelect = (row: WorkcenterVO) => {
  // 单击行高亮显示,不做其他操作
};

/** 工作中心表格行类名 */
const workCenterRowClassName = (row: WorkcenterVO) => {
  return row.code === form.value.workCenter ? 'row-dblclick' : '';
};

/** 双击选择工作中心 */
const handleWorkCenterDblclick = (row: WorkcenterVO) => {
  form.value.workCenter = row.code;
  proxy?.$modal.msgSuccess(`已选择工作中心: $de}`);
  workCenterDialog.visible = false;
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.tb-edit .input-box {
  display: none;
}
.tb-edit .current-cell .input-box {
  display: block;
  margin-left: -15px;
}

:deep(.row-dblclick) {
  background-color: var(--el-color-primary-light-8) !important;
  transition: background-color 0.15s ease-in-out;
}

.custom-table {
  :deep(.el-table__row) {
    cursor: pointer;

    &:hover {
      background-color: var(--el-color-primary-light-9);
    }
  }
}

.process-dialog {
  :deep(.el-dialog__body) {
    padding: 20px 30px;
  }
}

.process-form {
  .custom-input,
  .custom-select,
  .custom-textarea {
    transition: all 0.3s;

    &:hover,
    &:focus {
      box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
    }
  }

  .custom-divider {
    margin: 20px 0;
    font-weight: bold;
    color: var(--el-text-color-primary);
  }
}

.dialog-footer {
  .submit-btn {
    min-width: 100px;
  }

  .cancel-btn {
    min-width: 100px;
  }
}
</style>
