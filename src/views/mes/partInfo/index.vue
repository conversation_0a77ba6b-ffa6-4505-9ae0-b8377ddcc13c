<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="物料类型" prop="partType">
              <el-select v-model="queryParams.partType" placeholder="请选择物料类型" clearable>
                <el-option v-for="dict in item_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="物料编码" prop="partId">
              <el-input v-model="queryParams.partId" placeholder="请输入物料编码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="物料名称" prop="partName">
              <el-input v-model="queryParams.partName" placeholder="请输入物料名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="图号版本" prop="drawNumVersion">
              <el-input v-model="queryParams.drawNumVersion" placeholder="请输入图号版本" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select v-model="queryParams.status" placeholder="请选择物料状态" clearable>
                <el-option v-for="dict in item_is_status" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间" style="width: 308px">
              <el-date-picker
                v-model="dateRangeCreateDate"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mes:partInfo:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="partInfoList" @selection-change="handleSelectionChange" style="width: 100%">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="false" />
        <el-table-column label="物料编码" align="center" prop="partId" min-width="120" show-overflow-tooltip />
        <el-table-column label="物料名称" align="center" prop="partName" min-width="200" show-overflow-tooltip />
        <el-table-column label="物料类型" align="center" prop="partType" min-width="100" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="item_type" :value="scope.row.partType" />
          </template>
        </el-table-column>
        <el-table-column label="物料组" align="center" prop="partGroup" min-width="100" show-overflow-tooltip />
        <el-table-column label="规格描述" align="center" prop="spec" min-width="150" show-overflow-tooltip />
        <el-table-column label="状态" align="center" prop="status" min-width="80" show-overflow-tooltip>
          <template #default="scope">
            <dict-tag :options="item_is_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="图号" align="center" prop="drawNum" min-width="120" show-overflow-tooltip />
        <el-table-column label="图号版本" align="center" prop="drawNumVersion" min-width="150" show-overflow-tooltip />
        <!--        item_batch_flag-->
        <el-table-column label="批次管理" align="center" prop="batchManag" min-width="80">
          <template #default="scope">
            <dict-tag :options="item_batch_flag" :value="scope.row.batchManag" />
          </template>
        </el-table-column>
        <el-table-column label="生产组" align="center" prop="prodGroup" min-width="100" show-overflow-tooltip />
        <el-table-column label="品牌名称" align="center" prop="brand" min-width="120" show-overflow-tooltip />
        <el-table-column label="品牌备注" align="center" prop="brandRemarks" min-width="120" show-overflow-tooltip />
        <el-table-column label="供应商代码" align="center" prop="compCode" min-width="120" show-overflow-tooltip />
        <el-table-column label="是否禁用" align="center" prop="unable" min-width="80" show-overflow-tooltip />
        <el-table-column label="国产/进口" align="center" prop="domesticOrImport" min-width="100" show-overflow-tooltip />
        <el-table-column label="正品/替代品" align="center" prop="authenticAlterna" min-width="120" show-overflow-tooltip />
        <el-table-column label="长度" align="center" prop="length1" min-width="80" show-overflow-tooltip />
        <el-table-column label="高度" align="center" prop="high1" min-width="80" show-overflow-tooltip />
        <el-table-column label="计量单位" align="center" prop="unit" min-width="80" show-overflow-tooltip />
        <el-table-column label="宽度" align="center" prop="width1" min-width="80" show-overflow-tooltip />
        <el-table-column label="密度" align="center" prop="density" min-width="80" show-overflow-tooltip />
        <el-table-column label="客户代码" align="center" prop="custCode" min-width="100" show-overflow-tooltip />
        <el-table-column label="行业标准" align="center" prop="industryStd" min-width="100" show-overflow-tooltip />
        <el-table-column label="米重" align="center" prop="meterWeight" min-width="80" show-overflow-tooltip />
        <el-table-column label="图纸等级" align="center" prop="drawingLevel" min-width="100" show-overflow-tooltip />
        <el-table-column label="标准工艺" align="center" prop="stdProcess" min-width="120" show-overflow-tooltip />
        <el-table-column label="直径" align="center" prop="diameter" min-width="80" show-overflow-tooltip />
        <el-table-column label="内径" align="center" prop="insideDiameter" min-width="80" show-overflow-tooltip />
        <el-table-column label="外径" align="center" prop="outerDiameter" min-width="80" show-overflow-tooltip />
        <el-table-column label="包装尺寸" align="center" prop="boxSize" min-width="120" show-overflow-tooltip />
        <!--        <el-table-column label="SAP同步状态" align="center" prop="sapSyncInfo" min-width="120" show-overflow-tooltip />-->
        <el-table-column label="创建时间" align="center" prop="createDate" width="180" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ parseTime(scope.row.createDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" align="center" prop="updateDate" width="180" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ parseTime(scope.row.updateDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改物料档案信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="partInfoFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="长度(mm)" prop="length1">
          <el-input v-model="form.length1" placeholder="请输入长度(mm)" />
        </el-form-item>
        <el-form-item label="物料类型" prop="partType">
          <el-select v-model="form.partType" placeholder="请选择物料类型">
            <el-option v-for="dict in item_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="规格描述" prop="spec">
          <el-input v-model="form.spec" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="高度(mm)" prop="high1">
          <el-input v-model="form.high1" placeholder="请输入高度(mm)" />
        </el-form-item>
        <el-form-item label="计量单位" prop="unit">
          <el-input v-model="form.unit" placeholder="请输入计量单位" />
        </el-form-item>
        <el-form-item label="物料组" prop="partGroup">
          <el-input v-model="form.partGroup" placeholder="请输入物料组" />
        </el-form-item>
        <el-form-item label="宽度(mm)" prop="width1">
          <el-input v-model="form.width1" placeholder="请输入宽度(mm)" />
        </el-form-item>
        <el-form-item label="物料编码" prop="partId">
          <el-input v-model="form.partId" placeholder="请输入物料编码" />
        </el-form-item>
        <el-form-item label="物料名称" prop="partName">
          <el-input v-model="form.partName" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="批次管理标识" prop="batchManag">
          <el-input v-model="form.batchManag" placeholder="请输入批次管理标识" />
        </el-form-item>
        <el-form-item label="生产组" prop="prodGroup">
          <el-input v-model="form.prodGroup" placeholder="请输入生产组" />
        </el-form-item>
        <el-form-item label="工厂代码" prop="plantCode">
          <el-input v-model="form.plantCode" placeholder="请输入工厂代码" />
        </el-form-item>
        <el-form-item label="品牌名称" prop="brand">
          <el-input v-model="form.brand" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="图号" prop="drawNum">
          <el-input v-model="form.drawNum" placeholder="请输入图号" />
        </el-form-item>
        <el-form-item label="供应商代码" prop="compCode">
          <el-input v-model="form.compCode" placeholder="请输入供应商代码" />
        </el-form-item>
        <el-form-item label="是否禁用" prop="unable">
          <el-input v-model="form.unable" placeholder="请输入是否禁用" />
        </el-form-item>
        <el-form-item label="国产/进口" prop="domesticOrImport">
          <el-input v-model="form.domesticOrImport" placeholder="请输入国产/进口" />
        </el-form-item>
        <el-form-item label="正品/替代品" prop="authenticAlterna">
          <el-input v-model="form.authenticAlterna" placeholder="请输入正品/替代品" />
        </el-form-item>
        <el-form-item label="密度(g/cm³)" prop="density">
          <el-input v-model="form.density" placeholder="请输入密度(g/cm³)" />
        </el-form-item>
        <el-form-item label="品牌备注" prop="brandRemarks">
          <el-input v-model="form.brandRemarks" placeholder="请输入品牌备注" />
        </el-form-item>
        <el-form-item label="图纸版本说明" prop="drawNumVersion">
          <el-input v-model="form.drawNumVersion" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="客户代码" prop="custCode">
          <el-input v-model="form.custCode" placeholder="请输入客户代码" />
        </el-form-item>
        <el-form-item label="行业标准" prop="industryStd">
          <el-input v-model="form.industryStd" placeholder="请输入行业标准" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option v-for="dict in item_is_status" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="米重(kg/m)" prop="meterWeight">
          <el-input v-model="form.meterWeight" placeholder="请输入米重(kg/m)" />
        </el-form-item>
        <el-form-item label="图纸等级" prop="drawingLevel">
          <el-input v-model="form.drawingLevel" placeholder="请输入图纸等级" />
        </el-form-item>
        <el-form-item label="标准工艺" prop="stdProcess">
          <el-input v-model="form.stdProcess" placeholder="请输入标准工艺" />
        </el-form-item>
        <el-form-item label="直径(mm)" prop="diameter">
          <el-input v-model="form.diameter" placeholder="请输入直径(mm)" />
        </el-form-item>
        <el-form-item label="内径(mm)" prop="insideDiameter">
          <el-input v-model="form.insideDiameter" placeholder="请输入内径(mm)" />
        </el-form-item>
        <el-form-item label="外径(mm)" prop="outerDiameter">
          <el-input v-model="form.outerDiameter" placeholder="请输入外径(mm)" />
        </el-form-item>
        <el-form-item label="包装尺寸" prop="boxSize">
          <el-input v-model="form.boxSize" placeholder="请输入包装尺寸" />
        </el-form-item>
        <el-form-item label="SAP同步状态" prop="sapSyncInfo">
          <el-input v-model="form.sapSyncInfo" placeholder="请输入SAP同步状态" />
        </el-form-item>
        <el-form-item label="产线编号" prop="lineNum">
          <el-input v-model="form.lineNum" placeholder="请输入产线编号" />
        </el-form-item>
        <el-form-item label="需求订单号" prop="reqOrderNum">
          <el-input v-model="form.reqOrderNum" placeholder="请输入需求订单号" />
        </el-form-item>
        <el-form-item label="SAP更新状态" prop="sapUpdateInfo">
          <el-input v-model="form.sapUpdateInfo" placeholder="请输入SAP更新状态" />
        </el-form-item>
        <el-form-item label="原始物料号" prop="originalItem">
          <el-input v-model="form.originalItem" placeholder="请输入原始物料号" />
        </el-form-item>
        <el-form-item label="多标准管控" prop="manyStandard">
          <el-input v-model="form.manyStandard" placeholder="请输入多标准管控" />
        </el-form-item>
        <el-form-item label="研发估值" prop="rdValuation">
          <el-input v-model="form.rdValuation" placeholder="请输入研发估值" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="PartInfo" lang="ts">
import { addPartInfo, delPartInfo, getPartInfo, listPartInfo, updatePartInfo } from '@/api/mes/partInfo';
import { PartInfoForm, PartInfoQuery, PartInfoVO } from '@/api/mes/partInfo/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { item_is_status, item_type, item_batch_flag } = toRefs<any>(proxy?.useDict('item_is_status', 'item_type', 'item_batch_flag'));

const partInfoList = ref<PartInfoVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeCreateDate = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const partInfoFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: PartInfoForm = {
  id: undefined,
  length1: undefined,
  partType: undefined,
  spec: undefined,
  high1: undefined,
  unit: undefined,
  partGroup: undefined,
  width1: undefined,
  partId: undefined,
  partName: undefined,
  batchManag: undefined,
  prodGroup: undefined,
  plantCode: undefined,
  brand: undefined,
  drawNum: undefined,
  compCode: undefined,
  unable: undefined,
  domesticOrImport: undefined,
  authenticAlterna: undefined,
  density: undefined,
  brandRemarks: undefined,
  drawNumVersion: undefined,
  custCode: undefined,
  industryStd: undefined,
  status: undefined,
  meterWeight: undefined,
  drawingLevel: undefined,
  stdProcess: undefined,
  diameter: undefined,
  insideDiameter: undefined,
  outerDiameter: undefined,
  boxSize: undefined,
  sapSyncInfo: undefined,
  lineNum: undefined,
  reqOrderNum: undefined,
  sapUpdateInfo: undefined,
  originalItem: undefined,
  manyStandard: undefined,
  rdValuation: undefined
};
const data = reactive<PageData<PartInfoForm, PartInfoQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    partType: undefined,
    partId: undefined,
    partName: undefined,
    drawNum: undefined,
    drawNumVersion: undefined,
    status: undefined,
    params: {
      createDate: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询物料档案信息列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeCreateDate.value, 'CreateDate');
  const res = await listPartInfo(queryParams.value);
  partInfoList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  partInfoFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeCreateDate.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: PartInfoVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加物料档案信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: PartInfoVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getPartInfo(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改物料档案信息';
};

/** 提交按钮 */
const submitForm = () => {
  partInfoFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updatePartInfo(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addPartInfo(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: PartInfoVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除物料档案信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delPartInfo(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mes/partInfo/export',
    {
      ...queryParams.value
    },
    `partInfo_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
