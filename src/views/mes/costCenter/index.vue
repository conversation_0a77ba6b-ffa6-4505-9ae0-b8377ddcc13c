<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
            <el-form-item label="成本中心代码" prop="costCenterCode">
              <el-input v-model="queryParams.costCenterCode" placeholder="请输入成本中心代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="成本中心名称" prop="costCenterName">
              <el-input v-model="queryParams.costCenterName" placeholder="请输入成本中心名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="单价" prop="costCenterPrice">
              <el-input v-model="queryParams.costCenterPrice" placeholder="请输入单价" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mes:costCenter:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mes:costCenter:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mes:costCenter:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mes:costCenter:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="costCenterList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="" align="center" prop="id" v-if="true" />
        <el-table-column label="成本中心代码" align="center" prop="costCenterCode" />
        <el-table-column label="成本中心名称" align="center" prop="costCenterName" />
        <el-table-column label="单价" align="center" prop="costCenterPrice" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mes:costCenter:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mes:costCenter:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改售后成本中心对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="costCenterFormRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="成本中心代码" prop="costCenterCode">
          <el-input v-model="form.costCenterCode" placeholder="请输入成本中心代码" />
        </el-form-item>
        <el-form-item label="成本中心名称" prop="costCenterName">
          <el-input v-model="form.costCenterName" placeholder="请输入成本中心名称" />
        </el-form-item>
        <el-form-item label="单价" prop="costCenterPrice">
          <el-input v-model="form.costCenterPrice" placeholder="请输入单价" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CostCenter" lang="ts">
import { addCostCenter, delCostCenter, getCostCenter, listCostCenter, updateCostCenter } from '@/api/mes/costCenter';
import { CostCenterForm, CostCenterQuery, CostCenterVO } from '@/api/mes/costCenter/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const costCenterList = ref<CostCenterVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const costCenterFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: CostCenterForm = {
  id: undefined,
  costCenterCode: undefined,
  costCenterName: undefined,
  costCenterPrice: undefined
};
const data = reactive<PageData<CostCenterForm, CostCenterQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    costCenterCode: undefined,
    costCenterName: undefined,
    costCenterPrice: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '不能为空', trigger: 'blur' }],
    costCenterCode: [{ required: true, message: '成本中心代码不能为空', trigger: 'blur' }],
    costCenterName: [{ required: true, message: '成本中心名称不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询售后成本中心列表 */
const getList = async () => {
  loading.value = true;
  const res = await listCostCenter(queryParams.value);
  costCenterList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  costCenterFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: CostCenterVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加售后成本中心';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: CostCenterVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getCostCenter(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改售后成本中心';
};

/** 提交按钮 */
const submitForm = () => {
  costCenterFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateCostCenter(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addCostCenter(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: CostCenterVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除售后成本中心编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delCostCenter(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mes/costCenter/export',
    {
      ...queryParams.value
    },
    `costCenter_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
