<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
            <el-form-item label="序号" prop="sortNum">
              <el-input v-model="queryParams.sortNum" placeholder="请输入序号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="动作代码" prop="actionCode">
              <el-input v-model="queryParams.actionCode" placeholder="请输入动作代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="动作名称" prop="actionName">
              <el-input v-model="queryParams.actionName" placeholder="请输入动作名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="部门代码" prop="deptCode">
              <el-input v-model="queryParams.deptCode" placeholder="请输入部门代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="部门名称" prop="deptName">
              <el-input v-model="queryParams.deptName" placeholder="请输入部门名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="在用" prop="isUse">
              <el-select v-model="queryParams.isUse" placeholder="请选择在用" clearable>
                <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="需同步SAP" prop="isSap">
              <el-select v-model="queryParams.isSap" placeholder="请选择需同步SAP" clearable>
                <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="需同步Master" prop="isMaster">
              <el-select v-model="queryParams.isMaster" placeholder="请选择需同步Master" clearable>
                <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="需要机床" prop="needMachin">
              <el-select v-model="queryParams.needMachin" placeholder="请选择需要机床" clearable>
                <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="需单独关单" prop="needClose">
              <el-select v-model="queryParams.needClose" placeholder="请选择需单独关单" clearable>
                <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="外发" prop="isOut">
              <el-select v-model="queryParams.isOut" placeholder="请选择外发" clearable>
                <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="多次报工" prop="isMult">
              <el-select v-model="queryParams.isMult" placeholder="请选择多次报工" clearable>
                <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mes:bgAction:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mes:bgAction:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mes:bgAction:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mes:bgAction:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="bgActionList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="" align="center" prop="id" v-if="true" />
        <el-table-column label="序号" align="center" prop="sortNum" />
        <el-table-column label="动作代码" align="center" prop="actionCode" />
        <el-table-column label="动作名称" align="center" prop="actionName" />
        <el-table-column label="部门代码" align="center" prop="deptCode" />
        <el-table-column label="部门名称" align="center" prop="deptName" />
        <el-table-column label="在用" align="center" prop="isUse">
          <template #default="scope">
            <dict-tag :options="is_use" :value="scope.row.isUse" />
          </template>
        </el-table-column>
        <el-table-column label="需同步SAP" align="center" prop="isSap">
          <template #default="scope">
            <dict-tag :options="is_use" :value="scope.row.isSap" />
          </template>
        </el-table-column>
        <el-table-column label="需同步Master" align="center" prop="isMaster">
          <template #default="scope">
            <dict-tag :options="is_use" :value="scope.row.isMaster" />
          </template>
        </el-table-column>
        <el-table-column label="需要机床" align="center" prop="needMachin">
          <template #default="scope">
            <dict-tag :options="is_use" :value="scope.row.needMachin" />
          </template>
        </el-table-column>
        <el-table-column label="需单独关单" align="center" prop="needClose">
          <template #default="scope">
            <dict-tag :options="is_use" :value="scope.row.needClose" />
          </template>
        </el-table-column>
        <el-table-column label="外发" align="center" prop="isOut">
          <template #default="scope">
            <dict-tag :options="is_use" :value="scope.row.isOut" />
          </template>
        </el-table-column>
        <el-table-column label="多次报工" align="center" prop="isMult">
          <template #default="scope">
            <dict-tag :options="is_use" :value="scope.row.isMult" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mes:bgAction:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mes:bgAction:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改报工动作对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="bgActionFormRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="序号" prop="sortNum">
          <el-input v-model="form.sortNum" placeholder="请输入序号" />
        </el-form-item>
        <el-form-item label="动作代码" prop="actionCode">
          <el-input v-model="form.actionCode" placeholder="请输入动作代码" />
        </el-form-item>
        <el-form-item label="动作名称" prop="actionName">
          <el-input v-model="form.actionName" placeholder="请输入动作名称" />
        </el-form-item>
        <el-form-item label="部门代码" prop="deptCode">
          <el-input v-model="form.deptCode" placeholder="请输入部门代码" />
        </el-form-item>
        <el-form-item label="部门名称" prop="deptName">
          <el-input v-model="form.deptName" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="在用" prop="isUse">
          <el-select v-model="form.isUse" placeholder="请选择在用">
            <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="需同步SAP" prop="isSap">
          <el-select v-model="form.isSap" placeholder="请选择需同步SAP">
            <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="需同步Master" prop="isMaster">
          <el-select v-model="form.isMaster" placeholder="请选择需同步Master">
            <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="需要机床" prop="needMachin">
          <el-select v-model="form.needMachin" placeholder="请选择需要机床">
            <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="需单独关单" prop="needClose">
          <el-select v-model="form.needClose" placeholder="请选择需单独关单">
            <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="外发" prop="isOut">
          <el-select v-model="form.isOut" placeholder="请选择外发">
            <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="多次报工" prop="isMult">
          <el-select v-model="form.isMult" placeholder="请选择多次报工">
            <el-option v-for="dict in is_use" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BgAction" lang="ts">
import { addBgAction, delBgAction, getBgAction, listBgAction, updateBgAction } from '@/api/mes/bgAction';
import { BgActionForm, BgActionQuery, BgActionVO } from '@/api/mes/bgAction/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { is_use } = toRefs<any>(proxy?.useDict('is_use'));

const bgActionList = ref<BgActionVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const bgActionFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: BgActionForm = {
  id: undefined,
  sortNum: undefined,
  actionCode: undefined,
  actionName: undefined,
  deptCode: undefined,
  deptName: undefined,
  isUse: undefined,
  isSap: undefined,
  isMaster: undefined,
  needMachin: undefined,
  needClose: undefined,
  isOut: undefined,
  isMult: undefined
};
const data = reactive<PageData<BgActionForm, BgActionQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    sortNum: undefined,
    actionCode: undefined,
    actionName: undefined,
    deptCode: undefined,
    deptName: undefined,
    isUse: undefined,
    isSap: undefined,
    isMaster: undefined,
    needMachin: undefined,
    needClose: undefined,
    isOut: undefined,
    isMult: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询报工动作列表 */
const getList = async () => {
  loading.value = true;
  const res = await listBgAction(queryParams.value);
  bgActionList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  bgActionFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: BgActionVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加报工动作';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: BgActionVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getBgAction(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改报工动作';
};

/** 提交按钮 */
const submitForm = () => {
  bgActionFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateBgAction(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addBgAction(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: BgActionVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除报工动作编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delBgAction(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mes/bgAction/export',
    {
      ...queryParams.value
    },
    `bgAction_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
