<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" :inline="true" label-width="68px" v-if="optType !== 'view'">
      <el-form-item label="工序编号" prop="processCode">
        <el-input v-model="queryParams.processCode" placeholder="请输入工序编号" clearable style="width: 200px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="工序名称" prop="processName">
        <el-input v-model="queryParams.processName" placeholder="请输入工序名称" clearable style="width: 200px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" v-if="optType !== 'view'">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mes:pro:routeprocess:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['mes:pro:routeprocess:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['mes:pro:routeprocess:remove']"
          >删除</el-button
        >
      </el-col>
      <!--      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mes:pro:routeprocess:export']">导出</el-button>
      </el-col>-->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" border :data="routeprocessList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="orderNum" width="80" />
      <el-table-column label="工序编号" align="center" prop="processCode" />
      <el-table-column label="工序名称" align="center" prop="processName" />
      <el-table-column label="下一道工序" width="150px" align="center" prop="nextProcessName" />
      <el-table-column label="与下一道工序关系" width="150px" align="center" prop="linkType">
        <template v-slot="scope">
          <dict-tag :options="mes_link_type" :value="scope.row.linkType" />
        </template>
      </el-table-column>
      <el-table-column label="关键工序" width="100px" align="center" prop="keyFlag">
        <template v-slot="scope">
          <dict-tag :options="sys_yes_no" :value="scope.row.keyFlag" />
        </template>
      </el-table-column>
      <el-table-column label="批量等待时间" align="center" prop="defaultPreTime" />
      <el-table-column label="等待时间" align="center" prop="defaultSufTime" />
      <el-table-column label="备注" align="center" prop="remark" />
      <!--      <el-table-column label="甘特图颜色" align="center" prop="colorCode" width="100">
        <template #default="scope">
          <el-color-picker v-model="scope.row.colorCode" disabled size="small" />
        </template>
      </el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" v-if="optType != 'view'">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mes:pro:routeprocess:edit']"></el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mes:pro:routeprocess:remove']"></el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改工艺组成对话框 -->
    <el-dialog :title="title" v-model="open" width="700px" append-to-body class="custom-dialog">
      <el-form ref="form" :model="formData" :rules="rules" label-width="120px" class="custom-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="序号" prop="orderNum">
              <div class="flex items-center">
                <el-input-number v-model="formData.orderNum" :min="1" controls-position="right" class="flex-1" />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工序" prop="processId" required>
              <el-select v-model="formData.processId" placeholder="请选择工序" class="w-full" @change="dynamicProcessEvent">
                <el-option v-for="item in processOptions" :key="item.processId" :label="item.processName" :value="item.processId">
                  <span style="float: left">{{ item.processName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ item.processCode }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="与下一道工序关系" prop="linkType">
              <el-select v-model="formData.linkType" placeholder="请选择" class="w-full">
                <el-option v-for="dict in mes_link_type" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关键工序" prop="keyFlag" required>
              <el-select v-model="formData.keyFlag" placeholder="请选择" class="w-full">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否检验" prop="isCheck">
              <el-select v-model="formData.isCheck" placeholder="请选择" class="w-full">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="急单时间" prop="urgWaitTime">
              <el-input-number v-model="formData.urgWaitTime" :min="0" controls-position="right" class="w-full" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="批量等待时间" prop="defaultPreTime">
              <el-input-number v-model="formData.defaultPreTime" :min="0" controls-position="right" class="w-full" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="等待时间" prop="defaultSufTime">
              <el-input-number v-model="formData.defaultSufTime" :min="0" controls-position="right" class="w-full" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="formData.remark" type="textarea" :rows="3" placeholder="请输入备注内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="RouteProcess">
import { getCurrentInstance, onMounted, ref, toRefs } from 'vue';
import type { FormInstance } from 'element-plus';
import { addRouteProcess, delRouteProcess, getRouteProcess, listRouteProcess, updateRouteProcess } from '@/api/mes/routeProcess';
import { listAllProcess } from '@/api/mes/process';

interface Props {
  routeId: string | number;
  processId?: string | number;
  optType: string;
}

interface RouteProcessForm {
  recordId?: string | number;
  routeId: string | number;
  processId?: string | number;
  processCode?: string;
  processName?: string;
  isCheck: string;
  orderNum: number;
  nextProcessId?: string | number;
  nextProcessCode?: string;
  nextProcessName?: string;
  linkType?: string;
  defaultPreTime: number;
  defaultSufTime: number;
  urgWaitTime: number;
  colorCode: string;
  keyFlag: string;
  remark?: string;
  attr1?: any;
  attr2?: any;
  attr3?: any;
  attr4?: any;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
}

interface QueryParams {
  pageNum: number;
  pageSize: number;
  routeId: string | number;
  processId?: string | number;
  processCode?: string;
  processName?: string;
}

const props = defineProps<Props>();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { mes_link_type, sys_yes_no } = toRefs<any>(proxy?.useDict('mes_link_type', 'sys_yes_no'));

const loading = ref(false);
const ids = ref([]);
const total = ref(0);
const open = ref(false);
const title = ref('');
const single = ref(true);
const multiple = ref(true);
const routeprocessList = ref([]);
const processOptions = ref([]);
const showSearch = ref(true);

const queryForm = ref<FormInstance>();
const form = ref<FormInstance>();
const queryParams = ref<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  routeId: props.routeId,
  processId: undefined,
  processCode: undefined,
  processName: undefined
});

const initFormData: RouteProcessForm = {
  routeId: props.routeId,
  processId: props.processId,
  isCheck: 'N',
  orderNum: 1,
  keyFlag: 'N',
  defaultPreTime: 0,
  defaultSufTime: 0,
  urgWaitTime: 0,
  colorCode: '#00AEF3'
};

const formData = ref<RouteProcessForm>({ ...initFormData });

const rules = ref({
  routeId: [{ required: true, message: '工艺路线ID不能为空', trigger: 'blur' }],
  processId: [{ required: true, message: '工序ID不能为空', trigger: 'change' }],
  keyFlag: [{ required: true, message: '请指定当前工序是否关键工序', trigger: 'change' }],
  isCheck: [{ required: true, message: '请指定当前工序是否需要质检确认', trigger: 'change' }],
  linkType: [{ required: true, message: '请选择与下一道工序关系', trigger: 'change' }],
  remark: [{ max: 250, message: '字段过长', trigger: 'blur' }]
});

/** 查询工艺组成列表 */
const getList = async () => {
  loading.value = true;
  const res = await listRouteProcess(queryParams.value);
  routeprocessList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 查询工序信息 */
const getProcess = async () => {
  const res = await listAllProcess();
  processOptions.value = res.data;
};

/** 工序切换带出信息 */
const dynamicProcessEvent = async (processId) => {
  // 从processOptions中找到对应的工序对象
  const selectedProcess = processOptions.value.find((item) => item.processId === processId);
  if (selectedProcess) {
    // 获取当前选择工序数据的时间信息
    formData.value.defaultPreTime = selectedProcess.batchWaitTime || 0;
    formData.value.defaultSufTime = selectedProcess.waitTime || 0;
    formData.value.urgWaitTime = selectedProcess.urgWaitTime || 0;
    formData.value.remark = selectedProcess.description || '';
  }
};

// 取消按钮
const cancel = () => {
  open.value = false;
  reset();
};

// 表单重置
const reset = () => {
  formData.value = {
    routeId: props.routeId,
    isCheck: 'N',
    orderNum: 1,
    keyFlag: 'N',
    defaultPreTime: 0,
    defaultSufTime: 0,
    urgWaitTime: 0,
    colorCode: '#00AEF3'
  };
  form.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryForm.value?.resetFields();
  handleQuery();
};

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.recordId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  if (props.processId) {
    formData.value.processId = props.processId;
  }
  open.value = true;
  title.value = '添加工艺组成';
};

/** 修改按钮操作 */
const handleUpdate = async (row) => {
  reset();
  const recordId = row.recordId || ids.value;
  const res = await getRouteProcess(recordId);
  formData.value = res.data;
  open.value = true;
  title.value = '修改工艺组成';
};

/** 提交按钮 */
const submitForm = () => {
  form.value?.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (formData.value.recordId != null) {
          await updateRouteProcess(formData.value);
          proxy?.$modal.msgSuccess('修改成功');
        } else {
          await addRouteProcess(formData.value);
          proxy?.$modal.msgSuccess('新增成功');
        }
        open.value = false;
        getList();
      } catch (error) {
        console.error('提交失败:', error);
      }
    } else {
      console.error('表单验证失败');
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row) => {
  const recordIds = row.recordId || ids.value;
  await proxy?.$modal.confirm('是否确认删除工艺组成编号为"' + recordIds + '"的数据项？');
  await delRouteProcess(recordIds);
  getList();
  proxy?.$modal.msgSuccess('删除成功');
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mes/pro/routeprocess/export',
    {
      ...queryParams.value
    },
    `routeprocess_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
  getProcess();
});
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.custom-dialog {
  .el-dialog__body {
    padding: 20px 30px;
  }

  .el-dialog__header {
    margin-right: 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
    padding: 15px 20px;
  }

  .el-dialog__footer {
    border-top: 1px solid var(--el-border-color-lighter);
    padding: 15px 20px;
  }
}

.custom-form {
  .el-form-item {
    margin-bottom: 20px;

    &__label {
      font-weight: normal;
      color: var(--el-text-color-regular);
    }
  }

  .el-input-number {
    width: 100%;
  }

  .el-select {
    width: 100%;
  }

  .flex {
    display: flex;
  }

  .items-center {
    align-items: center;
  }

  .w-full {
    width: 100%;
  }

  .ml-2 {
    margin-left: 8px;
  }
}

.dialog-footer {
  text-align: right;

  .el-button {
    min-width: 100px;

    & + .el-button {
      margin-left: 12px;
    }
  }
}
</style>

<script lang="ts">
export default defineComponent({
  name: 'RouteProcess'
});
</script>
