<template>
  <div class="p-4">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-4">
        <el-card shadow="hover" class="search-card">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form">
            <el-form-item label="路线编号" prop="routeCode" for="queryRouteCode">
              <el-input
                id="queryRouteCode"
                v-model="queryParams.routeCode"
                placeholder="请输入路线编号"
                clearable
                @keyup.enter="handleQuery"
                class="w-64"
              />
            </el-form-item>
            <el-form-item label="路线名称" prop="routeName" for="queryRouteName">
              <el-input
                id="queryRouteName"
                v-model="queryParams.routeName"
                placeholder="请输入路线名称"
                clearable
                @keyup.enter="handleQuery"
                class="w-64"
              />
            </el-form-item>
            <el-form-item label="是否启用" prop="enableFlag" for="queryEnableFlag">
              <el-select id="queryEnableFlag" v-model="queryParams.enableFlag" placeholder="请选择是否启用" clearable class="w-64">
                <el-option v-for="dict in sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery" class="mr-2">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never" class="main-card">
      <template #header>
        <el-row :gutter="10" class="mb-4">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mes:route:add']" class="action-button">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @click="handleUpdate()"
              v-hasPermi="['mes:route:edit']"
              class="action-button"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete()"
              v-hasPermi="['mes:route:remove']"
              class="action-button"
              >删除</el-button
            >
          </el-col>
          <!--          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mes:route:export']" class="action-button"
              >导出</el-button
            >
          </el-col>-->
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="routeList" @selection-change="handleSelectionChange" class="custom-table">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="路线编号" align="center" prop="routeCode" min-width="120">
          <template #default="scope">
            <el-button link type="primary" @click="handleView(scope.row)" v-hasPermi="['mes:route:query']" class="link-button">{{
              scope.row.routeCode
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="路线名称" align="center" prop="routeName" min-width="120" />
        <el-table-column label="路线说明" align="center" prop="routeDesc" min-width="150" show-overflow-tooltip />
        <el-table-column label="是否启用" align="center" prop="enableFlag" width="80">
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.enableFlag" />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作" align="center" width="120" fixed="right">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['mes:route:edit']"
                class="action-icon-button"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button
                link
                type="primary"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['mes:route:remove']"
                class="action-icon-button"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        class="mt-4"
      />
    </el-card>

    <!-- 添加或修改路线对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="1080px" append-to-body class="custom-dialog">
      <el-form ref="routeFormRef" :model="form" :rules="rules" label-width="100px" class="custom-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="编号" prop="routeCode" for="formRouteCode">
              <div class="flex items-center">
                <el-input id="formRouteCode" v-model="form.routeCode" placeholder="请输入路线编号" class="custom-input flex-1" />
                <el-switch
                  id="autoGenSwitch"
                  v-model="autoGenFlag"
                  active-color="#13ce66"
                  active-text="自动生成"
                  @change="handleAutoGenChange"
                  v-if="optType !== 'view'"
                  class="custom-switch ml-2"
                />
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="名称" prop="routeName" for="formRouteName">
              <el-input id="formRouteName" v-model="form.routeName" placeholder="请输入路线名称" class="custom-input" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否启用" prop="enableFlag" for="formEnableFlag">
              <el-radio-group id="formEnableFlag" v-model="form.enableFlag" :disabled="optType === 'view'" class="custom-radio-group">
                <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="说明" prop="routeDesc" for="formRouteDesc">
              <el-input id="formRouteDesc" v-model="form.routeDesc" type="textarea" :rows="3" placeholder="请输入内容" class="custom-textarea" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark" for="formRemark">
              <el-input id="formRemark" v-model="form.remark" type="textarea" :rows="3" placeholder="请输入内容" class="custom-textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-tabs type="border-card" v-if="form.routeId" class="custom-tabs">
        <el-tab-pane label="组成工序">
          <RouteProcess v-if="form.routeId" :optType="optType" :routeId="form.routeId" />
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div class="dialog-footer" v-if="optType !== 'view'">
          <el-button type="primary" @click="submitForm" class="submit-button">确 定</el-button>
          <el-button @click="cancel" class="cancel-button">取 消</el-button>
        </div>
        <div class="dialog-footer" v-else>
          <el-button @click="cancel" class="cancel-button">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Route" lang="ts">
import { addRoute, delRoute, getRoute, listRoute, updateRoute } from '@/api/mes/route';
import { RouteForm, RouteQuery, RouteVO } from '@/api/mes/route/types';
import { genCode } from '@/api/system/autocode';
import RouteProcess from './routeprocess.vue';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_yes_no } = toRefs<any>(proxy?.useDict('sys_yes_no'));

const routeList = ref<RouteVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const autoGenFlag = ref(false);
const optType = ref<string>();

const queryFormRef = ref<ElFormInstance>();
const routeFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: RouteForm = {
  routeId: undefined,
  routeCode: undefined,
  routeName: undefined,
  routeDesc: undefined,
  enableFlag: 'Y',
  remark: undefined
};

const data = reactive<PageData<RouteForm, RouteQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    routeCode: undefined,
    routeName: undefined,
    routeDesc: undefined,
    enableFlag: undefined,
    params: {}
  },
  rules: {
    routeCode: [
      { required: true, message: '路线编号不能为空', trigger: 'blur' },
      { max: 64, message: '字段过长', trigger: 'blur' }
    ],
    routeName: [
      { required: true, message: '路线名称不能为空', trigger: 'blur' },
      { max: 100, message: '字段过长', trigger: 'blur' }
    ],
    enableFlag: [{ required: true, message: '是否启用不能为空', trigger: 'blur' }],
    remark: [{ max: 250, message: '长度必须小于250个字符', trigger: 'blur' }],
    routeDesc: [{ max: 250, message: '字段过长', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询路线列表 */
const getList = async () => {
  loading.value = true;
  const res = await listRoute(queryParams.value);
  routeList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  autoGenFlag.value = false;
  routeFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: RouteVO[]) => {
  ids.value = selection.map((item) => item.routeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加路线';
  optType.value = 'add';
};

/** 查看按钮操作 */
const handleView = async (row: RouteVO) => {
  reset();
  const routeId = row.routeId || ids.value[0];
  const res = await getRoute(routeId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '查看工艺线路信息';
  optType.value = 'view';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: RouteVO) => {
  reset();
  const routeId = row?.routeId || ids.value[0];
  const res = await getRoute(routeId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改路线';
  optType.value = 'edit';
};

/** 提交按钮 */
const submitForm = () => {
  routeFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.routeId) {
        await updateRoute(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addRoute(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: RouteVO) => {
  const routeIds = row?.routeId || ids.value;
  await proxy?.$modal.confirm('是否确认删除路线编号为"' + routeIds + '"的数据项？').finally(() => (loading.value = false));
  await delRoute(routeIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mes/route/export',
    {
      ...queryParams.value
    },
    `route_${new Date().getTime()}.xlsx`
  );
};

/** 自动生成编码 */
const handleAutoGenChange = async (autoGenFlag: boolean) => {
  if (autoGenFlag) {
    const response = await genCode('ROUTE_CODE');
    form.value.routeCode = response.data;
  } else {
    form.value.routeCode = undefined;
  }
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.p-4 {
  padding: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.w-64 {
  width: 16rem;
}

.search-card {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.main-card {
  border-radius: 0.5rem;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.action-button {
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.custom-table {
  border-radius: 0.5rem;
  overflow: hidden;

  :deep(.el-table__header) {
    background-color: #f5f7fa;
  }

  :deep(.el-table__row) {
    transition: background-color 0.3s;

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.link-button {
  font-weight: 500;
  transition: color 0.3s;

  &:hover {
    color: #409eff;
  }
}

.action-icon-button {
  padding: 0.25rem;
  transition: transform 0.3s;

  &:hover {
    transform: scale(1.1);
  }
}

.custom-dialog {
  :deep(.el-dialog__header) {
    padding: 1.5rem;
    border-bottom: 1px solid #e4e7ed;
  }

  :deep(.el-dialog__body) {
    padding: 1.5rem;
  }

  :deep(.el-dialog__footer) {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e4e7ed;
  }
}

.custom-form {
  .el-form-item {
    margin-bottom: 1.5rem;
  }
}

.custom-input {
  :deep(.el-input__wrapper) {
    border-radius: 0.25rem;
  }
}

.custom-textarea {
  :deep(.el-textarea__inner) {
    border-radius: 0.25rem;
    resize: vertical;
  }
}

.custom-switch {
  :deep(.el-switch__core) {
    border-radius: 1rem;
  }

  :deep(.el-switch__label) {
    font-size: 0.875rem;
  }
}

.custom-radio-group {
  display: flex;
  gap: 1rem;
}

.custom-tabs {
  border-radius: 0.5rem;
  overflow: hidden;
  margin-top: 1rem;

  :deep(.el-tabs__header) {
    margin-bottom: 1rem;
  }
}

.submit-button {
  padding: 0.5rem 2rem;
  border-radius: 0.25rem;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.cancel-button {
  padding: 0.5rem 2rem;
  border-radius: 0.25rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.flex-1 {
  flex: 1;
}

.ml-2 {
  margin-left: 0.5rem;
}
</style>
