<template>
  <div class="assemb-update-container">
    <!-- 表单部分 -->
    <el-form :model="formData" ref="formRef" label-position="right" label-width="80px" class="main-form">
      <input type="hidden" v-model="formData.id" />
      <input type="hidden" v-model="formData.deilMap" />
      <input type="hidden" v-model="formData.technologylAll" />
      <input type="hidden" v-model="formData.arbpl" />

      <div class="form-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="动作:" class="custom-form-item">
              <div class="input-with-prefix" @click="isClear(1)">
<!--                <img :src="actionLocked ? lockedImg : unlockedImg" alt="" class="lock-icon">-->
                <el-input v-model="formData.actionCode" class="scan-input" placeholder="请输入" @keyup.enter="getActionList"></el-input>
                <el-input v-model="formData.actionName" placeholder="动作名称" readonly></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作员:" class="custom-form-item">
              <div class="input-with-prefix" @click="isClear(2)">
<!--                <img :src="opLocked ? lockedImg : unlockedImg" alt="" class="lock-icon">-->
                <el-input v-model="formData.opCode" class="scan-input" placeholder="请输入" @keyup.enter="getUserScanInfo"></el-input>
                <el-input v-model="formData.opName" placeholder="姓名" readonly class="small-input"></el-input>
                <el-input v-model="formData.deptCode" placeholder="部门" readonly class="small-input"></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工单号:" class="custom-form-item">
              <div class="input-with-prefix">
                <el-input v-model="formData.moNo" class="scan-input medium-input" placeholder="请输入" @keyup.enter="scanMoNo"></el-input>
                <el-input v-model="formData.wpCode" placeholder="工序" readonly class="small-input"></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总人数:" class="custom-form-item">
              <el-input v-model="formData.empTotal" placeholder="请输入" readonly></el-input>
              <el-input v-model="formData.custCode" placeholder="客户代码" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备名称:" class="custom-form-item">
              <el-input v-model="formData.machinDesc" placeholder="请输入" readonly></el-input>
              <el-input v-model="formData.partVersion" placeholder="图号版本" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单数量:" class="custom-form-item">
              <el-input v-model="formData.moQty" placeholder="请输入" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="送货单:" class="custom-form-item">
              <el-input v-model="formData.derno" placeholder="请输入" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报工日期:" class="custom-form-item">
              <el-date-picker v-model="formData.reportDate" type="date" placeholder="请输入" readonly @click="getData"></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <div class="button-group">
          <el-button @click="clearAll">清空</el-button>
          <el-button type="primary" @click="confirmAll(1)">确定</el-button>
        </div>
      </div>
    </el-form>

    <!-- 表格部分 -->
    <div class="tables-container">
      <div class="main-table">
        <div class="table-header">
          <el-button icon="Delete" @click="delRow(1)">删除</el-button>
          <div class="input-group">
            <span class="label">合格数量:</span>
            <el-input v-model="formData.passQty" @input="currentPassQty" data-q="passQty" readonly class="mini-input"></el-input>
          </div>
          <div class="input-group">
            <span class="label">报废数量:</span>
            <el-input v-model="formData.scrapQty" @input="currentPassQty" data-q="scrapQty" readonly class="mini-input"></el-input>
          </div>
          <div class="input-group">
            <span class="label">当前进度:</span>
            <el-input v-model="formData.progressCurrent" maxlength="3" readonly @input="currentChange" class="mini-input"></el-input>
          </div>
          <div class="input-group">
            <span class="label">总进度:</span>
            <el-input v-model="formData.progressTotal" readonly class="mini-input" value="0"></el-input>
          </div>
          <div class="input-group">
            <span class="label">备注:</span>
            <el-input v-model="formData.remark" class="remark-input"></el-input>
          </div>
        </div>

        <el-table
          ref="table1Ref"
          :data="tableData1"
          height="calc(100vh - 340px)"
          @selection-change="handleSelectionChange1"
          :default-sort="{prop: 'startDate', order: 'descending'}"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="id" label="id" v-if="false"></el-table-column>
          <el-table-column prop="moNo" label="工单号"></el-table-column>
          <el-table-column prop="wpCode" label="工序号"></el-table-column>
          <el-table-column prop="wpName" label="工序名称"></el-table-column>
          <el-table-column prop="status" label="单据状态">
            <template #default="scope">
              <span>{{ formatStatus(scope.row.status) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="opName" label="操作员"></el-table-column>
          <el-table-column prop="actionCode" label="动作"></el-table-column>
          <el-table-column prop="actionName" label="动作名称"></el-table-column>
          <el-table-column prop="startDate" label="开始时间" sortable></el-table-column>
          <el-table-column prop="endDate" label="结束时间"></el-table-column>
          <el-table-column prop="passQty" label="合格数量"></el-table-column>
          <el-table-column prop="scrapQty" label="报废数量"></el-table-column>
          <el-table-column prop="progressCurrent" label="当前进度"></el-table-column>
          <el-table-column prop="progressTotal" label="总进度"></el-table-column>
          <el-table-column prop="procressTime" label="工艺工时"></el-table-column>
          <el-table-column prop="actualTime" label="实际时间"></el-table-column>
          <el-table-column prop="empTotal" label="总人数"></el-table-column>
        </el-table>
      </div>

      <div class="person-table">
        <div class="table-header">
          <el-button icon="Delete" @click="delRow(2)">删除</el-button>
          <div class="input-group">
            <span class="label">参与人员:</span>
            <el-input v-model="userCode" placeholder="请输入" class="scan-input" @keyup.enter="addParticipant"></el-input>
          </div>
        </div>

        <el-table
          ref="table2Ref"
          :data="tableData2"
          height="calc(100vh - 340px)"
          @selection-change="handleSelectionChange2"
          :default-sort="{prop: 'id', order: 'descending'}"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="userCode" label="工号"></el-table-column>
          <el-table-column prop="userName" label="姓名"></el-table-column>
          <el-table-column prop="actualTime" label="实际工时">
            <template #default="scope">
              <el-input v-model="scope.row.actualTime" @blur="saveData(scope.$index, 'actualTime', scope.row.actualTime)" class="edit-cell"></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import {
  getActionListDo,
  getBgUserInfoDo,
  getUserScanInfoDo,
  scanMoNoDo,
  updateTechnologyFileDo
} from '@/api/mes/bg/assemb.ts';

// 图片资源
// const lockedImg = new URL('@/assets/images/locked.png', import.meta.url).href;
// const unlockedImg = new URL('@/assets/images/unlocked.png', import.meta.url).href;

// 表单数据
const formRef = ref(null);
const formData = reactive({
  id: '',
  deilMap: '',
  technologylAll: '',
  arbpl: '',
  actionCode: '',
  actionName: '',
  opCode: '',
  opName: '',
  deptCode: '',
  moNo: '',
  wpCode: '',
  empTotal: '0',
  custCode: '',
  machinDesc: '',
  partVersion: '',
  moQty: '',
  derno: '',
  reportDate: '',
  passQty: '',
  scrapQty: '',
  progressCurrent: '',
  progressTotal: '0',
  remark: ''
});

// 表格数据
const tableData1 = ref([]);
const tableData2 = ref([]);
const table1Ref = ref(null);
const table2Ref = ref(null);
const selectedRows1 = ref([]);
const selectedRows2 = ref([]);
const userCode = ref('');

// 锁定状态
const actionLocked = ref(true);
const opLocked = ref(true);

// 初始化
onMounted(() => {
  init(1);
  scanMoNo(0);
});

// 方法转换
const init = (type) => {
  // 初始化逻辑...
  console.log('初始化类型:', type);
};

// 锁定/解锁字段
const isClear = (type) => {
  if (type === 1) {
    actionLocked.value = !actionLocked.value;
  } else if (type === 2) {
    opLocked.value = !opLocked.value;
  }
};

// 获取动作列表
const getActionList = () => {
  if (!formData.actionCode) return;

  getActionListDo({
    actionCode: formData.actionCode
  }).then(res => {
    if (res.success) {
      formData.actionName = res.data.actionName || '';
      // 其他字段处理...
    } else {
      ElMessage.warning(res.message || '获取动作失败');
    }
  }).catch(() => {
    ElMessage.error('网络请求失败');
  });
};

// 获取用户信息
const getUserScanInfo = () => {
  if (!formData.opCode) return;

  getUserScanInfoDo({
    opCode: formData.opCode
  }).then(res => {
    if (res.success) {
      formData.opName = res.data.opName || '';
      formData.deptCode = res.data.deptCode || '';
      // 其他字段处理...
    } else {
      ElMessage.warning(res.message || '获取用户信息失败');
    }
  }).catch(() => {
    ElMessage.error('网络请求失败');
  });
};

// 扫描工单号
const scanMoNo = (type) => {
  if (!formData.moNo && type !== 0) return;

  scanMoNoDo({
    moNo: formData.moNo,
    type: type
  }).then(res => {
    if (res.success) {
      // 处理返回数据
      formData.wpCode = res.data.wpCode || '';
      formData.moQty = res.data.moQty || '';
      // 加载表格数据
      tableData1.value = res.data.list || [];
    } else {
      ElMessage.warning(res.message || '扫描工单失败');
    }
  }).catch(() => {
    ElMessage.error('网络请求失败');
  });
};

// 添加参与人员
const addParticipant = () => {
  if (!userCode.value) return;

  const scanId = selectedRows1.value.length > 0 ? selectedRows1.value[0].id : '';

  if (!scanId) {
    ElMessage.warning('请先选择一条关单数据再操作');
    return false;
  }

  if (selectedRows1.value.length > 1) {
    ElMessage.warning('只能选择一条关单数据');
    userCode.value = '';
    return false;
  }

  getBgUserInfoDo({
    scanId: scanId,
    userCode: userCode.value.trim()
  }).then(res => {
    if (res.success) {
      const isExist = tableData2.value.some(item => item.userCode === res.data.user.userCode);
      if (!isExist) {
        tableData2.value.push(res.data.user);
        formData.empTotal = tableData2.value.length;
      }
    } else {
      ElMessage.warning(res.message || '获取用户信息失败');
    }
    userCode.value = '';
  }).catch(() => {
    ElMessage.error('网络请求失败');
    userCode.value = '';
  });
};

// 保存数据
const saveData = (index, field, value) => {
  if (!tableData2.value[index]) return;
  tableData2.value[index][field] = value;
};

// 处理表格选择
const handleSelectionChange1 = (selection) => {
  selectedRows1.value = selection;
};

const handleSelectionChange2 = (selection) => {
  selectedRows2.value = selection;
};

// 删除行
const delRow = (tableType) => {
  if (tableType === 1) {
    const selectedIds = selectedRows1.value.map(row => row.id);
    tableData1.value = tableData1.value.filter(row => !selectedIds.includes(row.id));
  } else if (tableType === 2) {
    const selectedIndices = selectedRows2.value.map(row => {
      return tableData2.value.findIndex(item => item.userCode === row.userCode);
    });

    for (let i = selectedIndices.length - 1; i >= 0; i--) {
      if (selectedIndices[i] !== -1) {
        tableData2.value.splice(selectedIndices[i], 1);
      }
    }

    formData.empTotal = tableData2.value.length;
  }
};

// 清空所有数据
const clearAll = () => {
  Object.keys(formData).forEach(key => {
    if (key !== 'progressTotal' && key !== 'empTotal') {
      formData[key] = '';
    }
  });
  formData.progressTotal = '0';
  formData.empTotal = '0';
  tableData1.value = [];
  tableData2.value = [];
};

// 确认所有数据
const confirmAll = (type) => {
  // 表单验证和数据准备
  const deilMap = JSON.stringify(tableData1.value);
  const technologylAll = JSON.stringify(tableData2.value);

  // 设置隐藏字段
  formData.deilMap = deilMap;
  formData.technologylAll = technologylAll;

  updateTechnologyFileDo(formData).then(res => {
    if (res.success) {
      ElMessage.success('操作成功');
      clearAll(); // 成功后清空表单
    } else {
      ElMessage.warning(res.message || '操作失败');
    }
  }).catch(() => {
    ElMessage.error('网络请求失败');
  });
};

// 获取日期
const getData = () => {
  // 设置当前日期
  formData.reportDate = new Date();
};

// 当前数量变更
const currentPassQty = (e) => {
  const target = e.target;
  const dataQ = target.getAttribute('data-q');
  console.log('数量变更:', dataQ, target.value);
};

// 当前进度变更
const currentChange = () => {
  console.log('进度变更:', formData.progressCurrent);
};

// 格式化状态
const formatStatus = (value) => {
  if (value === undefined) return '';
  if (value === '0') return '新开';
  if (value === '1') return '待关';
  if (value === '2') return '已关';
  return value;
};
</script>

<style scoped>
.assemb-update-container {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.main-form {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.form-content {
  margin-bottom: 20px;
}

.custom-form-item {
  margin-bottom: 18px;
}

.input-with-prefix {
  display: flex;
  align-items: center;
}

.lock-icon {
  width: 14px;
  margin-right: 5px;
  cursor: pointer;
}

.scan-input {
  background-repeat: no-repeat;
  background-position: 95% center;
  background-size: 15px;
  margin-right: 10px;
}

.small-input {
  width: 80px;
  margin-right: 10px;
}

.medium-input {
  width: 180px;
}

.button-group {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.tables-container {
  display: flex;
  flex: 1;
  margin-top: 20px;
}

.main-table {
  width: 80%;
  margin-right: 20px;
}

.person-table {
  width: 20%;
}

.table-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.input-group {
  display: flex;
  align-items: center;
  margin-left: 15px;
  margin-bottom: 10px;
}

.label {
  width: 70px;
  font-weight: bold;
  text-align: right;
  margin-right: 5px;
}

.mini-input {
  width: 60px;
}

.remark-input {
  width: 150px;
}

.edit-cell {
  color: blue;
}

/* 表格选中行的样式 */
:deep(.el-table .el-table__row.selected) {
  background-color: #FFE2C1;
}
</style>
