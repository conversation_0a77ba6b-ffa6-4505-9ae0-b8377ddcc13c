<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <!--            <el-form-item label="MO号" prop="moNumber">
              <el-input v-model="queryParams.moNumber" placeholder="请输入MO号" clearable @keyup.enter="handleQuery" />
            </el-form-item>-->
            <el-form-item label="图号" prop="noNumber">
              <el-input v-model="queryParams.noNumber" placeholder="请输入图号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mes:convertRecord:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mes:convertRecord:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mes:convertRecord:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mes:convertRecord:export']">导出</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Download" @click="handleBatchGenerate" v-hasPermi="['mes:convertRecord:batchGenerate']"
              >批量转图</el-button
            >
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="convertRecordList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="主键" align="center" prop="id" v-if="true" />
        <!--        <el-table-column label="MO号" align="center" prop="moNumber" />-->
        <el-table-column label="NO号" align="center" prop="noNumber" />
        <el-table-column label="原始文件名" align="center" prop="originalFileName" />
        <el-table-column label="转换后文件名" align="center" prop="convertedFileName" />
        <el-table-column label="状态" align="center" prop="status">
          <template #default="scope">
            {{ formatStatus(scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column label="错误信息" align="center" prop="errorMessage" />
        <el-table-column label="文件大小" align="center" prop="fileSize">
          <template #default="scope">
            {{ formatFileSize(scope.row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mes:convertRecord:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="下载ZIP" placement="top">
              <el-button
                link
                type="success"
                icon="Download"
                @click="handleDownloadSingle(scope.row)"
                v-hasPermi="['mes:convertRecord:download']"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mes:convertRecord:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改PDF转换记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="convertRecordFormRef" :model="form" :rules="rules" label-width="80px">
        <!--        <el-form-item label="MO号" prop="moNumber">
          <el-input v-model="form.moNumber" placeholder="请输入MO号" />
        </el-form-item>-->
        <el-form-item label="图号" prop="noNumber">
          <el-input v-model="form.noNumber" placeholder="请输入图号" />
        </el-form-item>
        <el-form-item label="原始文件名" prop="originalFileName">
          <el-input v-model="form.originalFileName" placeholder="请输入原始文件名" />
        </el-form-item>
        <el-form-item label="转换后文件名" prop="convertedFileName">
          <el-input v-model="form.convertedFileName" placeholder="请输入转换后文件名" />
        </el-form-item>
        <el-form-item label="ZIP文件路径" prop="zipFilePath">
          <el-input v-model="form.zipFilePath" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="ZIP文件URL" prop="zipFileUrl">
          <el-input v-model="form.zipFileUrl" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="错误信息" prop="errorMessage">
          <el-input v-model="form.errorMessage" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="文件大小" prop="fileSize">
          <el-input v-model="form.fileSize" placeholder="请输入文件大小" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量生成ZIP对话框 -->
    <el-dialog title="批量转图" v-model="batchDialog.visible" width="400px" append-to-body>
      <el-form ref="batchFormRef" :model="batchForm" :rules="batchRules" label-width="80px">
        <!--        <el-form-item label="MO号" prop="moNumber">
          <el-input
            v-model="batchForm.moNumber"
            type="textarea"
            :rows="4"
            placeholder="请输入MO号，多个MO号请用中英文逗号分号分隔\n例如：MO001,MO002 或 MO001;MO002 或 MO001，MO002"
            clearable
          />
        </el-form-item>-->
        <el-form-item label="图号" prop="noNumber">
          <el-input
            v-model="batchForm.noNumber"
            type="textarea"
            :rows="4"
            placeholder="请输入图号，多个图号请用中英文逗号分号分隔\n例如：NO001,NO002 或 NO001;NO002 或 NO001，NO002"
            clearable
          />
        </el-form-item>
        <el-form-item label="缩放比例" prop="scale">
          <el-input-number
            v-model="batchForm.scale"
            :min="0.1"
            :max="1"
            :step="0.01"
            :precision="2"
            placeholder="请输入缩放比例"
            style="width: 200px"
          />
          <div class="text-sm text-gray-500 mt-1">设置PDF转换的缩放比例，范围0.1-1.0，默认0.94（94%）</div>
        </el-form-item>
        <el-alert title="提示：请至少填写一个图号，支持批量输入（中英文逗号分号分隔）" type="info" :closable="false" show-icon class="mb-4" />
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="batchLoading" type="primary" @click="submitBatchGenerate">生成并下载</el-button>
          <el-button @click="cancelBatch">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ConvertRecord" lang="ts">
import { listConvertRecord, getConvertRecord, delConvertRecord, addConvertRecord, updateConvertRecord } from '@/api/mes/convertRecord';
import { ConvertRecordVO, ConvertRecordQuery, ConvertRecordForm } from '@/api/mes/convertRecord/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const convertRecordList = ref<ConvertRecordVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const batchLoading = ref(false);

const queryFormRef = ref<ElFormInstance>();
const convertRecordFormRef = ref<ElFormInstance>();
const batchFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const batchDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ConvertRecordForm = {
  id: undefined,
  moNumber: undefined,
  noNumber: undefined,
  originalFileName: undefined,
  convertedFileName: undefined,
  zipFilePath: undefined,
  zipFileUrl: undefined,
  status: undefined,
  errorMessage: undefined,
  fileSize: undefined
};

const initBatchFormData = {
  moNumber: undefined,
  noNumber: undefined,
  scale: 0.94
};
const data = reactive<PageData<ConvertRecordForm, ConvertRecordQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    moNumber: undefined,
    noNumber: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '主键不能为空', trigger: 'blur' }],
    moNumber: [{ required: true, message: 'MO号不能为空', trigger: 'blur' }]
  }
});

const batchData = reactive({
  form: { ...initBatchFormData },
  rules: {
    moNumber: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!value && !batchForm.value.noNumber) {
            callback(new Error('请至少一个图号'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    noNumber: [
      {
        validator: (rule: any, value: any, callback: any) => {
          if (!value && !batchForm.value.moNumber) {
            callback(new Error('请至少填写一个图号'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    scale: [
      { required: true, message: '缩放比例不能为空', trigger: 'blur' },
      { type: 'number', min: 0.1, max: 1, message: '缩放比例必须在0.1-1.0之间', trigger: 'blur' }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);
const { form: batchForm, rules: batchRules } = toRefs(batchData);

/** 查询PDF转换记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listConvertRecord(queryParams.value);
  convertRecordList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  convertRecordFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ConvertRecordVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加PDF转换记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ConvertRecordVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getConvertRecord(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改PDF转换记录';
};

/** 提交按钮 */
const submitForm = () => {
  convertRecordFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateConvertRecord(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addConvertRecord(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ConvertRecordVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除PDF转换记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delConvertRecord(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mes/convertRecord/export',
    {
      ...queryParams.value
    },
    `convertRecord_${new Date().getTime()}.xlsx`
  );
};

/** 批量生成ZIP按钮操作 */
const handleBatchGenerate = () => {
  batchForm.value = { ...initBatchFormData };
  batchFormRef.value?.resetFields();
  batchDialog.visible = true;
};

/** 取消批量生成 */
const cancelBatch = () => {
  batchForm.value = { ...initBatchFormData };
  batchFormRef.value?.resetFields();
  batchDialog.visible = false;
};

/** 提交批量生成 */
const submitBatchGenerate = () => {
  batchFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      batchLoading.value = true;
      try {
        // 处理多批量输入，将中英文逗号分号分隔的字符串转换为数组再转为字符串
        const processBatchInput = (input: string | undefined) => {
          if (!input) return undefined;
          // 按中英文逗号分号分割，去除空白字符
          return input
            .split(/[,;，；]+/) // 按中英文逗号分号分割
            .map((item) => item.trim()) // 去除首尾空白
            .filter((item) => item.length > 0) // 过滤空字符串
            .join(','); // 用逗号连接
        };

        // 调用后台API生成ZIP包并下载
        const params = {
          mos: processBatchInput(batchForm.value.moNumber),
          nos: processBatchInput(batchForm.value.noNumber),
          scale: batchForm.value.scale
        };
        // 使用proxy下载方法调用后台API
        proxy?.download('mes/convertRecord/batchDownloadAndConvertByMoOrNoWithZip', params, `convert_${new Date().getTime()}.zip`);
        proxy?.$modal.msgSuccess('ZIP包生成成功，正在下载...');
        batchDialog.visible = false;
        // 刷新列表
        await getList();
      } catch (error) {
        proxy?.$modal.msgError('ZIP包生成失败，请重试');
      } finally {
        batchLoading.value = false;
      }
    }
  });
};

/** 单独下载ZIP */
const handleDownloadSingle = (row: ConvertRecordVO) => {
  try {
    // 构建下载参数，优先使用MO号，如果没有则使用NO号
    const params = {
      mos: row.moNumber || undefined,
      nos: row.noNumber || undefined,
      scale: 0.94 // 默认缩放比例
    };

    // 生成文件名
    const fileName = `${row.moNumber || row.noNumber}_${new Date().getTime()}.zip`;

    // 使用proxy下载方法调用后台API
    proxy?.download('mes/convertRecord/batchDownloadAndConvertByMoOrNoWithZip', params, fileName);
    proxy?.$modal.msgSuccess('ZIP包生成成功，正在下载...');
  } catch (error) {
    proxy?.$modal.msgError('ZIP包生成失败，请重试');
  }
};

/** 格式化文件大小 */
const formatFileSize = (bytes: number): string => {
  if (!bytes || bytes === 0) return '0 MB';
  const mb = bytes / (1024 * 1024);
  return `${mb.toFixed(2)} MB`;
};

/** 格式化状态 */
const formatStatus = (status: number): string => {
  switch (status) {
    case 0:
      return '处理中';
    case 1:
      return '成功';
    case 2:
      return '失败';
    default:
      return '未知';
  }
};

onMounted(() => {
  getList();
});
</script>
