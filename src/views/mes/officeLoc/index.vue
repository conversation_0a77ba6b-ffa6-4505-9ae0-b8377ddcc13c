<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
            <el-form-item label="办事处代码" prop="officeCode">
              <el-input v-model="queryParams.officeCode" placeholder="请输入办事处代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="描述" prop="officeDesc">
              <el-input v-model="queryParams.officeDesc" placeholder="请输入描述" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工号" prop="workNum">
              <el-input v-model="queryParams.workNum" placeholder="请输入工号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="主管" prop="officeSupervisor">
              <el-input v-model="queryParams.officeSupervisor" placeholder="请输入主管" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工作中心" prop="workCenter">
              <el-input v-model="queryParams.workCenter" placeholder="请输入工作中心" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="办事处员工数" prop="totalUser">
              <el-input v-model="queryParams.totalUser" placeholder="请输入办事处员工数" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['mes:officeLoc:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['mes:officeLoc:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['mes:officeLoc:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['mes:officeLoc:export']">导出</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" icon="Upload" @click="handleImport" v-hasPermi="['mes:officeLoc:import']">导入</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="officeLocList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="办事处代码" align="center" prop="officeCode" />
        <el-table-column label="描述" align="center" prop="officeDesc" />
        <el-table-column label="工号" align="center" prop="workNum" />
        <el-table-column label="主管" align="center" prop="officeSupervisor" />
        <el-table-column label="工作中心" align="center" prop="workCenter" />
        <el-table-column label="办事处员工数" align="center" prop="totalUser" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['mes:officeLoc:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['mes:officeLoc:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改工作地点组对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="officeLocFormRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="办事处代码" prop="officeCode">
          <el-input v-model="form.officeCode" placeholder="请输入办事处代码" />
        </el-form-item>
        <el-form-item label="描述" prop="officeDesc">
          <el-input v-model="form.officeDesc" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="工号" prop="workNum">
          <el-input v-model="form.workNum" placeholder="请输入工号" />
        </el-form-item>
        <el-form-item label="主管" prop="officeSupervisor">
          <el-input v-model="form.officeSupervisor" placeholder="请输入主管" />
        </el-form-item>
        <el-form-item label="工作中心" prop="workCenter">
          <el-input v-model="form.workCenter" placeholder="请输入工作中心" />
        </el-form-item>
        <el-form-item label="办事处员工数" prop="totalUser">
          <el-input v-model="form.totalUser" placeholder="请输入办事处员工数" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog v-model="importDialog.visible" title="导入办事处数据" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :on-error="handleFileError"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">拖拽文件到此处，或 <em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">请上传 .xlsx 或 .xls 格式的Excel文件，且文件大小不超过 5MB</div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="upload.isUploading" @click="submitFileForm">确 定</el-button>
          <el-button @click="importDialog.visible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OfficeLoc" lang="ts">
import { addOfficeLoc, delOfficeLoc, getOfficeLoc, listOfficeLoc, updateOfficeLoc } from '@/api/mes/officeLoc';
import { OfficeLocForm, OfficeLocQuery, OfficeLocVO } from '@/api/mes/officeLoc/types';
import { UploadFilled } from '@element-plus/icons-vue';
import { globalHeaders } from '@/utils/request';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const officeLocList = ref<OfficeLocVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const officeLocFormRef = ref<ElFormInstance>();
const uploadRef = ref<ElUploadInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const importDialog = reactive({
  visible: false
});

const upload = reactive({
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: globalHeaders(),
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/mes/officeLoc/importData'
});

const initFormData: OfficeLocForm = {
  id: undefined,
  officeCode: undefined,
  officeDesc: undefined,
  workNum: undefined,
  officeSupervisor: undefined,
  workCenter: undefined,
  totalUser: undefined
};
const data = reactive<PageData<OfficeLocForm, OfficeLocQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    officeCode: undefined,
    officeDesc: undefined,
    workNum: undefined,
    officeSupervisor: undefined,
    workCenter: undefined,
    totalUser: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '不能为空', trigger: 'blur' }],
    officeCode: [{ required: true, message: '办事处代码不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询工作地点组列表 */
const getList = async () => {
  loading.value = true;
  const res = await listOfficeLoc(queryParams.value);
  officeLocList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  officeLocFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: OfficeLocVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加工作地点组';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: OfficeLocVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getOfficeLoc(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改工作地点组';
};

/** 提交按钮 */
const submitForm = () => {
  officeLocFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateOfficeLoc(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addOfficeLoc(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: OfficeLocVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除工作地点组编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delOfficeLoc(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'mes/officeLoc/export',
    {
      ...queryParams.value
    },
    `officeLoc_${new Date().getTime()}.xlsx`
  );
};

/** 导入按钮操作 */
const handleImport = async () => {
  importDialog.visible = true;
};

/** 处理文件上传中 */
const handleFileUploadProgress = (event: any) => {
  upload.isUploading = true;
};

/** 处理文件上传成功 */
const handleFileSuccess = (response: any) => {
  upload.isUploading = false;
  importDialog.visible = false;
  uploadRef.value?.clearFiles();
  proxy?.$modal.msgSuccess('导入成功');
  getList();
};

/** 处理文件上传成功 */
const handleFileError = (response: any) => {
  upload.isUploading = false;
  importDialog.visible = false;
  uploadRef.value?.clearFiles();
  proxy?.$modal.msgError('导入失败');
  // getList();
};

/** 提交上传文件 */
const submitFileForm = () => {
  uploadRef.value?.submit();
};

onMounted(() => {
  getList();
});
</script>
