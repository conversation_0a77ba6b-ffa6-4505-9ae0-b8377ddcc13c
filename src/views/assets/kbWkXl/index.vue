<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="统计日期" style="width: 308px">
              <el-date-picker
                v-model="dateRangeSysDt"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 1, 1, 23, 59, 59)]"
              />
            </el-form-item>
            <el-form-item label="工站号" prop="wkNo">
              <el-input v-model="queryParams.wkNo" placeholder="请输入工站号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工站名称" prop="wkName">
              <el-input v-model="queryParams.wkName" placeholder="请输入工站名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['assets:kbWkXl:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['assets:kbWkXl:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['assets:kbWkXl:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['assets:kbWkXl:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="kbWkXlList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="id" align="center" prop="id" v-if="true" />
        <el-table-column label="统计日期" align="center" prop="sysDt" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sysDt, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="楼层" align="center" prop="floorNo" />
        <el-table-column label="工站号" align="center" prop="wkNo" />
        <el-table-column label="工站名称" align="center" prop="wkName" />
        <el-table-column label="标准工时" align="center" prop="basGs" />
        <el-table-column label="扫描工时" align="center" prop="smGs" />
        <el-table-column label="工站效率" align="center" prop="wkXl" />
        <el-table-column label="月份" align="center" prop="sysDm" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['assets:kbWkXl:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['assets:kbWkXl:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改装配看板隐藏数据对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="kbWkXlFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="统计日期" prop="sysDt">
          <el-date-picker clearable v-model="form.sysDt" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择统计日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="楼层" prop="floorNo">
          <el-input v-model="form.floorNo" placeholder="请输入楼层" />
        </el-form-item>
        <el-form-item label="工站号" prop="wkNo">
          <el-input v-model="form.wkNo" placeholder="请输入工站号" />
        </el-form-item>
        <el-form-item label="工站名称" prop="wkName">
          <el-input v-model="form.wkName" placeholder="请输入工站名称" />
        </el-form-item>
        <el-form-item label="标准工时" prop="basGs">
          <el-input v-model="form.basGs" placeholder="请输入标准工时" />
        </el-form-item>
        <el-form-item label="扫描工时" prop="smGs">
          <el-input v-model="form.smGs" placeholder="请输入扫描工时" />
        </el-form-item>
        <el-form-item label="工站效率" prop="wkXl">
          <el-input v-model="form.wkXl" placeholder="请输入工站效率" />
        </el-form-item>
        <el-form-item label="月份" prop="sysDm">
          <el-input v-model="form.sysDm" placeholder="请输入月份" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="KbWkXl" lang="ts">
import { addKbWkXl, delKbWkXl, getKbWkXl, listKbWkXl, updateKbWkXl } from '@/api/assets/kbWkXl';
import { KbWkXlForm, KbWkXlQuery, KbWkXlVO } from '@/api/assets/kbWkXl/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const kbWkXlList = ref<KbWkXlVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRangeSysDt = ref<[DateModelType, DateModelType]>(['', '']);

const queryFormRef = ref<ElFormInstance>();
const kbWkXlFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: KbWkXlForm = {
  id: undefined,
  sysDt: undefined,
  floorNo: undefined,
  wkNo: undefined,
  wkName: undefined,
  basGs: undefined,
  smGs: undefined,
  wkXl: undefined,
  sysDm: undefined
};
const data = reactive<PageData<KbWkXlForm, KbWkXlQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    wkNo: undefined,
    wkName: undefined,
    params: {
      sysDt: undefined
    }
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询装配看板隐藏数据列表 */
const getList = async () => {
  loading.value = true;
  queryParams.value.params = {};
  proxy?.addDateRange(queryParams.value, dateRangeSysDt.value, 'SysDt');
  const res = await listKbWkXl(queryParams.value);
  kbWkXlList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  kbWkXlFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  dateRangeSysDt.value = ['', ''];
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: KbWkXlVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加装配看板隐藏数据';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: KbWkXlVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getKbWkXl(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改装配看板隐藏数据';
};

/** 提交按钮 */
const submitForm = () => {
  kbWkXlFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateKbWkXl(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addKbWkXl(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: KbWkXlVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除装配看板隐藏数据编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delKbWkXl(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'assets/kbWkXl/export',
    {
      ...queryParams.value
    },
    `kbWkXl_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
