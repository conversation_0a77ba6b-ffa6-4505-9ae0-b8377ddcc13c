<template>
  <div class="barcode-container">
    <!-- 搜索区域 -->
<!--    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">-->
<!--      <div v-show="showSearch" class="search-section">-->
<!--        <el-card shadow="hover" class="search-card">-->
<!--          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form">-->
<!--            <el-form-item label="装配类型" prop="actType">-->
<!--              <el-input v-model="queryParams.actType" placeholder="请输入装配类型" clearable @keyup.enter="handleQuery" />-->
<!--            </el-form-item>-->
<!--            <el-form-item label="MO号" prop="moNo">-->
<!--              <el-input v-model="queryParams.moNo" placeholder="请输入MO号" clearable @keyup.enter="handleQuery" />-->
<!--            </el-form-item>-->
<!--            <el-form-item class="button-group">-->
<!--              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>-->
<!--              <el-button icon="Refresh" @click="resetQuery">重置</el-button>-->
<!--            </el-form-item>-->
<!--          </el-form>-->
<!--        </el-card>-->
<!--      </div>-->
<!--    </transition>-->

    <!-- 表格区域 -->
    <el-card shadow="never" class="table-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-select v-model="zpType" placeholder="选择装配类型" clearable class="type-select">
              <el-option v-for="dict in barcode_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
            <el-button type="primary" icon="Document" @click="createBarcode" :disabled="!zpType">生成条码</el-button>
          </div>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
        </div>
      </template>

<!--      <el-table v-loading="loading" :data="barcodeList" @selection-change="handleSelectionChange" border stripe highlight-current-row>-->
<!--        <el-table-column type="selection" width="55" align="center" />-->
<!--        <el-table-column label="装配类型" align="center" prop="actType" min-width="100" />-->
<!--        <el-table-column label="条码类型" align="center" prop="barType" min-width="100" />-->
<!--        <el-table-column label="MO号" align="center" prop="moNo" min-width="140" show-overflow-tooltip />-->
<!--        <el-table-column label="产品名" align="center" prop="prdName" min-width="140" show-overflow-tooltip />-->
<!--        <el-table-column label="产品型号" align="center" prop="prdType" min-width="120" />-->
<!--        <el-table-column label="导入图号" align="center" prop="dwgNo" min-width="140" show-overflow-tooltip />-->
<!--        <el-table-column label="工序号" align="center" prop="proType" min-width="100" />-->
<!--        <el-table-column label="工序名称" align="center" prop="proName" min-width="140" show-overflow-tooltip />-->
<!--        <el-table-column label="数量" align="center" prop="qty" width="80" />-->
<!--        <el-table-column label="单位" align="center" prop="ut" width="80" />-->
<!--        <el-table-column label="MO图号" align="center" prop="moDwgno" min-width="220" show-overflow-tooltip />-->
<!--        <el-table-column label="条码" align="center" prop="barCode" min-width="140" show-overflow-tooltip />-->
<!--        <el-table-column label="调试工时" align="center" prop="testGs" width="100" />-->
<!--        <el-table-column label="操作工号" align="center" prop="usr" width="100" />-->
<!--      </el-table>-->

<!--      <pagination-->
<!--        v-show="total > 0"-->
<!--        :total="total"-->
<!--        v-model:page="queryParams.pageNum"-->
<!--        v-model:limit="queryParams.pageSize"-->
<!--        @pagination="getList"-->
<!--        class="pagination"-->
<!--      />-->
    </el-card>

    <!-- 对话框 -->
<!--    <el-dialog :title="dialog.title" v-model="dialog.visible" width="600px" append-to-body destroy-on-close>-->
<!--      <el-form ref="barcodeFormRef" :model="form" :rules="rules" label-width="120px" class="dialog-form">-->
<!--        <el-row :gutter="20">-->
<!--          <el-col :span="12">-->
<!--            <el-form-item label="装配类型" prop="actType">-->
<!--              <el-input v-model="form.actType" placeholder="请输入装配类型" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="12">-->
<!--            <el-form-item label="条码类型" prop="barType">-->
<!--              <el-input v-model="form.barType" placeholder="请输入条码类型" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-form-item label="SO号" prop="soNo">-->
<!--          <el-input v-model="form.soNo" placeholder="请输入SO号" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="SO项次" prop="soItm">-->
<!--          <el-input v-model="form.soItm" placeholder="请输入SO项次" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="MO号" prop="moNo">-->
<!--          <el-input v-model="form.moNo" placeholder="请输入MO号" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="关联号" prop="hmoNo">-->
<!--          <el-input v-model="form.hmoNo" placeholder="请输入关联号" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="产品名" prop="prdName">-->
<!--          <el-input v-model="form.prdName" placeholder="请输入产品名" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="导入图号" prop="dwgNo">-->
<!--          <el-input v-model="form.dwgNo" placeholder="请输入导入图号" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="工序名称p_name" prop="proName">-->
<!--          <el-input v-model="form.proName" placeholder="请输入工序名称p_name" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="数量" prop="qty">-->
<!--          <el-input v-model="form.qty" placeholder="请输入数量" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="单位" prop="ut">-->
<!--          <el-input v-model="form.ut" placeholder="请输入单位" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="MO图号" prop="moDwgno">-->
<!--          <el-input v-model="form.moDwgno" placeholder="请输入MO图号" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="条码" prop="barCode">-->
<!--          <el-input v-model="form.barCode" placeholder="请输入条码" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="调试工时" prop="testGs">-->
<!--          <el-input v-model="form.testGs" placeholder="请输入调试工时" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="标配人员" prop="bpRy">-->
<!--          <el-input v-model="form.bpRy" placeholder="请输入标配人员" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="标准工时" prop="basGs">-->
<!--          <el-input v-model="form.basGs" placeholder="请输入标准工时" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="累计工时" prop="wkGs">-->
<!--          <el-input v-model="form.wkGs" placeholder="请输入累计工时" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="关联号" prop="sopBatno">-->
<!--          <el-input v-model="form.sopBatno" placeholder="请输入关联号" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="主机" prop="host">-->
<!--          <el-input v-model="form.host" placeholder="请输入主机" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="操作时间" prop="sysdt">-->
<!--          <el-date-picker clearable v-model="form.sysdt" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择操作时间">-->
<!--          </el-date-picker>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="操作姓名" prop="usrName">-->
<!--          <el-input v-model="form.usrName" placeholder="请输入操作姓名" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="文件名" prop="sfileName">-->
<!--          <el-input v-model="form.sfileName" placeholder="请输入文件名" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="失效日期" prop="endDate">-->
<!--          <el-date-picker clearable v-model="form.endDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择失效日期">-->
<!--          </el-date-picker>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="操作工号" prop="usr">-->
<!--          <el-input v-model="form.usr" placeholder="请输入操作工号" />-->
<!--        </el-form-item>-->
<!--      </el-form>-->
<!--      <template #footer>-->
<!--        <div class="dialog-footer">-->
<!--          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>-->
<!--          <el-button @click="cancel">取 消</el-button>-->
<!--        </div>-->
<!--      </template>-->
<!--    </el-dialog>-->
  </div>
</template>

<style lang="scss" scoped>
.barcode-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .search-section {
    margin-bottom: 20px;

    .search-card {
      .search-form {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: center;

        .el-form-item {
          margin-bottom: 0;
        }

        .button-group {
          margin-left: auto;
        }
      }
    }
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;

      .header-left {
        display: flex;
        align-items: center;
        gap: 10px;

        .type-select {
          width: 200px;
        }
      }
    }

    .el-table {
      margin-bottom: 20px;
    }

    .pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }
  }

  .dialog-form {
    .el-form-item {
      margin-bottom: 20px;
    }
  }
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
  }
}

:deep(.el-dialog__body) {
  padding: 20px 30px;
}

:deep(.el-dialog__footer) {
  padding: 20px 30px;
  border-top: 1px solid #ebeef5;
}
</style>

<script setup name="Barcode" lang="ts">
import { addBarcode, delBarcode, getBarcode, listBarcode, updateBarcode } from '@/api/barcode/barcode';
import { BarcodeForm, BarcodeQuery, BarcodeVO } from '@/api/barcode/barcode/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const { barcode_type } = toRefs<any>(proxy?.useDict('barcode_type'));

const barcodeList = ref<BarcodeVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const zpType = ref('');

const queryFormRef = ref<ElFormInstance>();
const barcodeFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: BarcodeForm = {
  id: undefined,
  actType: undefined,
  barType: undefined,
  soNo: undefined,
  soItm: undefined,
  moNo: undefined,
  hmoNo: undefined,
  prdName: undefined,
  prdType: undefined,
  dwgNo: undefined,
  proType: undefined,
  proName: undefined,
  qty: undefined,
  ut: undefined,
  moDwgno: undefined,
  barCode: undefined,
  testGs: undefined,
  bpRy: undefined,
  basGs: undefined,
  wkGs: undefined,
  sopMo: undefined,
  sopBatno: undefined,
  host: undefined,
  sysdt: undefined,
  usrName: undefined,
  sfileName: undefined,
  endDate: undefined,
  usr: undefined
};
const data = reactive<PageData<BarcodeForm, BarcodeQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    actType: undefined,
    moNo: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询非生产条码列表 */
const getList = async () => {
  loading.value = true;
  const res = await listBarcode(queryParams.value);
  barcodeList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  barcodeFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: BarcodeVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加非生产条码';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: BarcodeVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getBarcode(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改非生产条码';
};

/** 提交按钮 */
const submitForm = () => {
  barcodeFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateBarcode(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addBarcode(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: BarcodeVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除非生产条码编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delBarcode(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'information/barcode/export',
    {
      ...queryParams.value
    },
    `barcode_${new Date().getTime()}.xlsx`
  );
};

/**
 * 生成条码
 */
const createBarcode = () => {
  if (zpType.value === '') {
    proxy?.$modal.msgError('请选择装配类型!');
    return;
  }
  const matchedItem = barcode_type.value.find((item) => item.value === zpType.value);
  const fileName = matchedItem ? matchedItem.label : '';
  // 生成条码
  proxy?.download(`/sfc/information/barcode/generate`, { type: zpType.value }, `${fileName}条码.xlsx`);
};

onMounted(() => {
});
</script>
