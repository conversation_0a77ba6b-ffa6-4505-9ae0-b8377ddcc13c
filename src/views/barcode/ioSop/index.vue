<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item prop="mo">
              <el-button type="primary" icon="Upload" @click="handleImport">导入SOP</el-button>
            </el-form-item>
            <el-form-item label="MO号" prop="mo">
              <el-input suffix-icon="Search" @change="searchMo" v-model="queryParams.mo" placeholder="请输入MO号" clearable />
            </el-form-item>

            <el-form-item label="数量">
              <el-input v-model="queryParams.num" readonly min="1" style="width: 100px" type="number" />
            </el-form-item>

            <el-form-item>
              <el-form-item>
                <el-button type="primary" v-hasPermi="['information:moTsGx:generate']" @click="createBarcode">生成条码</el-button>
                <el-button type="success" v-hasPermi="['information:moTsGx:create']" @click="exportFile">导出</el-button>
              </el-form-item>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <el-table v-loading="loading" border :data="testGxList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="id" align="center" prop="id" v-if="false" />
        <el-table-column label="调试工序号" align="center" prop="tsGxNo" />
        <el-table-column label="调试工序名" align="center" prop="tsGxName" />
        <el-table-column label="标准工时" align="center" prop="basGs" />
        <el-table-column label="文件名" align="center" prop="sfileName" />
        <el-table-column label="备注" align="center" prop="rem" />
        <el-table-column label="操作工号" align="center" prop="usrId" />
        <el-table-column label="操作姓名" align="center" prop="usrName" />
        <el-table-column label="主机" align="center" prop="host" />
        <el-table-column label="操作时间" align="center" prop="sysdt" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sysdt, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="标志" align="center" prop="flag" />
      </el-table>
      <!--      @pagination="getList"-->
      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" />
    </el-card>
    <!-- 添加或修改调试SOP设置对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="testGxFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="调试工序号" prop="tsGxNo">
          <el-input v-model="form.tsGxNo" placeholder="请输入调试工序号t_gx_no" />
        </el-form-item>
        <el-form-item label="调试工序名" prop="tsGxName">
          <el-input v-model="form.tsGxName" placeholder="请输入调试工序名t_gx_name" />
        </el-form-item>
        <el-form-item label="标准工时" prop="basGs">
          <el-input v-model="form.basGs" placeholder="请输入标准工时" />
        </el-form-item>
        <el-form-item label="备注" prop="rem">
          <el-input v-model="form.rem" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="文件名" prop="sfileName">
          <el-input v-model="form.sfileName" placeholder="请输入文件名" />
        </el-form-item>
        <el-form-item label="操作工号" prop="usrId">
          <el-input v-model="form.usrId" placeholder="请输入操作工号" />
        </el-form-item>
        <el-form-item label="操作姓名" prop="usrName">
          <el-input v-model="form.usrName" placeholder="请输入操作姓名" />
        </el-form-item>
        <el-form-item label="主机" prop="host">
          <el-input v-model="form.host" placeholder="请输入主机" />
        </el-form-item>
        <el-form-item label="操作时间" prop="sysdt">
          <el-date-picker clearable v-model="form.sysdt" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择操作时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="标志" prop="flag">
          <el-input v-model="form.flag" placeholder="请输入标志" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body destroy-on-close>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
        class="upload-area"
      >
        <el-icon class="el-icon--upload">
          <i-ep-upload-filled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">
            <span>仅允许导入xls、xlsx格式文件。</span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.el-form--inline .el-form-item {
  margin-right: 9px;
}
</style>

<script setup name="TestGx" lang="ts">
import { addTestGx, delTestGx, generateBarcode, getTestGx, listTestGx, updateTestGx } from '@/api/barcode/ioSop';
import { TestGxForm, TestGxQuery, TestGxVO } from '@/api/barcode/ioSop/types';
import { useI18n } from 'vue-i18n';
import { HttpStatus } from '@/enums/RespEnum';
import { globalHeaders } from '@/utils/request';
import { getOneOrderInfo } from '@/api/master/scanMaster';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const testGxList = ref<TestGxVO[]>([]);
const selectGxData = ref<TestGxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const limit = ref(1);
const uploadRef = shallowRef<ElUploadInstance>();
const queryFormRef = ref<ElFormInstance>();
const testGxFormRef = ref<ElFormInstance>();

const { t } = useI18n();

/*** 用户导入参数 */
const upload = reactive<ImportOption>({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: globalHeaders(),
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/sfc/information/moTsGx/sop'
});
/** 导入按钮操作 */
const handleImport = () => {
  upload.title = '导入SOP';
  upload.open = true;
};

/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value?.submit();
}

/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.handleRemove(file);
  loading.value = true;
  // sop_io默认查询标志为1，即调试SOP设置
  queryParams.value.flag = 1;
  testGxList.value = response.rows;
  total.value = response.total;
  loading.value = false;
};

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});
// const mo = ref('');
// const num = ref('');
// const dwgno = ref('');
export interface MoTsGx {
  mo: string;
  num: string;
  type: string;
  testGxDTOList: Array<any>;
}

const initFormData: TestGxForm = {
  id: undefined,
  tsGxNo: undefined,
  tsGxName: undefined,
  basGs: undefined,
  rem: undefined,
  sfileName: undefined,
  usrId: undefined,
  usrName: undefined,
  host: undefined,
  sysdt: undefined,
  flag: undefined
};
const data = reactive<PageData<TestGxForm, TestGxQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 30,
    tsGxNo: undefined,
    tsGxName: undefined,
    basGs: undefined,
    rem: undefined,
    sfileName: undefined,
    usrId: undefined,
    mo: undefined,
    num: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: 'id不能为空', trigger: 'blur' }],
    tsGxNo: [{ required: true, message: '调试工序号t_gx_no不能为空', trigger: 'blur' }],
    tsGxName: [{ required: true, message: '调试工序名t_gx_name不能为空', trigger: 'blur' }],
    basGs: [{ required: true, message: '标准工时不能为空', trigger: 'blur' }],
    rem: [{ required: true, message: '备注不能为空', trigger: 'blur' }],
    sfileName: [{ required: true, message: '文件名不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 查询工单信息
const searchMo = async () => {
  if (queryParams.value.mo === '') {
    queryParams.value.num = '';
    return;
  }
  loading.value = true;
  const mo = queryParams.value.mo;
  const res = await getOneOrderInfo(mo);
  queryParams.value.num = res.data?.orderQty;
  // queryParams.value.dwgno = res.data?.drawNumber;
  loading.value = false;
};

// 生成条码
const createBarcode = async (selection: TestGxVO[]) => {
  single.value = selection.length != 1;
  const tableData = testGxList.value;
  const mo = queryParams.value.mo;
  const num = queryParams.value.num;
  if (mo === undefined || num === undefined || tableData.length === 0) {
    proxy?.$modal.msgError(t('check.step'));
    return;
  }

  // 生成条码时间较长
  loading.value = true;
  const info: MoTsGx = {
    mo: queryParams.value.mo,
    num: queryParams.value.num,
    type: 'IO',
    testGxDTOList: tableData
  };
  try {
    const res = await generateBarcode(info);
    if (res.code != HttpStatus.SUCCESS) {
      proxy?.$modal.msgError(res.msg);
      return;
    } else {
      downloadFileByHref(res.data);
    }
  } catch (error) {
    loading.value = false;
  } finally {
    loading.value = false;
  }
};
/** 生成 */
const downloadFileByHref = (fileName: string) => {
  proxy?.download(`/sfc/information/moTsGx/create_excle`, { fileName: fileName }, `${fileName}`);
};

/** 导出操作 */
const exportFile = () => {
  const mo = queryParams.value.mo;
  if (mo === undefined || mo === '') {
    proxy?.$modal.msgError(t('check.input'));
    return;
  }
  downloadFileByHref(`${queryParams.value.mo}_io_barcode.xlsx`);
};
/** 查询调试SOP设置列表 */
const getList = async () => {
  loading.value = true;
  // sop_io默认查询标志为1，即调试SOP设置
  queryParams.value.flag = 1;
  const res = await listTestGx(queryParams.value);
  testGxList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  testGxFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  // getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: TestGxVO[]) => {
  ids.value = selection.map((item) => item.tsGxNo);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
  selectGxData.value = selection.map((item) => item);
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加调试SOP设置';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: TestGxVO) => {
  reset();
  const _id = row?.tsGxNo || ids.value[0];
  const res = await getTestGx(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改调试SOP设置';
};

/** 提交按钮 */
const submitForm = () => {
  testGxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.tsGxNo) {
        await updateTestGx(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addTestGx(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      // await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: TestGxVO) => {
  const _ids = row?.tsGxNo || ids.value;
  await proxy?.$modal.confirm('是否确认删除调试SOP设置编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delTestGx(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  // await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'information/testGx/export',
    {
      ...queryParams.value
    },
    `testGx_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  loading.value = false;
  // getList();
});
</script>
