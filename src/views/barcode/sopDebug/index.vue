<template>
  <div class="sop-debug-container">
    <!-- 搜索区域 -->
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
                :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search-section">
        <el-card shadow="hover" class="search-card">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form">
            <el-form-item :label="proxy.$t('barcode.sopDebug.moNumber')" prop="mo">
              <el-input
                v-model="queryParams.mo"
                :placeholder="proxy.$t('barcode.sopDebug.enterMoNumber')"
                suffix-icon="Search"
                @change="searchMo"
                class="mo-input"
              />
            </el-form-item>
            <el-form-item :label="proxy.$t('barcode.sopDebug.quantity')">
              <el-input v-model="queryParams.num" readonly min="1" style="width: 100px" type="number" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Document" v-hasPermi="['information:moTsGx:generate']"
                         @click="createBarcode">
                {{ proxy.$t('barcode.sopDebug.generateBarcode') }}
              </el-button>
              <el-button type="success" icon="Download" v-hasPermi="['information:moTsGx:create']" @click="exportFile">
                {{ proxy.$t('barcode.sopDebug.export') }}
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>
    <!-- 表格区域 -->
    <el-card shadow="never" class="table-card">
      <el-table v-loading="loading" :data="testGxList" @selection-change="handleSelectionChange" border stripe
                highlight-current-row>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column :label="proxy.$t('barcode.sopDebug.debugProcessNo')" align="center" prop="tsGxNo"
                         min-width="120" />
        <el-table-column :label="proxy.$t('barcode.sopDebug.debugProcessName')" align="center" prop="tsGxName"
                         min-width="150" />
        <el-table-column :label="proxy.$t('barcode.sopDebug.standardHours')" align="center" prop="basGs" width="100" />
        <el-table-column :label="proxy.$t('barcode.sopDebug.fileName')" align="center" prop="sfileName"
                         min-width="150" />
        <el-table-column :label="proxy.$t('barcode.sopDebug.remark')" align="center" prop="rem" min-width="120" />
        <el-table-column :label="proxy.$t('barcode.sopDebug.operatorId')" align="center" prop="usrId" width="100" />
        <el-table-column :label="proxy.$t('barcode.sopDebug.operatorName')" align="center" prop="usrName" width="100" />
        <el-table-column :label="proxy.$t('barcode.sopDebug.host')" align="center" prop="host" width="120" />
        <el-table-column :label="proxy.$t('barcode.sopDebug.operationTime')" align="center" prop="sysdt" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sysdt, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="proxy.$t('barcode.sopDebug.flag')" align="center" prop="flag" width="80" />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        class="pagination"
      />
    </el-card>
    <!-- 对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body destroy-on-close>
      <el-form ref="testGxFormRef" :model="form" :rules="rules" label-width="100px" class="dialog-form">
        <el-form-item :label="proxy.$t('barcode.sopDebug.debugProcessNo')" prop="tsGxNo">
          <el-input v-model="form.tsGxNo" :placeholder="proxy.$t('barcode.sopDebug.enterDebugProcessNo')" />
        </el-form-item>
        <el-form-item :label="proxy.$t('barcode.sopDebug.debugProcessName')" prop="tsGxName">
          <el-input v-model="form.tsGxName" :placeholder="proxy.$t('barcode.sopDebug.enterDebugProcessName')" />
        </el-form-item>
        <el-form-item :label="proxy.$t('barcode.sopDebug.standardHours')" prop="basGs">
          <el-input v-model.number="form.basGs" :placeholder="proxy.$t('barcode.sopDebug.enterStandardHours')" />
        </el-form-item>
        <el-form-item :label="proxy.$t('barcode.sopDebug.remark')" prop="rem">
          <el-input v-model="form.rem" :placeholder="proxy.$t('barcode.sopDebug.enterRemark')" />
        </el-form-item>
        <el-form-item :label="proxy.$t('barcode.sopDebug.fileName')" prop="sfileName">
          <el-input v-model="form.sfileName" :placeholder="proxy.$t('barcode.sopDebug.enterFileName')" />
        </el-form-item>
        <el-form-item :label="proxy.$t('barcode.sopDebug.operatorId')" prop="usrId">
          <el-input v-model.number="form.usrId" :placeholder="proxy.$t('barcode.sopDebug.enterOperatorId')" />
        </el-form-item>
        <el-form-item :label="proxy.$t('barcode.sopDebug.operatorName')" prop="usrName">
          <el-input v-model="form.usrName" :placeholder="proxy.$t('barcode.sopDebug.enterOperatorName')" />
        </el-form-item>
        <el-form-item :label="proxy.$t('barcode.sopDebug.host')" prop="host">
          <el-input v-model="form.host" :placeholder="proxy.$t('barcode.sopDebug.enterHost')" />
        </el-form-item>
        <el-form-item :label="proxy.$t('barcode.sopDebug.operationTime')" prop="sysdt">
          <el-date-picker
            v-model="form.sysdt"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            :placeholder="proxy.$t('barcode.sopDebug.selectOperationTime')"
            class="date-picker"
          />
        </el-form-item>
        <el-form-item :label="proxy.$t('barcode.sopDebug.flag')" prop="flag">
          <el-input v-model="form.flag" :placeholder="proxy.$t('barcode.sopDebug.enterFlag')" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">{{ proxy.$t('barcode.sopDebug.submit')
            }}
          </el-button>
          <el-button @click="cancel">{{ proxy.$t('barcode.sopDebug.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.sop-debug-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .search-section {
    margin-bottom: 20px;

    .search-card {
      .search-form {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: center;

        .el-form-item {
          margin-bottom: 0;
        }

        .mo-input {
          width: 200px;
        }

        .qty-input,
        .dwg-input {
          width: 120px;
        }

        .button-group {
          margin-left: auto;
        }
      }
    }
  }

  .table-card {
    .el-table {
      margin-bottom: 20px;
    }

    .pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
    }
  }

  .dialog-form {
    .el-form-item {
      margin-bottom: 20px;
    }

    .date-picker {
      width: 100%;
    }
  }
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
  }
}

:deep(.el-dialog__body) {
  padding: 20px 30px;
}

:deep(.el-dialog__footer) {
  padding: 20px 30px;
  border-top: 1px solid #ebeef5;
}
</style>
<script setup name="TestGx" lang="ts">
import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
import { addTestGx, delTestGx, generateBarcode, getTestGx, listTestGx, updateTestGx } from '@/api/barcode/ioSop';
import { TestGxForm, TestGxQuery, TestGxVO } from '@/api/barcode/ioSop/types';
import { useI18n } from 'vue-i18n';
import { HttpStatus } from '@/enums/RespEnum';
import { getOneOrderInfo } from '@/api/master/scanMaster';

interface DialogOption {
  visible: boolean;
  title: string;
}

interface PageData<T, U> {
  form: T;
  queryParams: U;
  rules: any;
}

const { proxy } = getCurrentInstance() as { proxy: any };
const testGxList = ref<TestGxVO[]>([]);
const selectGxData = ref<TestGxVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const queryFormRef = ref<ElFormInstance>();
const testGxFormRef = ref<ElFormInstance>();
const { t } = useI18n();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

export interface MoTsGx {
  mo: string;
  num: string;
  type: string;
  testGxDTOList: Array<any>;
}

const initFormData: TestGxForm = {
  id: undefined,
  tsGxNo: undefined,
  tsGxName: undefined,
  basGs: undefined,
  rem: undefined,
  sfileName: undefined,
  usrId: undefined,
  usrName: undefined,
  host: undefined,
  sysdt: undefined,
  flag: undefined
};

const data = reactive<PageData<TestGxForm, TestGxQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 30,
    tsGxNo: undefined,
    tsGxName: undefined,
    basGs: undefined,
    rem: undefined,
    sfileName: undefined,
    usrId: undefined,
    mo: undefined,
    num: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: proxy.$t('barcode.sopDebug.idRequired'), trigger: 'blur' }],
    tsGxNo: [{ required: true, message: proxy.$t('barcode.sopDebug.debugProcessNoRequired'), trigger: 'blur' }],
    tsGxName: [{ required: true, message: proxy.$t('barcode.sopDebug.debugProcessNameRequired'), trigger: 'blur' }],
    basGs: [{ required: true, message: proxy.$t('barcode.sopDebug.standardHoursRequired'), trigger: 'blur' }],
    rem: [{ required: true, message: proxy.$t('barcode.sopDebug.remarkRequired'), trigger: 'blur' }],
    sfileName: [{ required: true, message: proxy.$t('barcode.sopDebug.fileNameRequired'), trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 查询工单信息
const searchMo = async () => {
  if (queryParams.value.mo === '') {
    queryParams.value.num = '';
    return;
  }
  loading.value = true;
  try {
    const mo = queryParams.value.mo;
    const res = await getOneOrderInfo(mo);
    queryParams.value.num = res.data?.orderQty;
  } catch (error) {
    console.error('Error fetching order info:', error);
    proxy?.$modal.msgError(proxy.$t('barcode.sopDebug.fetchOrderError'));
  } finally {
    loading.value = false;
  }
};

// 生成条码
const createBarcode = async () => {
  if (queryParams.value.mo === undefined || queryParams.value.num === undefined || testGxList.value.length === 0) {
    proxy?.$modal.msgError(t('check.step'));
    return;
  }
  loading.value = true;
  try {
    const info: MoTsGx = {
      mo: queryParams.value.mo,
      num: queryParams.value.num,
      type: 'TS',
      testGxDTOList: testGxList.value
    };
    const res = await generateBarcode(info);
    if (res.code !== HttpStatus.SUCCESS) {
      proxy?.$modal.msgError(res.msg);
    } else {
      downloadFileByHref(res.data);
    }
  } catch (error) {
    console.error('Error generating barcode:', error);
    proxy?.$modal.msgError(proxy.$t('barcode.sopDebug.generateBarcodeError'));
  } finally {
    loading.value = false;
  }
};

/** 生成 */
const downloadFileByHref = (fileName: string) => {
  proxy?.download(`/sfc/information/moTsGx/create_excle`, { fileName: fileName }, `${fileName}`);
};

/** 导出操作 */
const exportFile = () => {
  const mo = queryParams.value.mo;
  if (mo === undefined || mo === '') {
    proxy?.$modal.msgError(t('check.input'));
    return;
  }
  downloadFileByHref(`${queryParams.value.mo}_ts_barcode.xlsx`);
};

/** 查询调试SOP设置列表 */
const getList = async () => {
  loading.value = true;
  try {
    queryParams.value.flag = 0;
    const res = await listTestGx(queryParams.value);
    testGxList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('Error fetching SOP list:', error);
    proxy?.$modal.msgError(proxy.$t('barcode.sopDebug.fetchSopListError'));
  } finally {
    loading.value = false;
  }
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  testGxFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: TestGxVO[]) => {
  ids.value = selection.map((item) => item.tsGxNo);
  single.value = selection.length !== 1;
  multiple.value = selection.length === 0;
  selectGxData.value = selection;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = proxy.$t('barcode.sopDebug.addDebuggingSOPSetting');
};

/** 修改按钮操作 */
const handleUpdate = async (row?: TestGxVO) => {
  reset();
  const _id = row?.tsGxNo || ids.value[0];
  try {
    const res = await getTestGx(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = proxy.$t('barcode.sopDebug.editDebuggingSOPSetting');
  } catch (error) {
    console.error('Error fetching SOP data:', error);
    proxy?.$modal.msgError(proxy.$t('barcode.sopDebug.fetchSopDataError'));
  }
};

/** 提交按钮 */
const submitForm = () => {
  testGxFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      try {
        if (form.value.tsGxNo) {
          await updateTestGx(form.value);
        } else {
          await addTestGx(form.value);
        }
        proxy?.$modal.msgSuccess(proxy.$t('barcode.sopDebug.operationSuccess'));
        dialog.visible = false;
        await getList();
      } catch (error) {
        console.error('Error submitting form:', error);
        proxy?.$modal.msgError(proxy.$t('barcode.sopDebug.submitFormError'));
      } finally {
        buttonLoading.value = false;
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: TestGxVO) => {
  const _ids = row?.tsGxNo || ids.value;
  try {
    await proxy?.$modal.confirm(proxy.$t('barcode.sopDebug.confirmDelete', [_ids]));
    await delTestGx(_ids);
    proxy?.$modal.msgSuccess(proxy.$t('barcode.sopDebug.deleteSuccess'));
    await getList();
  } catch (error) {
    console.error('Error deleting SOP:', error);
    proxy?.$modal.msgError(proxy.$t('barcode.sopDebug.deleteError'));
  }
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'information/testGx/export',
    {
      ...queryParams.value
    },
    `testGx_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
