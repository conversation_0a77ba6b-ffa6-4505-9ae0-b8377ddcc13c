<template>
  <div class="equip-barcode-container">
    <!-- 步骤区域 -->
    <div class="steps-section">
      <el-card shadow="hover" class="steps-card">
        <!-- 第一步 -->
        <div class="step-item">
          <div class="step-label">第一步：</div>
          <el-button type="primary" icon="Upload" @click="handleImport">导入SOP</el-button>
          <div class="step-content">{{ fileName }}</div>
        </div>

        <!-- 第二步 -->
        <div class="step-item">
          <div class="step-label">第二步：导入MO号</div>
          <div class="step-content">
            <el-input v-model="mo" placeholder="请输入MO号" class="mo-input" @keyup.enter="searchMo" />
            <el-input v-model="moNum" placeholder="" class="qty-input" />
          </div>
        </div>

        <!-- 第三步 -->
        <div class="step-item">
          <div class="step-label">第三步：</div>
          <el-button type="primary" :disabled="unable" @click="createBarcode">生成条码</el-button>
          <div class="step-content">{{ pdfName }}</div>
        </div>
      </el-card>
    </div>

    <!-- 搜索区域 -->
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter"
                :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="search-section">
        <el-card shadow="hover" class="search-card">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="search-form">
            <el-form-item label="MO号" prop="moNo">
              <el-input v-model="queryParams.moNo" placeholder="请输入MO号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="文件名" prop="fileName">
              <el-input v-model="queryParams.sfileName" placeholder="请输入文件名" clearable
                        @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="工站图号" prop="dwgName">
              <el-input v-model="queryParams.dwgNo" placeholder="请输入工站图号" clearable
                        @keyup.enter="handleQuery" />
            </el-form-item>
            <!--            <el-form-item label="客户SN号" prop="customerSn" label-width="80">-->
            <!--              <el-input v-model="queryParams.customerSn" placeholder="请输入客户SN号" clearable-->
            <!--                        @keyup.enter="handleQuery" />-->
            <!--            </el-form-item>-->
            <el-form-item class="button-group">
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <!-- 表格区域 -->
    <el-card shadow="never" class="table-card">
      <template #header>
        <div class="card-header">
          <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" />
        </div>
      </template>

      <el-table v-loading="loading" :data="equipBarcodeList" @selection-change="handleSelectionChange" border stripe
                highlight-current-row>
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="装配类型" align="center" prop="actType" min-width="100" />
        <el-table-column label="条码类型" align="center" prop="barType" min-width="100" />
        <el-table-column label="MO号" align="center" prop="moNo" min-width="200" show-overflow-tooltip />
        <el-table-column label="产品名" align="center" prop="prdName" min-width="250" show-overflow-tooltip />
        <el-table-column label="产品型号" align="center" prop="prdType" min-width="120" />
        <el-table-column label="工序号" align="center" prop="proType" min-width="100" />
        <el-table-column label="工序名称" align="center" prop="proName" min-width="200" show-overflow-tooltip />
        <el-table-column label="数量" align="center" prop="qty" width="80" />
        <el-table-column label="单位" align="center" prop="ut" width="80" />
        <el-table-column label="MO图号" align="center" prop="moDwgno" min-width="250" show-overflow-tooltip />
        <el-table-column label="条码" align="center" prop="barCode" min-width="200" show-overflow-tooltip />
        <el-table-column label="标配人员" align="center" prop="bpRy" width="100" />
        <el-table-column label="标准工时" align="center" prop="basGs" width="100" />
        <el-table-column label="关联号" align="center" prop="sopBatno" min-width="200" show-overflow-tooltip />
        <el-table-column label="操作姓名" align="center" prop="usrName" width="100" />
        <el-table-column label="文件名" align="center" prop="sfileName" min-width="150" />
        <el-table-column label="操作工号" align="center" prop="usr" width="100" />
        <!--        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">-->
        <!--          <template #default="scope">-->
        <!--            <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>-->
        <!--            <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
      </el-table>

      <pagination
        v-if="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        @pagination="getList"
        class="pagination"
      />
    </el-card>

    <!-- 导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body destroy-on-close>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
        class="upload-area"
      >
        <el-icon class="el-icon--upload">
          <i-ep-upload-filled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip">
            <span>仅允许导入xls、xlsx格式文件。</span>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.equip-barcode-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .steps-section {
    margin-bottom: 0px;

    .steps-card {
      .step-item {
        display: flex;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }

        .step-label {
          width: 200px;
          font-size: 16px;
          font-weight: 500;
          color: #303133;
        }

        .step-content {
          display: flex;
          align-items: center;
          gap: 10px;

          .mo-input,
          .qty-input {
            width: 200px;
          }

          .search-btn {
            margin: 0 10px;
          }
        }
      }
    }
  }

  .search-section {
    margin-bottom: 0px;

    .search-card {
      .search-form {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: center;

        .el-form-item {
          margin-bottom: 0;
        }

        .button-group {
          margin-left: auto;
        }
      }
    }
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
    }

    .el-table {
      margin-bottom: 20px;
    }

    .pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
      padding: 10px 0;
    }
  }
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table) {
  th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 600;
  }
}

:deep(.el-dialog__body) {
  padding: 20px 30px;
}

:deep(.el-dialog__footer) {
  padding: 20px 30px;
  border-top: 1px solid #ebeef5;
}

.upload-area {
  :deep(.el-upload-dragger) {
    width: 100%;
  }

  .el-upload__tip {
    text-align: center;
    color: #909399;
    font-size: 12px;
    margin-top: 10px;
  }
}

.el-button {
  &:focus {
    outline: none;
  }

  &:focus-visible {
    outline: 2px solid var(--el-color-primary);
    outline-offset: 2px;
  }
}
</style>

<script setup name="EquipBarcode" lang="ts">
import {
  addEquipBarcode,
  createBarcodeInfo,
  delEquipBarcode,
  EXPORT_BARCODE_URL,
  getEquipBarcode,
  listEquipBarcode,
  updateEquipBarcode
} from '@/api/barcode/equipBarcode';
import { EquipBarcodeForm, EquipBarcodeQuery, EquipBarcodeVO } from '@/api/barcode/equipBarcode/types';
import { globalHeaders } from '@/utils/request';
import { getOneOrderInfo } from '@/api/master/scanMaster';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const equipBarcodeList = shallowRef<EquipBarcodeVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const mo = ref('');
const moNum = ref('');
const dwgno = ref('');
const fileName = shallowRef<string | undefined>('');
const pdfName = shallowRef<string | undefined>('');
const uploadRef = shallowRef<ElUploadInstance>();
const queryFormRef = shallowRef<ElFormInstance>();
const equipBarcodeFormRef = shallowRef<ElFormInstance>();
const unable = ref(false);
/*** 用户导入参数 */
const upload = reactive<ImportOption>({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: globalHeaders(),
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/sfc/information/barcode/upload'
});

// 存储选中的行
const selectedRows = ref<EquipBarcodeVO[]>([]);

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: EquipBarcodeForm = {};
const data = reactive<PageData<EquipBarcodeForm, EquipBarcodeQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    moNo: undefined,
    sfileName: undefined,
    dwgNo: undefined,
    customerSn: undefined,
    params: {}
  },
  rules: {}
});

const { queryParams, form, rules } = toRefs(data);

/** 导入按钮操作 */
const handleImport = () => {
  upload.title = '导入SOP';
  upload.open = true;
  resetQuery();
};

/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value?.submit();
}

/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.handleRemove(file);
  fileName.value = file.name;
  reset();
  equipBarcodeList.value = response.data;
  total.value = response.data.length;
  queryParams.value.pageSize = Math.ceil(total.value / 10) * 10;
};


const searchMo = async () => {
  if (mo.value === '') {
    moNum.value = '';
    return;
  }
  await getOneOrderInfo(mo.value).then((res) => {
    if (res.data === null) {
      ElMessage.error('没有找到工单信息');
      return;
    }
    moNum.value = res.data?.orderQty;
    dwgno.value = res.data?.drawNumber;
  });
};

const createBarcode = async () => {
  if (fileName.value === '' || mo.value === '' || moNum.value === '' || equipBarcodeList.value.length === 0) {
    ElMessage.error('请先完成第一步和第二步');
    return;
  }
  // 生成条码时间较长，表格先加载效果
  loading.value = true;
  const batNo = equipBarcodeList.value.map((en) => en.sopBatno).pop();
  await createBarcodeInfo(fileName.value, mo.value, moNum.value, dwgno.value, batNo).then((res) => {
    if (res.code !== 200) {
      ElMessage.error(res.msg);
      return;
    }
    getList();
    pdfName.value = res.data;
    proxy?.download(`${EXPORT_BARCODE_URL}/${encodeURIComponent(res.data)}`, {}, res.data);
    // exportBarcodeFile(res.data);
    // 成功后防止多次生成条码
    unable.value = true;
  }).catch((error) => {
    ElMessage.error(error);
  }).finally(() => {
    loading.value = false;
  });
};

/** 查询条码制作列表 */
const getList = async () => {
  loading.value = true;
  try {
    let initParm = {};
    if (fileName.value !== '') {
      initParm = { sfileName: fileName.value };
    }
    const param = { ...queryParams.value, ...initParm };
    const res = await listEquipBarcode(param);
    equipBarcodeList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('获取列表失败:', error);
  } finally {
    loading.value = false;
  }
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  equipBarcodeFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  equipBarcodeList.value = [];
  total.value = 0;
  // queryParams.value = {
  //   pageNum: 1,
  //   pageSize: 10,
  //   moNo: undefined,
  //   fileName: undefined,
  //   dwgName: undefined,
  //   customerSn: undefined,
  //   params: {}
  // };
  // handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: EquipBarcodeVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
  selectedRows.value = selection;
  console.log(selection);
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加条码制作';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: EquipBarcodeVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getEquipBarcode(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改条码制作';
};

/** 提交按钮 */
const submitForm = () => {
  equipBarcodeFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateEquipBarcode(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addEquipBarcode(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: EquipBarcodeVO) => {
  const _ids = row?.id || ids.value;
  try {
    await proxy?.$modal.confirm('是否确认删除条码制作编号为"' + _ids + '"的数据项？');
    await delEquipBarcode(_ids);
    proxy?.$modal.msgSuccess('删除成功');
    await getList();
  } catch (error) {
    console.error('删除操作失败:', error);
  }
};

/** 导出按钮操作 */
const handleExport = () => {
  // 获取选中的行数据
  if (selectedRows.value.length > 0) {
    // 获取选中行的第一个数据
    const selectedRow = selectedRows.value[0];
    // 将选中行的数据赋值给 formModel
    if (selectedRow !== undefined) {
      const fame = `${selectedRow.moNo}_${selectedRow.sfileName}`;
      proxy?.download(`${EXPORT_BARCODE_URL}/${encodeURIComponent(fame)}`, {}, fame);
      // exportBarcodeFile(`${selectedRow.moNo}_${selectedRow.sfileName}`);
    }
  }
};

onMounted(() => {
});
</script>
