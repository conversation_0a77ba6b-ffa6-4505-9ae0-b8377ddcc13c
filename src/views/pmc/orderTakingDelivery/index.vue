<template>
  <div v-loading="loading">
    <el-card>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" @keyup.enter="getData()">
        <el-form-item prop="bargainDate" label="接单日期">
          <el-date-picker style="width: 240px" clearable v-model="queryParams.bargainDate" type="daterange"
            value-format="YYYY-MM-DD" start-placeholder="接单日期(开始)" end-placeholder="接单日期(结束)" />
        </el-form-item>
        <el-form-item prop="merchandiser">
          <el-select :style="widthStyle" v-model="queryParams.merchandiser" placeholder="跟单工程师">
            <el-option v-for="item in merchandisers" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="customerCode">
          <el-input placeholder="客户代码" :style="widthStyle" v-model="queryParams.customerCode" clearable />
        </el-form-item>
        <el-form-item prop="customerCode">
          <el-input placeholder="DR单号" :style="widthStyle" v-model="queryParams.drNumber" clearable />
        </el-form-item>
        <el-form-item prop="isUrgent">
          <el-select style="width: 150px" v-model="queryParams.isUrgent" placeholder="特急件">
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getData">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button :icon="Download" @click="handleExport">
            下载
          </el-button>
        </el-form-item>
      </el-form>

      <el-table :height="tableHeight" border :data="dataList" show-overflow-tooltip>
        <el-table-column label="统计项" :width="widthMedium" prop="statisticalItem" />
        <el-table-column label="跟单负责人" :width="widthMedium" align="center" prop="merchandiser" />
        <el-table-column label="加工类" align="center" prop="processType" />
        <el-table-column :width="widthNarrow" :formatter="numberFormatter" v-for="column in columns" :key="column"
          :prop="'dataMap.' + column" :label="column" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup name="OrderTakingDelivery" lang="ts">
import { getDataList, getMerchandisers } from '@/api/pmc/orderTakingDelivery/api';
import dayjs from 'dayjs';
import { Download } from '@element-plus/icons-vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const dataList = ref<any[]>([]);
const loading = ref(false);
const columns = ref<string[]>([]);
const merchandisers = ref<any[]>([]);
const tableHeight = ref(800);
const widthNarrow = 100
const widthMedium = 150
const widthWide = 150
const widthStyle = ref({
  'width': widthWide + 'px'
})
const minWidthStyle = ref({
  'minWidth': widthWide + 'px'
})
const queryFormRef = ref();
const queryParams = reactive({
  bargainDate: [
    dayjs().startOf('day').subtract(7, 'day').format('YYYY-MM-DD'),
    dayjs().endOf('day').format('YYYY-MM-DD')
  ],
  merchandiser: '',
  drNumber: '',
  customerCode: '',
  isUrgent: ''
});

const getQueryParams = (): Object => {
  let query = JSON.parse(JSON.stringify(queryParams));
  query.bargainDate[0] += ' 00:00:00'
  query.bargainDate[1] += ' 23:59:59'
  return query;
}

const getData = async () => {
  loading.value = true;
  getDataList(getQueryParams())
    .then((res) => {
      columns.value = []
      if (res.data.length > 1) {
        columns.value = Object.keys(res.data[0].dataMap).sort((a, b) => a.localeCompare(b))
      }
      dataList.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 获取跟单工程师列表 */
const getMerchandiserList = async () => {
  getMerchandisers().then((res) => {
    merchandisers.value = res.data.map((item: any) => {
      return {
        label: item,
        value: item
      }
    });
  });
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  getDataList();
}

const numberFormatter = (row: any, column: any, cellValue: any) => {
  if (cellValue === null || typeof cellValue === 'undefined' || cellValue === '') {
    return ''
  }
  return (Math.round(cellValue * 10) / 10).toString();
}


/** 导出按钮操作 */
const handleExport = () => {
  loading.value = true;
  try {
    proxy?.download('/pmc/order_taking_delivery/statistics/download', {
      ...getQueryParams()
    }, `半成品接单及交货统计${new Date().getTime()}.xlsx`)
  } catch (error) {
    ElMessage.error('导出失败');
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  getData();
  getMerchandiserList();
  tableHeight.value = window.innerHeight - 250;
});

</script>

<style lang="scss" scoped>
.el-form-item {
  margin-right: 16px;
}
</style>