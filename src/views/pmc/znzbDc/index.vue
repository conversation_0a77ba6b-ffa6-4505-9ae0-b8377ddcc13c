<template>
  <div class="p-2">
    <el-card>
      <el-space>
        <el-button :type="activeIndex === 0 ? 'primary' : ''" @click="buttonSearch('组织架构')">组织架构</el-button>
        <el-button :type="activeIndex === 1 ? 'primary' : ''" @click="buttonSearch('培训资料')">培训资料</el-button>
        <el-button :type="activeIndex === 2 ? 'primary' : ''" @click="buttonSearch('岗位职责')">岗位职责</el-button>
        <el-button :type="activeIndex === 3 ? 'primary' : ''" @click="buttonSearch('操作指导书')">操作指导书</el-button>
        <el-button :type="activeIndex === 4 ? 'primary' : ''" @click="buttonSearch('会议记录')">会议记录</el-button>
        <el-button :type="activeIndex === 5 ? 'primary' : ''" @click="buttonSearch('项目计划')">项目计划</el-button>
      </el-space>
      <el-table :data="tableData" border column-resizable :loading="loading" size="small">
        <el-table-column prop="dcName" label="文件名称">
          <template #default="{ row }">
            <span class="cursor-pointer hover:text-blue-500" @dblclick="handleDownload(row)">{{ row.dcName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="shUsr" label="审核工号" width="150" />
        <el-table-column prop="shName" label="审核姓名" width="150" />
        <el-table-column prop="shDt" label="审核时间" width="150" />
      </el-table>
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @current-change="pageChange"
        @size-change="pageSizeChange"
      >
      </el-pagination>
      <el-divider></el-divider>
      <el-space>
        <el-select v-model="dcType" :style="{ width: '200px' }" placeholder="分类" disabled>
          <el-option label="组织架构" value="组织架构"></el-option>
          <el-option label="培训资料" value="培训资料"></el-option>
          <el-option label="岗位职责" value="岗位职责"></el-option>
          <el-option label="操作指导书" value="操作指导书"></el-option>
          <el-option label="会议记录" value="会议记录"></el-option>
          <el-option label="项目计划" value="项目计划"></el-option>
        </el-select>
        <el-upload :http-request="uploadFile" :show-file-list="false" :before-upload="beforeUpload">
          <template #trigger>
            <el-button type="primary">上传</el-button>
          </template>
        </el-upload>
        <el-button type="primary" v-hasPermi="['pmc:znzbDc:audit']" @click="auditRecord">审核</el-button>
        <el-button type="primary" v-hasPermi="['pmc:znzbDc:remove']" @click="deleteRecord">删除</el-button>
      </el-space>
      <p></p>
      <el-table :data="tableData2" border :loading="loading" size="small" @selection-change="handleRowSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="dcType" label="分类" width="150" />
        <el-table-column prop="dcName" label="文件名称" width="auto" />
        <el-table-column prop="dcSta" label="状态" width="150" />
        <el-table-column prop="musr" label="上传工号" width="150" />
        <el-table-column prop="mname" label="上传姓名" width="150" />
        <el-table-column prop="sysdt" label="上传时间" width="150" />
        <el-table-column prop="host" label="上传电脑" width="150" />
        <!--        <el-table-column prop="shUsr" label="审核工号" width="150" />-->
        <!--        <el-table-column prop="shName" label="审核姓名" width="150" />-->
        <!--        <el-table-column prop="shDt" label="审核时间" width="150" />-->
      </el-table>
      <el-pagination
        v-model:current-page="pagination2.current"
        v-model:page-size="pagination2.pageSize"
        :total="pagination2.total"
        @current-change="pageChange2"
        @size-change="pageSizeChange2"
        layout="total, sizes, prev, pager, next, jumper"
      ></el-pagination>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import { auditFile, deleteFile, queryFileList, uploadManagerFile } from '@/api/pmc/znzbDc/file-manager';
import { checkPermi } from '@/utils/permission';
import { download } from '@/utils/request';

const enableEdit = ref(false);
const enableDel = ref(false);
const dcType = ref('');
// 新增：高亮按钮索引
const activeIndex = ref(-1);
// 按钮类型与索引的映射
const typeList = ['组织架构', '培训资料', '岗位职责', '操作指导书', '会议记录', '项目计划'];

const loading = ref(false);
const tableData = ref<[]>([]);
const tableData2 = ref([]);

// 新增：声明行选择键存储变量
const rowSelection = reactive({
  selectedRowKeys: [] as string[]
});

// 行选择器配置（Element Plus通过表格列实现选择）
const handleRowSelectionChange = (selectedRows) => {
  rowSelection.selectedRowKeys = selectedRows.map((row) => row.id);
};

const pagination = reactive({
  current: 1,
  pageSize: 10,
  showJumper: true,
  showPageSize: true,
  total: 0,
  showTotal: true
});
const pagination2 = reactive({
  current: 1,
  pageSize: 10,
  showJumper: true,
  showPageSize: true,
  total: 0,
  showTotal: true
});

const search = async (type: string, sta: string) => {
  loading.value = true;
  const page = {
    pageNum: pagination.current,
    pageSize: pagination.pageSize
  };
  const initParm = type !== '' ? { 'dcType': type, 'dcSta': sta } : { 'dcSta': sta };

  const param = { ...initParm, ...page };
  await queryFileList(param)
    .then((res) => {
      if (sta === '已审') {
        tableData.value = res.rows;
        pagination.total = res.total;
      }
      if (sta === '未审核') {
        tableData2.value = res.rows;
        pagination2.total = res.total;
      }
    })
    .catch((error) => {
      ElMessage.error(error);
    })
    .finally(() => {
      loading.value = false;
    });
};

// 查询按钮
const buttonSearch = (type: string) => {
  pagination.current = 1;
  dcType.value = type;
  activeIndex.value = typeList.indexOf(type);
  search(type, '已审');
};

const pageChange = (cur: number) => {
  pagination.current = cur;
  search(dcType.value, '已审');
};
const pageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  search(dcType.value, '已审');
};
const pageChange2 = (cur: number) => {
  pagination2.current = cur;
};
const pageSizeChange2 = (pageSize: number) => {
  pagination2.pageSize = pageSize;
};

const beforeUpload = (file: File) => {
  if (dcType.value === '') {
    ElMessage.error('');
    return false;
  }
  return true;
};

const uploadFile = (option: any) => {
  const { file, onSuccess, onError } = option;
  const formData = new FormData();
  formData.append('file', file);
  formData.append('dcType', dcType.value);

  uploadManagerFile(formData)
    .then((res) => {
      if (res.code === 200) {
        ElMessage.success(res.msg);
        if (enableEdit.value) {
          search('', '未审核');
        }
        onSuccess?.(res);
      }
    })
    .catch((error) => {
      ElMessage.error(`upload fail ${error}`);
      onError?.(error);
    });

  // 返回 Promise 以符合类型要求
  return Promise.resolve();
};

const auditRecord = () => {
  if (rowSelection.selectedRowKeys.length === 0) {
    ElMessage.warning('请选择要审核的记录');
    return;
  }
  auditFile(rowSelection.selectedRowKeys)
    .then((res) => {
      if (res.code === 200) {
        buttonSearch(dcType.value);
        search('', '未审核');
        rowSelection.selectedRowKeys = [];
      }
    })
    .catch((error) => {
      ElMessage.error(`audit fail ${error}`);
    });
};

const deleteRecord = () => {
  if (rowSelection.selectedRowKeys.length === 0) {
    ElMessage.warning('请选择要删除的记录');
    return;
  }
  deleteFile(rowSelection.selectedRowKeys)
    .then((res) => {
      if (res.code === 200) {
        search('', '未审核');
        rowSelection.selectedRowKeys = [];
      }
    })
    .catch((error) => {
      ElMessage.error(`delete fail ${error}`);
    });
};

const handleDownload = async (row: any) => {
  try {
    await download(
      '/pmc/znzbDc/download',
      {
        fileName: dcType.value + '/' + row.dcName
      },
      row.dcName
    );
    ElMessage.success('下载成功');
  } catch (err: any) {
    ElMessage.error('下载失败：' + err.message);
  }
};

onMounted(() => {
  buttonSearch('组织架构');
  enableEdit.value = checkPermi(['pmc:znzbDc:audit']);
  if (enableEdit.value) {
    search('', '未审核');
  }
});
</script>

<style scoped></style>
