<template>
  <div class="p-4">
    <!-- 搜索卡片 -->
    <el-card class="mb-6 shadow-md">
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">条件搜索</h3>
        </div>
      </template>

      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="接单日期">
              <el-date-picker
                v-model="searchForm.bargainDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 240px"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="客户代码">
              <el-input v-model="searchForm.customerCode" clearable style="width: 200px" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="DR单号">
              <el-input v-model="searchForm.drNumber" clearable style="width: 200px" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="负责人">
              <el-select v-model="searchForm.merchandiser" clearable :disabled="enable" style="width: 200px">
                <el-option v-for="item in mData" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="客户要求交期">
              <el-date-picker
                v-model="searchForm.deliveryDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 240px"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="利润中心">
              <el-select v-model="searchForm.profitCenter" clearable style="width: 200px">
                <el-option v-for="item in profitCenters" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="MO单号">
              <el-input v-model="searchForm.moNumber" clearable style="width: 200px" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="装配技师">
              <el-input v-model="searchForm.assemblyTechnician" clearable style="width: 200px" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="状态">
              <el-select v-model="searchForm.status" clearable style="width: 200px">
                <el-option label="已装配" value="已装配" />
                <el-option label="未装配" value="未装配" />
                <el-option label="全部" value="全部" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="结案">
              <el-select v-model="searchForm.isClosed" clearable style="width: 200px">
                <el-option label="Y" value="Y" />
                <el-option label="N" value="N" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" class="flex justify-end">
            <el-button type="primary" :loading="loading" @click="handleQuery">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon>
                <Delete />
              </el-icon>
              重置
            </el-button>
            <el-button @click="handleModify">
              <el-icon>
                <Edit />
              </el-icon>
              修改
            </el-button>
            <el-button type="warning" :loading="exportLoading" @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>
              下载
            </el-button>
            <el-button type="info" @click="handleLoadNewData">
              <el-icon>
                <Refresh />
              </el-icon>
              加载新数据
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="shadow-md">
      <el-table :data="paginatedData" :loading="loading" border stripe @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="moNo" label="mo_no" width="150" align="center">
          <template #default="{ row }">
            <span>{{ row.moNo }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="drItm" label="DR_ITM" width="100" align="center" />
        <el-table-column prop="drawNumber" label="图号" width="200" />
        <el-table-column prop="custNo" label="客户代码" width="120" align="center" />
        <el-table-column prop="orderDate" label="接单日期" width="150" align="center" />
        <el-table-column prop="custPoNo" label="客户PO号" width="150" align="center" />
        <el-table-column prop="deliveryDate" label="客户要求交期" width="150" align="center" />
        <el-table-column prop="pmcReqDate" label="PMC要求交期" width="150" align="center" />
        <el-table-column prop="orderQty" label="订单数量" width="100" align="center" />
        <el-table-column prop="lastOpTime" label="末工序时间" width="150" align="center" />
        <el-table-column prop="assemblerExceptionRemark" label="装配异常备注" width="150" align="center">
          <template #default="{ row }">
            <span>{{ row.assemblerExceptionRemark }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="qaExceptionRemark" label="QA异常备注" width="150" align="center">
          <template #default="{ row }">
            <span>{{ row.qaExceptionRemark }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pmcRemark" label="PMC备注" width="150" align="center">
          <template #default="{ row }">
            <span>{{ row.pmcRemark }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="ordererName" label="下单员姓名" width="120" align="center" />
        <el-table-column prop="merchandiser" label="跟单负责人" width="120" align="center" />
        <el-table-column prop="assembler" label="装配技师" width="120" align="center" />
        <el-table-column prop="bomCharge" label="研发工程师" width="120" align="center" />
        <el-table-column prop="pmcNotifyAssembly" label="PMC通知装配领料" width="150" align="center">
          <template #default="{ row }">
            <span>{{ row.pmcNotifyAssembly || '领料' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="assemblyStartTime" label="装配领料开工时间" width="150" align="center" />
        <el-table-column prop="partBomCount" label="零件BOM清单" width="120" align="center" />
        <el-table-column prop="standardBomCount" label="标准件BOM清单" width="120" align="center" />
        <el-table-column prop="totalProductionDrawings" label="生产图纸总数" width="120" align="center" />
        <el-table-column prop="uncompletedProduction" label="生产未完成图纸数" width="120" align="center" />
        <el-table-column prop="totalOutsourced" label="外发图纸总数" width="120" align="center" />
        <el-table-column prop="uncompletedOutsourced" label="外发图纸未完成数" width="120" align="center" />
        <el-table-column prop="totalSheetMetal" label="钣金图纸总数" width="120" align="center" />
        <el-table-column prop="uncompletedSheetMetal" label="钣金图纸未完成数" width="120" align="center" />
        <el-table-column prop="qaDrawingCount" label="QA图纸数" width="120" align="center" />
        <el-table-column prop="totalStandardParts" label="标准件总项数" width="120" align="center" />
        <el-table-column prop="uncompletedStandardParts" label="标准件未完成项数" width="120" align="center" />
        <el-table-column prop="soNo" label="SO_NO" width="120" align="center" />
        <el-table-column prop="soItm" label="SO_ITM" width="120" align="center" />
        <el-table-column prop="profitCenter" label="利润中心" width="120" align="center" />
        <el-table-column prop="partType" label="零件分类" width="120" align="center" />
        <el-table-column prop="planType" label="计划类型" width="120" align="center" />
        <el-table-column prop="status1" label="状态1" width="120" align="center" />
        <el-table-column prop="remark" label="备注" width="120" align="center" />
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="totalRecords"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-card>

    <el-dialog v-model="editDialogVisible" title="修改数据" width="70%" :before-close="handleClose">
      <el-form :model="editForm" label-width="150px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="MO号">
              <el-input v-model="editForm.moNo" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="装配异常备注">
              <el-select v-model="editForm.assemblerExceptionRemark" style="width: 100%">
                <el-option label="工程设计变更" value="工程设计变更" />
                <el-option label="工程师修改零件" value="工程师修改零件" />
                <el-option label="零件/标准件漏下" value="零件/标准件漏下" />
                <el-option label="缺少刻字/铭牌信息" value="缺少刻字/铭牌信息" />
                <el-option label="零件品质问题" value="零件品质问题" />
                <el-option label="工程师调试" value="工程师调试" />
                <el-option label="订单积压" value="订单积压" />
                <el-option label="标准件退货" value="标准件退货" />
                <el-option label="其它" value="其它" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="QA异常备注">
              <el-select v-model="editForm.qaExceptionRemark" style="width: 100%">
                <el-option label="供应商未交齐少数" value="供应商未交齐少数" />
                <el-option label="其它" value="其它" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="PMC备注">
              <el-input v-model="editForm.pmcRemark" type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="PMC通知装配领料">
              <el-select v-model="editForm.pmcNotifyAssembly" style="width: 100%">
                <el-option label="领料" value="领料" />
                <el-option label="齐料" value="齐料" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <el-input v-model="editForm.remark" type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmEdit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import {
  ElButton,
  ElCard,
  ElCheckbox,
  ElDatePicker,
  ElForm,
  ElFormItem,
  ElIcon,
  ElInput,
  ElMessage,
  ElPagination,
  ElSelect,
  ElTable,
  ElTableColumn
} from 'element-plus';
import { Back, Check, Close, Delete, Download, Edit, Refresh, Search } from '@element-plus/icons-vue';
import { exportToExcel } from '@/utils/excel';
import { getMerchandiserList } from '@/api/pmc/merchandiser';
import { getProfitCenterList } from '@/api/pmc/statis2';
import { checkPermi } from '@/utils/permission';
import { useUserStore } from '@/store/modules/user';
import { loadStatis6Data, getStatis6Data, saveStatis6Data } from '@/api/pmc/statis6';

// 搜索表单数据
const searchForm = reactive({
  bargainDate: [],
  deliveryDate: [],
  customerCode: '',
  drNumber: '',
  moNumber: '',
  profitCenter: '',
  merchandiser: '',
  assemblyTechnician: '',
  status: '已装配',
  isClosed: 'N'
});

// 响应式数据
const loading = ref(false);
const exportLoading = ref(false);
const enableSort = ref(false);
const totalRecords = ref(0);
const selectedRows = ref([]);
const enable = ref(false);
const mData = ref<string[]>([]);
const profitCenters = ref<string[]>([]);

// 编辑对话框相关
const editDialogVisible = ref(false);
const editForm = ref({
  moNo: '',
  drItm: '',
  assemblerExceptionRemark: '',
  qaExceptionRemark: '',
  pmcRemark: '',
  pmcNotifyAssembly: '待通知',
  assemblyStartTime: '',
  remark: ''
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

// 模拟数据
const tableData = ref([
  {
    id: 1,
    moNo: '240274040001',
    drItm: '0010',
    drawNumber: 'SS From7x Kingboo Retrofit',
    custNo: '10343',
    orderDate: '2025-07-18 14:00:00',
    custPoNo: '1800723742',
    deliveryDate: '2025-08-08',
    pmcReqDate: '2025-08-01',
    orderQty: 1,
    lastOpTime: '2025-07-18 16:12:00',
    assemblerExceptionRemark: '',
    qaExceptionRemark: '',
    pmcRemark: '',
    ordererName: '庾燕君',
    merchandiser: '肖伟',
    assembler: '黎配威',
    bomCharge: '22446耿尧',
    pmcNotifyAssembly: '',
    assemblyStartTime: '2025-08-01 09:00:00',
    partBomCount: 25,
    standardBomCount: 15,
    totalProductionDrawings: 30,
    uncompletedProduction: 5,
    totalOutsourced: 10,
    uncompletedOutsourced: 2,
    totalSheetMetal: 8,
    uncompletedSheetMetal: 1,
    qaDrawingCount: 3,
    totalStandardParts: 20,
    uncompletedStandardParts: 4,
    soNo: 'SO20250718001',
    soItm: '001',
    profitCenter: 'PC001',
    partType: '电子件',
    planType: 'MTO',
    status1: 1,
    remark: ''
  }
]);

// 下拉选项数据
// const merchandiserList = ref(['庾燕君', '肖伟', '黎配威', '耿尧']);
// const profitCenters = ref(['PC001', 'PC002', 'PC003']);

// 计算分页后的数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return tableData.value.slice(start, end);
});

// 加载负责人数据
const loadMerchandiserData = async () => {
  try {
    const res = await getMerchandiserList();
    mData.value = res.data;
  } catch (err: any) {
    ElMessage.error(err.message);
  }
};

// 加载利润中心数据
const loadProfitCenterData = async () => {
  try {
    const res = await getProfitCenterList();
    profitCenters.value = res.data;
  } catch (err: any) {
    ElMessage.error(err.message);
  }
};

// 查询方法
const handleQuery = async () => {
  loading.value = true;
  try {
    // 调用实际的API查询数据
    const res = await getStatis6Data(searchForm);
    tableData.value = res.data || [];

    totalRecords.value = tableData.value.length;
    currentPage.value = 1;
    ElMessage.success('查询成功');
  } catch (err: any) {
    ElMessage.error('查询失败：' + (err.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 修改方法
const handleModify = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要修改的记录');
    return;
  }

  // 初始化编辑表单
  editForm.value = {
    ...selectedRows.value[0],
    pmcNotifyAssembly: selectedRows.value[0].pmcNotifyAssembly || '待通知',
    assemblyStartTime: selectedRows.value[0].assemblyStartTime || ''
  };

  // 显示对话框
  editDialogVisible.value = true;
};

// 确认编辑方法
const confirmEdit = async () => {
  try {
    // 调用保存API更新数据
    await saveStatis6Data([editForm.value]);

    // 更新表格数据
    const index = tableData.value.findIndex(
      item => item.moNo === editForm.value.moNo && item.drItm === editForm.value.drItm
    );

    if (index !== -1) {
      tableData.value[index] = {
        ...tableData.value[index],
        ...editForm.value
      };
    }

    ElMessage.success('修改成功');
    // 关闭对话框
    editDialogVisible.value = false;
  } catch (err: any) {
    ElMessage.error('修改失败：' + (err.message || '未知错误'));
  }
};

// 对话框关闭处理
const handleClose = (done: () => void) => {
  ElMessage.info('编辑已取消');
  done();
};


// 导出方法
const handleExport = async () => {
  if (tableData.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  exportLoading.value = true;
  try {
    const exportData = tableData.value.map((item) => ({
      'ID': item.id,
      'MO号': item.moNo,
      'DR项次': item.drItm,
      '图号': item.drawNumber,
      '客户代码': item.custNo,
      '接单日期': item.orderDate,
      '客户PO号': item.custPoNo,
      '客户要求交期': item.deliveryDate,
      'PMC要求交期': item.pmcReqDate,
      '订单数量': item.orderQty,
      '末工序时间': item.lastOpTime,
      '装配异常备注': item.assemblerExceptionRemark,
      'QA异常备注': item.qaExceptionRemark,
      'PMC备注': item.pmcRemark,
      '下单员姓名': item.ordererName,
      '跟单负责人': item.merchandiser,
      '装配技师': item.assembler,
      '研发工程师': item.bomCharge,
      'PMC通知装配领料': item.pmcNotifyAssembly || '领料',
      '装配领料开工时间': item.assemblyStartTime,
      '零件BOM清单': item.partBomCount,
      '标准件BOM清单': item.standardBomCount,
      '生产图纸总数': item.totalProductionDrawings,
      '生产未完成图纸数': item.uncompletedProduction,
      '外发图纸总数': item.totalOutsourced,
      '外发图纸未完成数': item.uncompletedOutsourced,
      '钣金图纸总数': item.totalSheetMetal,
      '钣金图纸未完成数': item.uncompletedSheetMetal,
      'QA图纸数': item.qaDrawingCount,
      '标准件总项数': item.totalStandardParts,
      '标准件未完成项数': item.uncompletedStandardParts,
      'SO_NO': item.soNo,
      'SO_ITM': item.soItm,
      '利润中心': item.profitCenter,
      '零件分类': item.partType,
      '计划类型': item.planType,
      '状态1': item.status1,
      '备注': item.remark
    }));

    const headers = {
      'MO号': 'MO号',
      'DR项次': 'DR项次',
      '图号': '图号',
      '客户代码': '客户代码',
      '接单日期': '接单日期',
      '客户PO号': '客户PO号',
      '客户要求交期': '客户要求交期',
      'PMC要求交期': 'PMC要求交期',
      '订单数量': '订单数量',
      '末工序时间': '末工序时间',
      '装配异常备注': '装配异常备注',
      'QA异常备注': 'QA异常备注',
      'PMC备注': 'PMC备注',
      '下单员姓名': '下单员姓名',
      '跟单负责人': '跟单负责人',
      '装配技师': '装配技师',
      '研发工程师': '研发工程师',
      'PMC通知装配领料': 'PMC通知装配领料',
      '装配领料开工时间': '装配领料开工时间',
      '零件BOM清单': '零件BOM清单',
      '标准件BOM清单': '标准件BOM清单',
      '生产图纸总数': '生产图纸总数',
      '生产未完成图纸数': '生产未完成图纸数',
      '外发图纸总数': '外发图纸总数',
      '外发图纸未完成数': '外发图纸未完成数',
      '钣金图纸总数': '钣金图纸总数',
      '钣金图纸未完成数': '钣金图纸未完成数',
      'QA图纸数': 'QA图纸数',
      '标准件总项数': '标准件总项数',
      '标准件未完成项数': '标准件未完成项数',
      'SO_NO': 'SO_NO',
      'SO_ITM': 'SO_ITM',
      '利润中心': '利润中心',
      '零件分类': '零件分类',
      '计划类型': '计划类型',
      '状态1': '状态1',
      '备注': '备注'
    };

    const fileName = `装配部治具装配状态一览表_${new Date().getTime()}`;
    await exportToExcel(exportData, headers, fileName, '装配部治具装配状态一览表', 'E6A23C');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    exportLoading.value = false;
  }
};

// 加载新数据方法
const handleLoadNewData = async () => {
  loading.value = true;
  try {
    // 调用实际的API重新加载数据
    const res = await loadStatis6Data();
    ElMessage.success('数据已刷新');
  } catch (err: any) {
    ElMessage.error('加载新数据失败：' + (err.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 重置表单方法
const handleReset = () => {
  // 重置搜索表单为初始值
  searchForm.bargainDate = [];
  searchForm.deliveryDate = [];
  searchForm.customerCode = '';
  searchForm.drNumber = '';
  searchForm.moNumber = '';
  searchForm.profitCenter = '';
  searchForm.merchandiser = '';
  searchForm.assemblyTechnician = '';
  searchForm.status = '已装配';
  searchForm.isClosed = 'N';
  
  ElMessage.success('表单已重置');
};

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
};

// 处理分页变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
};

const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 初始化
onMounted(() => {
  loadProfitCenterData();
  if (checkPermi(['pmc:mo:statis6'])) {
    loadMerchandiserData();
  } else {
    searchForm.merchandiser = useUserStore().nickname;
    enable.value = true;
  }
});
</script>

<style scoped>
.el-date-picker {
  width: 240px;
}

.el-select {
  width: 200px;
}

.el-input {
  width: 100%;
}
</style>
