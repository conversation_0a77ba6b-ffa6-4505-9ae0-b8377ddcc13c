<template>
  <div class="p-4">
    <el-dialog v-model="dialogVisible" title="达成率及延误定义" width="50%">
      <el-icon name="info-filled" style="color: #409eff; margin-right: 10px"></el-icon>
      <span>各项达成率定义：</span>
      <ol style="margin-top: 0px">
        <li>达成率 = 交期延误天数小于等于0的图纸数/图纸总数</li>
        <li>生产延误天数 = "MFG/QA收货时间" - PMC要求交期</li>
        <li>钣金件延误天数 = 钣金得QA检测或者钣金烧焊完成时间 - PMC要求交期</li>
        <li>外发延误天数 = 外发收货送检时间 - PMC要求交期</li>
        <li>项目延误天数 = 成品仓时间 - 客户要求交期</li>
        <li>图纸总数延误天数 = 进半成品仓时间 - PMC要求交期</li>
        <li>特采件延误天数 = 进半成品仓时间 - PMC要求交期</li>
      </ol>
    </el-dialog>
    <!-- 搜索卡片 -->
    <el-card class="mb-6 shadow-md">
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">条件搜索</h3>
          <span style="color: #0a11b4">统计对象:未完成的订单(不含标准件)</span>
          <el-button type="warning" @click="dialogVisible = true">达成率定义</el-button>
        </div>
      </template>

      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="接单日期">
              <el-date-picker
                v-model="searchForm.bargainDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="客户代码">
              <el-input v-model="searchForm.customerCode" clearable/>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="MO单号">
              <el-input v-model="searchForm.moNumber" clearable/>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="PMC要求交期">
              <el-date-picker
                v-model="searchForm.pmcReqDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="负责人">
              <el-select v-model="searchForm.merchandiser" placeholder="" style="width: 150px" clearable :disabled="enable">
                <el-option v-for="item in mData" :key="item" :label="item" :value="item" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" class="flex justify-end">
            <el-button type="primary" :loading="loading" @click="handleQuery">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon>
                <Refresh />
              </el-icon>
              重置
            </el-button>
            <el-button type="success" :loading="exportLoading" @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>
              导出Excel
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-table :data="paginatedData" :loading="loading" border stripe>
      <el-table-column prop="engineer" label="工程师" width="120" />
      <el-table-column prop="projectCount" label="项目数" width="100">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'projectDetails')">{{ row.projectCount }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="completedProjects" label="项目达成" width="120">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'completedProjectDetails')">{{ row.completedProjects }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="delayedProjects" label="项目延误" width="120">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'delayedProjectDetails')">{{ row.delayedProjects }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="achievementRate" label="项目达成率" width="130" />
      <el-table-column prop="drawingTotal" label="图纸总数" width="120">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'drawingTotalDetails')">{{ row.drawingTotal }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="deliveryOnTime" label="交期达成" width="120">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'deliveryOnTimeDetails')">{{ row.deliveryOnTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="delayedDelivery" label="交期延误" width="120">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'delayedDeliveryDetails')">{{ row.delayedDelivery }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="completionRate" label="达成率" width="120" />
      <el-table-column prop="productionDrawings" label="生产图纸数" width="140">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'productionDrawingDetails')">{{ row.productionDrawings }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="productionDeliveryRate" label="生产交货达成率" width="170" />
      <el-table-column prop="stampingDrawings" label="钣金图纸数" width="140">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'stampingDrawingDetails')">{{ row.stampingDrawings }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="stampingDeliveryRate" label="钣金交货达成率" width="170" />
      <el-table-column prop="externalDrawings" label="外发图纸数" width="140">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'externalDrawingDetails')">{{ row.externalDrawings }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="externalDeliveryRate" label="外发交货达成率" width="170" />
    </el-table>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailVisible" title="详细信息" width="80%" append-to-body>
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-semibold">详细信息 (共{{ detailData.length }}条记录)</span>
          <el-button type="primary" :loading="detailExportLoading" @click="handleDetailExport">
            <el-icon><Download /></el-icon>
            下载Excel
          </el-button>
        </div>
      </template>

      <el-table :data="paginatedDetailData" border stripe>
        <el-table-column prop="id" label="ID" width="80"/>
        <el-table-column prop="moState" label="状态" width="100"/>
        <el-table-column prop="projectDescription" label="项目描述" width="150"/>
        <el-table-column prop="mfgDept" label="制造部门" width="120"/>
        <el-table-column prop="soNo" label="SO_NO" width="100"/>
        <el-table-column prop="soItm" label="SO_ITM" width="100"/>
        <el-table-column prop="moNo" label="MO_NO" width="100"/>
        <el-table-column prop="drawNumber" label="图号" width="120"/>
        <el-table-column prop="partVer" label="版本" width="80"/>
        <el-table-column prop="custNo" label="客户代码" width="100"/>
        <el-table-column prop="orderQty" label="订单数量" width="100"/>
        <el-table-column prop="productionQty" label="生产数量" width="100"/>
        <el-table-column prop="outstandingQty" label="欠出货数量" width="120"/>
        <el-table-column prop="bargainDate" label="接单日期" width="100"/>
        <el-table-column prop="deliveryDate" label="客户要求交期" width="120"/>
        <el-table-column prop="pmcReqDate" label="PMC要求交期" width="120"/>
        <el-table-column prop="inDate" label="入仓日期" width="100"/>
        <el-table-column prop="lastOpTime" label="末工序时间" width="120"/>
        <el-table-column prop="delayDays" label="延误天数" width="100"/>
        <el-table-column prop="poNo" label="客户PO号" width="120"/>
        <el-table-column prop="moRem" label="备注" width="150"/>
        <el-table-column prop="cusRem" label="CUS_REM" width="120"/>
        <el-table-column prop="pmcRem" label="PMC备注" width="120"/>
        <el-table-column prop="orderName" label="下单员姓名" width="120"/>
        <el-table-column prop="merchandiserCharge" label="跟单负责人" width="120"/>
        <el-table-column prop="groupTime" label="分组时间" width="120"/>
        <el-table-column prop="profitCenter" label="利润中心" width="100"/>
        <el-table-column prop="partType" label="零件分类" width="100"/>
        <el-table-column prop="urgent" label="URGENT" width="80"/>
        <el-table-column prop="orderType" label="订单类别" width="100"/>
        <el-table-column prop="orderDescription" label="类别描述" width="150"/>
        <el-table-column prop="planType" label="计划类型" width="100"/>
        <el-table-column prop="partId" label="品号" width="100"/>
        <el-table-column prop="internalOrderNo" label="内部订单号" width="120"/>
        <el-table-column prop="upMo" label="上层订单号" width="120"/>
        <el-table-column prop="drNo" label="DR_NO" width="100"/>
        <el-table-column prop="drItm" label="DR_ITM" width="100"/>
        <el-table-column prop="act" label="动作" width="100"/>
        <el-table-column prop="workshop" label="生产车间" width="120"/>
      </el-table>

      <!-- 详情对话框分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="detailCurrentPage"
          v-model:page-size="detailPageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="detailData.length"
          @size-change="handleDetailSizeChange"
          @current-change="handleDetailCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-dialog>

    <!-- 分页 -->
    <div class="mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        :total="totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="shadow-sm"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import {
  ElButton,
  ElCard,
  ElDatePicker,
  ElDialog,
  ElForm,
  ElFormItem,
  ElIcon,
  ElInput,
  ElMessage,
  ElOption,
  ElPagination,
  ElSelect,
  ElTable,
  ElTableColumn
} from 'element-plus';
import { getMerchandiserList } from '@/api/pmc/merchandiser.js';
import { checkPermi } from '@/utils/permission';
import { useUserStore } from '@/store/modules/user';
import { Download, Refresh, Search } from '@element-plus/icons-vue';
import { download } from '@/utils/request';
import { getStatis1Data } from '@/api/pmc/statis1';
import { exportToExcel } from '@/utils/excel';

const enable = ref(false);
const dialogVisible = ref(false);
const mData = ref<string[]>([]);
// 响应式表单数据
const searchForm = reactive({
  bargainDate: [],
  pmcReqDate: [],
  merchandiser: '',
  customerCode: '',
  moNumber: ''
});

// 表格数据
const tableData = ref<[]>([]);

// 分页相关变量
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

// 计算分页后的数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return tableData.value.slice(start, end);
});

// 计算详情对话框分页后的数据
const paginatedDetailData = computed(() => {
  const start = (detailCurrentPage.value - 1) * detailPageSize.value;
  const end = start + detailPageSize.value;
  return detailData.value.slice(start, end);
});

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
};

// 显示详情
const showDetail = (row: any, detailType: string) => {
  // 根据点击的列显示对应的详情数据
  detailData.value = row[detailType] || [];
  currentDetailType.value = detailType;
  // 重置详情对话框分页状态
  detailCurrentPage.value = 1;
  detailPageSize.value = 10;
  detailVisible.value = true;
};

// 处理详情对话框分页变化
const handleDetailSizeChange = (val: number) => {
  detailPageSize.value = val;
  detailCurrentPage.value = 1;
};

const handleDetailCurrentChange = (val: number) => {
  detailCurrentPage.value = val;
};

const loading = ref(false);
const exportLoading = ref(false);

// 详情对话框相关变量
const detailVisible = ref(false);
const detailExportLoading = ref(false);
const detailData = ref([]);
const currentDetailType = ref(''); // 记录当前详情类型，用于生成文件名

// 详情对话框分页相关变量
const detailCurrentPage = ref(1);
const detailPageSize = ref(10);

// 查询方法
const handleQuery = async () => {
  loading.value = true;
  try {
    const res = await getStatis1Data(searchForm);
    tableData.value = res.data;
    totalItems.value = res.data.length;
    currentPage.value = 1; // 重置到第一页
  } catch (err) {
    ElMessage.error(err);
  } finally {
    loading.value = false;
  }
};

// 重置方法
const handleReset = () => {
  searchForm.bargainDate = [];
  searchForm.pmcReqDate = [];
  searchForm.merchandiser = '';
  searchForm.customerCode = '';
  searchForm.moNumber = '';
  currentPage.value = 1; // 重置到第一页
  tableData.value = [];
  totalItems.value = 0;
};

// 导出方法
const handleExport = async () => {
  if (!tableData.value || tableData.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  exportLoading.value = true;
  try {
    // 定义主表格的表头映射
    const mainHeaders = {
      engineer: '工程师',
      projectCount: '项目数',
      completedProjects: '项目达成',
      delayedProjects: '项目延误',
      achievementRate: '项目达成率',
      drawingTotal: '图纸总数',
      deliveryOnTime: '交期达成',
      delayedDelivery: '交期延误',
      completionRate: '达成率',
      productionDrawings: '生产图纸数',
      productionDeliveryRate: '生产交货达成率',
      stampingDrawings: '钣金图纸数',
      stampingDeliveryRate: '钣金交货达成率',
      externalDrawings: '外发图纸数',
      externalDeliveryRate: '外发交货达成率'
    };

    const fileName = `自动化PMC订单交货达成统计_${new Date().getTime()}`;

    // 使用绿色表头（与原导出按钮颜色保持一致）
    await exportToExcel(tableData.value, mainHeaders, fileName, '自动化PMC订单交货达成统计', '67C23A');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    exportLoading.value = false;
  }
};

// 详情导出方法
const handleDetailExport = async () => {
  if (!detailData.value || detailData.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  detailExportLoading.value = true;
  try {
    // 定义表头映射
    const headers = {
      id: 'ID',
      moState: '状态',
      projectDescription: '项目描述',
      mfgDept: '制造部门',
      soNo: 'SO_NO',
      soItm: 'SO_ITM',
      moNo: 'MO_NO',
      drawNumber: '图号',
      partVer: '版本',
      custNo: '客户代码',
      orderQty: '订单数量',
      productionQty: '生产数量',
      outstandingQty: '欠出货数量',
      bargainDate: '接单日期',
      deliveryDate: '客户要求交期',
      pmcReqDate: 'PMC要求交期',
      inDate: '入仓日期',
      lastOpTime: '末工序时间',
      delayDays: '延误天数',
      poNo: '客户PO号',
      moRem: '备注',
      cusRem: 'CUS_REM',
      pmcRem: 'PMC备注',
      orderName: '下单员姓名',
      merchandiserCharge: '跟单负责人',
      groupTime: '分组时间',
      profitCenter: '利润中心',
      partType: '零件分类',
      urgent: 'URGENT',
      orderType: '订单类别',
      orderDescription: '类别描述',
      planType: '计划类型',
      partId: '品号',
      internalOrderNo: '内部订单号',
      upMo: '上层订单号',
      drNo: 'DR_NO',
      drItm: 'DR_ITM',
      act: '动作',
      workshop: '生产车间'
    };

    // 生成文件名
    const typeNameMap: Record<string, string> = {
      projectDetails: '项目数',
      completedProjectDetails: '项目达成',
      delayedProjectDetails: '项目延误',
      drawingTotalDetails: '图纸总数',
      deliveryOnTimeDetails: '交期达成',
      delayedDeliveryDetails: '交期延误',
      productionDrawingDetails: '生产图纸数',
      stampingDrawingDetails: '钣金图纸数',
      externalDrawingDetails: '外发图纸数'
    };

    const typeName = typeNameMap[currentDetailType.value] || '详情数据';
    const fileName = `自动化PMC订单交货达成统计_${typeName}_${new Date().getTime()}`;

    // 使用蓝色表头
    await exportToExcel(detailData.value, headers, fileName, '详情数据', '4472C4');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    detailExportLoading.value = false;
  }
};

const loadMerchandiserData = async () => {
  await getMerchandiserList()
    .then((res) => {
      mData.value = res.data;
    })
    .catch((err) => {
      ElMessage.error(err);
    });
};

onMounted(() => {
  if (checkPermi(['pmc:mo:statis1'])) {
    loadMerchandiserData();
  } else {
    searchForm.merchandiser = useUserStore().nickname;
    enable.value = true;
  }
  console.log(useUserStore().nickname);
});
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}
</style>
