<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="客户代码" prop="customerCode">
        <el-input
          v-model="queryParams.customerCode"
          placeholder="请输入客户代码"
          clearable
          style="width: 160px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="责任人" prop="responsiblePerson">
        <el-select
          v-model="queryParams.responsiblePerson"
          placeholder="请选择责任人"
          clearable
          style="width: 160px"
        >
          <el-option
            v-for="dict in responsiblePersonOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="DR单号" prop="drNo">
        <el-input
          v-model="queryParams.drNo"
          placeholder="请输入DR单号"
          clearable
          style="width: 160px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="DR项次" prop="drItem">
        <el-input
          v-model="queryParams.drItem"
          placeholder="请输入DR项次"
          clearable
          style="width: 160px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Refresh" @click="loadData">刷新</el-button>
        <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['pmc:reports:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tableData" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="订单类别" align="center" prop="orderType" />
      <el-table-column label="出货地点" align="center" prop="shipmentLocation" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="客户代码" align="center" prop="customerCode" />
      <el-table-column label="客户PO号" align="center" prop="customerPo" />
      <el-table-column label="接单日期" align="center" prop="orderDate">
        <template #default="scope">
          <span>{{ parseTime(scope.row.orderDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="DR单号" align="center" prop="drNo" />
      <el-table-column label="DR项次" align="center" prop="drItem" />
      <el-table-column label="PMC要求交期" align="center" prop="pmcRequiredDate">
        <template #default="scope">
          <span>{{ parseTime(scope.row.pmcRequiredDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="statusOptions" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="MO单号" align="center" prop="moNo" />
      <el-table-column label="图号" align="center" prop="drawingNo" />
      <el-table-column label="描述" align="center" prop="description" />
      <el-table-column label="订单数量" align="center" prop="orderQty" />
      <el-table-column label="客户要求交期" align="center" prop="customerRequiredDate">
        <template #default="scope">
          <span>{{ parseTime(scope.row.customerRequiredDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="进度概况及备注" align="center" prop="progressRemark">
        <template #default="scope">
          <el-input 
            v-model="scope.row.progressRemark" 
            type="textarea"
            size="small" 
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="交货风险描述" align="center" prop="deliveryRiskDesc">
        <template #default="scope">
          <el-input 
            v-model="scope.row.deliveryRiskDesc" 
            type="textarea"
            size="small" 
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column label="自制欠数" align="center" prop="selfMadeShortage"/>
      <el-table-column label="钣金欠数" align="center" prop="sheetMetalShortage"/>
      <el-table-column label="外发欠数" align="center" prop="outsourcingShortage"/>
      <el-table-column label="标准件欠数" align="center" prop="standardPartsShortage"/>
      <el-table-column label="时间节点" align="center" prop="timeNode"/>
      <!-- 新增字段 - 时间节点相关列 -->
      <el-table-column label="下图日期" align="center" prop="drawingReleaseDate">
        <template #default="scope">
          <el-date-picker
            v-model="scope.row.drawingReleaseDate"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            size="small"
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="机架A" align="center" prop="frameA">
        <template #default="scope">
          <el-input 
            v-model="scope.row.frameA" 
            size="small" 
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="机架B" align="center" prop="frameB">
        <template #default="scope">
          <el-input 
            v-model="scope.row.frameB" 
            size="small" 
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="零件齐料时间" align="center" prop="partsReadyTime">
        <template #default="scope">
          <el-date-picker
            v-model="scope.row.partsReadyTime"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            size="small"
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="标准件齐料时间" align="center" prop="standardPartsReadyTime">
        <template #default="scope">
          <el-date-picker
            v-model="scope.row.standardPartsReadyTime"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            size="small"
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="装配开始" align="center" prop="assemblyStart">
        <template #default="scope">
          <el-date-picker
            v-model="scope.row.assemblyStart"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            size="small"
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="装配结束" align="center" prop="assemblyEnd">
        <template #default="scope">
          <el-date-picker
            v-model="scope.row.assemblyEnd"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            size="small"
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="调试开始" align="center" prop="debugStart">
        <template #default="scope">
          <el-date-picker
            v-model="scope.row.debugStart"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            size="small"
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="调试结束" align="center" prop="debugEnd">
        <template #default="scope">
          <el-date-picker
            v-model="scope.row.debugEnd"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            size="small"
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="出货ETD" align="center" prop="shipmentETD">
        <template #default="scope">
          <el-date-picker
            v-model="scope.row.shipmentETD"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            size="small"
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      
      <!-- 添加其他遗漏字段 -->
      <el-table-column label="装配车间" align="center" prop="assemblyWorkshop">
        <template #default="scope">
          <el-input 
            v-model="scope.row.assemblyWorkshop" 
            size="small" 
            @change="handleCellUpdate(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="项目经理" align="center" prop="projectManager"/>
      <el-table-column label="下单员姓名" align="center" prop="orderPersonName"/>
      <el-table-column label="BOM责任人" align="center" prop="bomResponsible"/>
      <el-table-column label="usr" align="center" prop="usr"/>
      <el-table-column label="us_name" align="center" prop="us_name"/>
    </el-table>
    
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus';
import { parseTime } from '@/utils/ruoyi';
import { getProjectScheduleList, loadDataFromMaster, updateProjectSchedule, deleteProjectSchedule, exportProjectSchedule, getResponsiblePersonList } from '@/api/pmc/mo/pmcProject/index';
import { ProjectScheduleQuery, ProjectScheduleData, ResponsiblePerson } from '@/api/pmc/mo/pmcProject/types';

// 使用导入的类型定义
type QueryParams = ProjectScheduleQuery;
 
// 使用导入的类型定义
type TableData = ProjectScheduleData;

// 查询参数
const queryParams = reactive<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  customerCode: '',
  responsiblePerson: '',
  drNo: '',
  drItem: ''   
});

// 表格数据
const tableData = ref<TableData[]>([]);
const total = ref(0);
const loading = ref(false);
const showSearch = ref(true);
const ids = ref<number[]>([]);
const single = ref(true);
const multiple = ref(true);

// 表单参数
const open = ref(false);
const title = ref('');

// 下拉选项
const responsiblePersonOptions = ref<{value: string, label: string}[]>([]);

const statusOptions = ref([
  { value: '0', label: '进行中' },
  { value: '1', label: '已完成' },
  { value: '2', label: '延期' }
]);

// 查询列表
const getList = () => {
  loading.value = true;
  getProjectScheduleList(queryParams)
    .then(response => {
      tableData.value = response.data.rows;
      total.value = response.data.total;
    })
    .catch(error => {
      console.error('获取数据失败:', error);
      ElMessage.error('获取数据失败');
    })
    .finally(() => {
      loading.value = false;
    });
};

const loadDataList = () => {
  loading.value = true;
  loadDataFromMaster()
    .then(response => {
      
    })
    .catch(error => {
      console.error('获取数据失败:', error);
      ElMessage.error('获取数据失败');
    })
    .finally(() => {
      loading.value = false;
    });
};



// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

// 刷新按钮操作
const loadData = () => {
  loadDataList();
};


// 重置按钮操作
const resetQuery = () => {
  queryParams.customerCode = '';
  queryParams.responsiblePerson = '';
  queryParams.drNo = '';
  handleQuery();
};

// 多选框选中数据
const handleSelectionChange = (selection: TableData[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 导出按钮操作
const handleExport = () => {
  exportProjectSchedule(queryParams)
    .then(response => {
      const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = 'PMC项目进度表_' + new Date().getTime() + '.xlsx';
      link.click();
      URL.revokeObjectURL(link.href);
      ElMessage.success('导出成功');
    })
    .catch(error => {
      console.error('导出失败:', error);
      ElMessage.error('导出失败，请稍后重试');
    });
};

/** 单元格内容更新处理 */
function handleCellUpdate(row: TableData) {
  // 调用更新API保存修改后的数据
  updateProjectSchedule(row)
    .then(() => {
      ElMessage.success('修改成功');
    })
    .catch(error => {
      console.error('修改失败:', error);
      ElMessage.error('修改失败，请稍后重试');
    });
}

// 获取责任负责人列表
const loadResponsiblePersonList = () => {
  getResponsiblePersonList()
    .then(response => {
      responsiblePersonOptions.value = response.data.map((item: any) => {
        return { value: item.name, label: item.name };
      });
    })
    .catch(() => {
      // 如果API尚未实现，使用模拟数据
      responsiblePersonOptions.value = [
        { value: '张三', label: '张三' },
        { value: '李四', label: '李四' },
        { value: '王五', label: '王五' }
      ];
    });
};

onMounted(() => {
  getList();
  loadResponsiblePersonList();
});
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.dialog-footer {
  padding: 20px 0 0;
  text-align: right;
}

.mb8 {
  margin-bottom: 8px;
}
</style>