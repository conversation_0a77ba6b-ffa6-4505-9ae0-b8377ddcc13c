<template>
  <div class="p-4">
    <!-- 搜索卡片 -->
    <el-card class="mb-6 shadow-md">
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">条件搜索</h3>
        </div>
      </template>

      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-row :gutter="24" class="items-center mb-6">
          <el-col :span="8">
            <el-form-item label="外发日期">
              <el-date-picker
                v-model="searchForm.outsourceDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 260px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="MO单号">
              <el-input v-model="searchForm.moNumber" placeholder="" style="width: 140px" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="厂商简称">
              <el-input v-model="searchForm.supplierName" placeholder="" style="width: 140px" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="MO状态">
              <el-input v-model="searchForm.moStatus" placeholder="" style="width: 120px" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24" class="items-center mb-6">
          <el-col :span="8">
            <el-form-item label="要求日期">
              <el-date-picker
                v-model="searchForm.deliveryDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 260px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="客户代码">
              <el-input v-model="searchForm.customerCode" placeholder="" style="width: 140px" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="关闭否">
              <el-select v-model="searchForm.closeStore" placeholder="" style="width: 140px">
                <el-option label="是" value="Y" />
                <el-option label="否" value="N" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户分类" class="text-red-500">
              <el-select v-model="searchForm.customerCategory" placeholder="" style="width: 140px">
                <el-option label="自动化" value="自动化" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24" class="items-center mb-6">
          <el-col :span="24" class="flex justify-end gap-3">
            <el-button type="primary" :loading="loading" @click="handleQuery" size="default">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <el-button @click="handleReset" size="default">
              <el-icon>
                <Refresh />
              </el-icon>
              重置
            </el-button>
            <el-button type="success" :loading="exportLoading" @click="handleExport" :disabled="isExportDisabled" size="default">
              <el-icon>
                <Download />
              </el-icon>
              导出
            </el-button>
          </el-col>
        </el-row>


      </el-form>
    </el-card>

    <!-- 统计信息标题 -->
    <div class="mb-4">
      <span class="text-red-500 font-semibold">统计信息</span>
    </div>

    <!-- 统计表格区域 -->
    <el-card class="shadow-md">
      <el-table :data="paginatedStatisData" border stripe>
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="profitCenter" label="利润中心" width="120" align="center" />
        <el-table-column prop="pmcFollower" label="PMC跟单员" width="120" align="center" />
        <el-table-column prop="statistics" label="统计" width="100" align="center">
          <template #default="{ row }">
            <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'statisticsDetails')">{{ row.statistics }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="deliveryAchievement" label="交期达成" width="100" align="center">
          <template #default="{ row }">
            <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'deliveryAchievementDetails')">{{ row.deliveryAchievement }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="achievementRate" label="达成率" width="100" align="center" />
        <el-table-column prop="pendingDelayConfirmation" label="待确认是否延误" width="150" align="center">
          <template #default="{ row }">
            <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'pendingDelayConfirmationDetails')">{{ row.pendingDelayConfirmation }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="statisData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailVisible" title="详细信息" width="90%" append-to-body>
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-semibold">详细信息 (共{{ detailData.length }}条记录)</span>
          <el-button type="primary" :loading="detailExportLoading" @click="handleDetailExport">
            <el-icon><Download /></el-icon>
            下载Excel
          </el-button>
        </div>
      </template>

      <el-table :data="paginatedDetailData" border stripe>
        <el-table-column prop="moState" label="MO状态" width="120" />
        <el-table-column prop="sheetCount" label="张数" width="80" />
        <el-table-column prop="drawNumber" label="图号" width="180" />
        <el-table-column prop="outTime" label="外发时间" width="150" />
        <el-table-column prop="supplier" label="供应商" width="120" />
        <el-table-column prop="vendorAbbreviation" label="厂商简称" width="100" />
        <el-table-column prop="moNumber" label="MO单号" width="140" />
        <el-table-column prop="processNumber" label="工序号" width="80" />
        <el-table-column prop="processTotalQty" label="工序总数量" width="100" />
        <el-table-column prop="processName" label="工序名称" width="100" />
        <el-table-column prop="workCenter" label="工作中心" width="100" />
        <el-table-column prop="receiveQty" label="收货数量" width="100" />
        <el-table-column prop="returnQty" label="退货数量" width="100" />
        <el-table-column prop="customerCode" label="客户代码" width="100" />
        <el-table-column prop="outDeliveryDate" label="外发交期" width="120" />
        <el-table-column prop="customerDeliveryDate" label="客户交期" width="120" />
        <el-table-column prop="processDescription" label="工序描述" width="200" show-overflow-tooltip />
        <el-table-column prop="scheduleStartDate" label="计划开始日期" width="150" />
        <el-table-column prop="scheduleEndDate" label="计划结束日期" width="150" />
        <el-table-column prop="materialDescription" label="物料描述" width="150" />
        <el-table-column prop="merchandiserCharge" label="跟单负责人" width="120" />
        <el-table-column prop="factory" label="工厂" width="80" />
        <el-table-column prop="poNumber" label="PO号" width="120" />
        <el-table-column prop="poItemNumber" label="PO项目号" width="100" />
        <el-table-column prop="poQty" label="PO数量" width="100" />
        <el-table-column prop="poDate" label="PO日期" width="120" />
        <el-table-column prop="pjNumber" label="项目号" width="140" />
        <el-table-column prop="upOrder" label="上级订单" width="140" />
        <el-table-column prop="group" label="组别" width="80" />
        <el-table-column prop="updateBatch" label="更新批次" width="140" />
        <el-table-column prop="delayDays" label="延误天数" width="100" />
        <el-table-column prop="profitCenter" label="利润中心" width="120" />
        <el-table-column prop="outMerchandiser" label="外发跟单员" width="120" />
        <el-table-column prop="lastProcessTime" label="最后工序时间" width="150" />
      </el-table>

      <!-- 详情对话框分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="detailCurrentPage"
          v-model:page-size="detailPageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="detailData.length"
          @size-change="handleDetailSizeChange"
          @current-change="handleDetailCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import {
  ElButton,
  ElCard,
  ElCheckbox,
  ElDatePicker,
  ElDialog,
  ElForm,
  ElFormItem,
  ElIcon,
  ElInput,
  ElMessage,
  ElPagination,
  ElSelect,
  ElOption,
  ElTable,
  ElTableColumn
} from 'element-plus';
import { Download, Refresh, Search } from '@element-plus/icons-vue';
import { exportToExcel } from '@/utils/excel';
import { getStatis7Data } from '@/api/pmc/statis7';



// 响应式表单数据
const searchForm = reactive({
  outsourceDateRange: null,
  deliveryDateRange: null,
  moNumber: '',
  supplierName: '',
  moStatus: '',
  customerCode: '',
  closeStore: 'N',
  customerCategory: '自动化'
});

// 统计数据
const statisData = ref([]);

// 分页相关变量
const currentPage = ref(1);
const pageSize = ref(10);

// 详情对话框分页相关变量
const detailCurrentPage = ref(1);
const detailPageSize = ref(10);

// 计算分页后的统计数据
const paginatedStatisData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return statisData.value.slice(start, end);
});

// 计算详情对话框分页后的数据
const paginatedDetailData = computed(() => {
  const start = (detailCurrentPage.value - 1) * detailPageSize.value;
  const end = start + detailPageSize.value;
  return detailData.value.slice(start, end);
});

// 加载状态
const loading = ref(false);
const exportLoading = ref(false);

// 详情对话框相关
const detailVisible = ref(false);
const detailExportLoading = ref(false);
const detailData = ref([]);
const currentDetailType = ref(''); // 记录当前详情类型，用于生成文件名

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
};

// 处理详情对话框页码变化
const handleDetailCurrentChange = (val: number) => {
  detailCurrentPage.value = val;
};

// 处理详情对话框每页条数变化
const handleDetailSizeChange = (val: number) => {
  detailPageSize.value = val;
  detailCurrentPage.value = 1;
};

// 显示详情
const showDetail = (row: any, detailType: string) => {
  // 根据点击的列显示对应的详情数据
  detailData.value = row[detailType] || [];
  currentDetailType.value = detailType;
  // 重置详情对话框分页状态
  detailCurrentPage.value = 1;
  detailPageSize.value = 10;
  detailVisible.value = true;
};

// 查询方法
const handleQuery = async () => {
  loading.value = true;
  try {
    // 这里应该调用实际的API
    const res = await getStatis7Data(searchForm);
    statisData.value = res.data || [];

    // 模拟查询延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    ElMessage.success('查询成功');
  } catch (err: any) {
    ElMessage.error('查询失败：' + (err.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 重置方法
const handleReset = () => {
  searchForm.outsourceDateRange = null;
  searchForm.deliveryDateRange = null;
  searchForm.moNumber = '';
  searchForm.supplierName = '';
  searchForm.moStatus = '';
  searchForm.customerCode = '';
  searchForm.closeStore = 'N';
  searchForm.customerCategory = '自动化';
  currentPage.value = 1;
};

// 导出方法
const handleExport = async () => {
  exportLoading.value = true;
  try {
    if (statisData.value.length === 0) {
      ElMessage.warning('没有数据可以导出');
      return;
    }

    const exportData = statisData.value.map((item) => ({
      'ID': item.id,
      '利润中心': item.profitCenter,
      'PMC跟单员': item.pmcFollower,
      '统计': item.statistics,
      '交期达成': item.deliveryAchievement,
      '达成率': item.achievementRate,
      '待确认是否延误': item.pendingDelayConfirmation
    }));

    const headers = {
      'ID': 'ID',
      '利润中心': '利润中心',
      'PMC跟单员': 'PMC跟单员',
      '统计': '统计',
      '交期达成': '交期达成',
      '达成率': '达成率',
      '待确认是否延误': '待确认是否延误'
    };

    const fileName = `电镀热处理统计分析表_${new Date().getTime()}`;
    await exportToExcel(exportData, headers, fileName, '统计信息', '409EFF');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    exportLoading.value = false;
  }
};

// 详情导出方法
const handleDetailExport = async () => {
  if (!detailData.value || detailData.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  detailExportLoading.value = true;
  try {
    // 定义表头映射
    const headers = {
      moState: 'MO状态',
      sheetCount: '张数',
      drawNumber: '图号',
      outTime: '外发时间',
      supplier: '供应商',
      vendorAbbreviation: '厂商简称',
      moNumber: 'MO单号',
      processNumber: '工序号',
      processTotalQty: '工序总数量',
      processName: '工序名称',
      workCenter: '工作中心',
      receiveQty: '收货数量',
      returnQty: '退货数量',
      customerCode: '客户代码',
      outDeliveryDate: '外发交期',
      customerDeliveryDate: '客户交期',
      processDescription: '工序描述',
      scheduleStartDate: '计划开始日期',
      scheduleEndDate: '计划结束日期',
      materialDescription: '物料描述',
      merchandiserCharge: '跟单负责人',
      factory: '工厂',
      poNumber: 'PO号',
      poItemNumber: 'PO项目号',
      poQty: 'PO数量',
      poDate: 'PO日期',
      pjNumber: '项目号',
      upOrder: '上级订单',
      group: '组别',
      updateBatch: '更新批次',
      delayDays: '延误天数',
      profitCenter: '利润中心',
      outMerchandiser: '外发跟单员',
      lastProcessTime: '最后工序时间'
    };

    // 生成文件名
    const typeNameMap: Record<string, string> = {
      statisticsDetails: '统计明细',
      deliveryAchievementDetails: '交期达成明细',
      pendingDelayConfirmationDetails: '待确认是否延误明细'
    };

    const typeName = typeNameMap[currentDetailType.value] || '详情数据';
    const fileName = `外发统计报表_${typeName}_${new Date().getTime()}`;

    // 使用蓝色表头
    await exportToExcel(detailData.value, headers, fileName, '详情数据', '4472C4');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    detailExportLoading.value = false;
  }
};

// 计算导出按钮是否禁用
const isExportDisabled = computed(() => {
  return statisData.value.length === 0;
});

onMounted(() => {
});
</script>

<style scoped>
/* 表格行选中样式 */
.el-table__row:hover {
  background-color: #f5f7fa !important;
}

.el-table__row.current-row {
  background-color: #e6f7ff !important;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
