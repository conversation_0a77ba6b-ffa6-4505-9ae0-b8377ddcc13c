<template>
  <div class="p-4">
    <!-- 搜索卡片 -->
    <el-card class="mb-6 shadow-md">
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">条件搜索</h3>
          <span class="text-red-500 text-sm">说明:双击状态查看扫描记录;双击MO号查看工艺制程;双击图号调取图纸</span>
        </div>
      </template>

      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-row :gutter="20" class="items-center">
          <el-col :span="6">
            <el-form-item label="日期类别">
              <el-select v-model="searchForm.dateType" placeholder="请选择日期类别" style="width: 250px">
                <el-option label="接单日期" value="orderDate" />
                <el-option label="客户要求交期" value="customerDelivery" />
                <el-option label="PMC要求交期" value="pmcDelivery" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="日期范围">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 250px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" class="flex justify-end">
            <el-button type="warning" :loading="loading" @click="handleCrossProcess">
              <el-icon>
                <Operation />
              </el-icon>
              交叉工序
            </el-button>
            <el-button type="info" :loading="loading" @click="handleStatistics">
              <el-icon>
                <TrendCharts />
              </el-icon>
              统计
            </el-button>
            <el-button type="primary" :loading="loading" @click="handleQuery5">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon>
                <Refresh />
              </el-icon>
              重置
            </el-button>
            <el-button type="success" :loading="exportLoading" @click="handleExport" :disabled="isExportDisabled">
              <el-icon>
                <Download />
              </el-icon>
              下载
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- MO列表区域 -->
    <el-card v-show="!showStatisTable" class="shadow-md">
      <el-table :data="paginatedMoList" border stripe @row-click="handleRowClick" @row-dblclick="viewMoProcess" class="cursor-pointer">
        <el-table-column prop="moNo" label="MO_NO" width="200" align="center">
          <template #default="{ row }">
            <span class="font-medium" :class="{ 'text-green-600': row.selected }">
              {{ row.moNo }}
            </span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="moList.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-card>

    <!-- 统计表格区域 -->
    <el-card v-show="showStatisTable" class="shadow-md mt-6">
      <el-table :data="paginatedStatisList" border stripe>
        <el-table-column prop="moState" label="状态" width="100" align="center">
          <template #default="{ row }">
            <span @dblclick="viewScanRecords(row)" class="cursor-pointer">
              {{ row.moState }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="bargainDate" label="接单日期" width="120" align="center" />
        <el-table-column prop="moRem" label="备注" min-width="150" />
        <el-table-column prop="custNo" label="客户代码" width="120" align="center" />
        <el-table-column prop="deliveryDate" label="客户要求交期" width="120" align="center" />
        <el-table-column prop="pmcReqDate" label="PMC要求交期" width="120" align="center" />
        <el-table-column prop="partType" label="零件分类" width="120" align="center" />
        <el-table-column prop="moNo" label="MO_NO" width="150" align="center">
          <template #default="{ row }">
            <span @dblclick="viewProcessRecords(row)" class="cursor-pointer">
              {{ row.moNo }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="soItm" label="行号" width="100" align="center" />
        <el-table-column prop="drawNumber" label="图号" width="120" align="center">
          <template #default="{ row }">
            <span @dblclick="handleViewPic(row)" class="cursor-pointer">
              {{ row.drawNumber }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="orderQty" label="订单数量" width="100" align="center" />
        <el-table-column prop="mfgDept" label="完成部门" width="100" align="center" />
        <el-table-column prop="workshop" label="生产车间" width="100" align="center" />
        <el-table-column prop="lastOpTime" label="末工序时间" width="120" align="center" />
        <el-table-column prop="merchandiser" label="跟单人" width="100" align="center" />
        <el-table-column prop="bomCharge" label="工艺编制人" width="100" align="center" />
      </el-table>

      <!-- 统计表格分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="statisCurrentPage"
          v-model:page-size="statisPageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="statisList.length"
          @size-change="handleStatisSizeChange"
          @current-change="handleStatisCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-card>

    <!-- 交叉工序明细对话框 -->
    <el-dialog v-model="crossProcessVisible" title="交叉工序明细" width="90%" append-to-body>
      <div class="mb-4">
        <div class="flex justify-between items-center">
          <div>
            <h4 class="text-lg font-semibold mb-2">MO号：{{ currentMoNo }}</h4>
            <p class="text-gray-600">共 {{ currentCrossProcessList.length }} 个交叉工序</p>
          </div>
          <el-button type="success" :loading="detailExportLoading" @click="handleExportDetail" :disabled="currentCrossProcessList.length === 0">
            <el-icon>
              <Download />
            </el-icon>
            导出明细
          </el-button>
        </div>
      </div>

      <el-table :data="currentCrossProcessList" border stripe>
        <el-table-column prop="gxItm" label="工序号" width="100" align="center" />
        <el-table-column prop="gxName" label="工序名称" width="200" />
        <el-table-column prop="gxArbpl" label="工作中心" width="150" align="center" />
        <el-table-column prop="gxRem" label="工序说明" min-width="200" />
        <el-table-column prop="gsTs" label="设置时间" width="100" align="center">
          <template #default="{ row }"> {{ row.gsTs }}小时</template>
        </el-table-column>
        <el-table-column prop="gsRun" label="运行时间" width="100" align="center">
          <template #default="{ row }"> {{ row.gsRun }}小时</template>
        </el-table-column>
        <el-table-column prop="gsSum" label="总工时" width="100" align="center">
          <template #default="{ row }"> {{ row.gsSum }}小时</template>
        </el-table-column>
        <el-table-column prop="qty" label="数量" width="80" align="center" />
        <el-table-column prop="bdate" label="开始日期" width="120" align="center" />
        <el-table-column prop="edate" label="结束日期" width="120" align="center" />
      </el-table>
    </el-dialog>

    <!-- 扫描记录对话框 -->
    <el-dialog v-model="scanRecordVisible" title="扫描记录" width="95%" append-to-body>
      <div class="mb-4 flex justify-between">
        <div></div>
        <el-button type="success" :loading="scanExportLoading" @click="handleExportScanRecords" :disabled="scanRecords.length === 0">
          <el-icon>
            <Download />
          </el-icon>
          导出扫描记录
        </el-button>
      </div>
      <el-table :data="scanRecords" border stripe max-height="600">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="pjNumber" label="PJ号" width="120" />
        <el-table-column prop="processNumber" label="工序号" width="100" />
        <el-table-column prop="processName" label="工序名称" width="150" />
        <el-table-column prop="planType" label="计划类型" width="100" />
        <el-table-column prop="identifier" label="标识" width="120" />
        <el-table-column prop="operator" label="操作员" width="100" />
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="machineTool" label="机床" width="120" />
        <el-table-column prop="action" label="动作" width="120" />
        <el-table-column prop="startTime" label="开始时间" width="150" />
        <el-table-column prop="endTime" label="结束时间" width="150" />
        <el-table-column prop="passQuantity" label="通过数量" width="100" />
        <el-table-column prop="scrapQuantity" label="报废数量" width="100" />
        <el-table-column prop="totalProgress" label="总进度" width="100" />
        <el-table-column prop="currentProgress" label="当前进度" width="100" />
        <el-table-column prop="processHours" label="工艺工时" width="100" />
        <el-table-column prop="actualTime" label="实际时间" width="100" />
        <el-table-column prop="remark" label="备注" width="150" />
        <el-table-column prop="methodDescription" label="作法描述" width="150" />
        <el-table-column prop="supplier" label="供应商" width="120" />
        <el-table-column prop="deliveryNoteNumber" label="送货单号" width="120" />
        <el-table-column prop="deliveryTime" label="送货时间" width="150" />
        <el-table-column prop="inspectionNoteNumber" label="送检单号" width="120" />
        <el-table-column prop="receiptVoucher" label="收货凭证" width="120" />
        <el-table-column prop="batNo" label="BAT_NO" width="120" />
        <el-table-column prop="orderCategory" label="订单类别" width="100" />
        <el-table-column prop="customerCode" label="客户代码" width="120" />
        <el-table-column prop="furnaceNumber" label="炉号" width="120" />
        <el-table-column prop="processingPlant" label="加工厂址" width="150" />
        <el-table-column prop="postingDate" label="过账日期" width="150" />
        <el-table-column prop="movementType" label="移动类型" width="100" />
        <el-table-column prop="company" label="公司" width="120" />
        <el-table-column prop="factory" label="工厂" width="120" />
        <el-table-column prop="materialNumber" label="物料编号" width="120" />
        <el-table-column prop="materialDescription" label="物料描述" width="150" />
        <el-table-column prop="storageLocation" label="库存地点" width="120" />
        <el-table-column prop="purchasePoNumber" label="采购PO号" width="120" />
        <el-table-column prop="lineItem" label="行项目" width="100" />
        <el-table-column prop="sameBatchNumber" label="同批批号" width="120" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="costCenter" label="成本中心" width="120" />
        <el-table-column prop="drawingNumber" label="图号" width="120" />
        <el-table-column prop="version" label="版本" width="80" />
        <el-table-column prop="excessQuantity" label="多余数量" width="100" />
        <el-table-column prop="industry" label="行业" width="100" />
        <el-table-column prop="fhid" label="FHID" width="120" />
        <el-table-column prop="deliveryNote" label="送货单" width="120" />
        <el-table-column prop="dnNumber" label="DN单号" width="120" />
        <el-table-column prop="dnLineNumber" label="DN行号" width="100" />
        <el-table-column prop="departmentCode" label="部门代号" width="100" />
        <el-table-column prop="department" label="部门" width="120" />
        <el-table-column prop="storagePosition" label="库位" width="120" />
        <el-table-column prop="materialRequester" label="领料人" width="100" />
        <el-table-column prop="materialRequesterName" label="领料姓名" width="100" />
        <el-table-column prop="factoryExitApplication" label="出厂申请" width="120" />
        <el-table-column prop="partClassification" label="零件分类" width="120" />
        <el-table-column prop="productNumber" label="品号" width="120" />
        <el-table-column prop="isCombinedOrder" label="是否合单" width="100" />
      </el-table>
    </el-dialog>

    <!-- 工艺记录对话框 -->
    <el-dialog v-model="processRecordVisible" title="工艺记录" width="95%" append-to-body>
      <el-table :data="processRecords" border stripe max-height="600">
        <el-table-column prop="processNumber" label="工序号" width="100" />
        <el-table-column prop="processName" label="工序名称" width="150" />
        <el-table-column prop="workCenter" label="工作中心" width="120" />
        <el-table-column prop="plannedStartTime" label="计划开始时间" width="150" />
        <el-table-column prop="plannedEndTime" label="计划结束时间" width="150" />
        <el-table-column prop="actualStartTime" label="实际开始时间" width="150" />
        <el-table-column prop="actualEndTime" label="实际结束时间" width="150" />
        <el-table-column prop="plannedQuantity" label="计划数量" width="100" />
        <el-table-column prop="completedQuantity" label="完成数量" width="100" />
        <el-table-column prop="qualifiedQuantity" label="合格数量" width="100" />
        <el-table-column prop="unqualifiedQuantity" label="不合格数量" width="120" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="resource" label="资源" width="120" />
        <el-table-column prop="status" label="状态" width="100" />
        <el-table-column prop="operator" label="操作员" width="100" />
        <el-table-column prop="remark" label="备注" min-width="200" />
      </el-table>
    </el-dialog>

    <!-- 工艺制程对话框 -->
    <el-dialog v-model="processVisible" title="工艺制程" width="90%" append-to-body>
      <el-steps :active="currentProcessStep" direction="vertical" class="mb-6">
        <el-step v-for="(step, index) in processSteps" :key="index" :title="step.name" :description="step.description" />
      </el-steps>

      <el-table :data="processDetails" border stripe>
        <el-table-column prop="stepNo" label="工序号" />
        <el-table-column prop="stepName" label="工序名称" />
        <el-table-column prop="workCenter" label="工作中心" />
        <el-table-column prop="estimatedTime" label="预计工时" />
        <el-table-column prop="status" label="状态" />
        <el-table-column prop="startDate" label="开始日期" />
        <el-table-column prop="endDate" label="完成日期" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import {
  ElButton,
  ElCard,
  ElDatePicker,
  ElDialog,
  ElForm,
  ElFormItem,
  ElIcon,
  ElMessage,
  ElPagination,
  ElStep,
  ElSteps,
  ElTable,
  ElTableColumn
} from 'element-plus';
import { Download, Operation, Refresh, Search, TrendCharts } from '@element-plus/icons-vue';
import { exportToExcel } from '@/utils/excel';
import { getPicUrl, getStatis5Data, getStatis5Data2, getStatis5Data3, getStatis5Data4, getStatis5Data5 } from '@/api/pmc/statis5';

// 获取默认日期范围（当天的前7天）
const getDefaultDateRange = () => {
  const today = new Date();
  const sevenDaysAgo = new Date(today);
  sevenDaysAgo.setDate(today.getDate() - 7);

  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  return [formatDate(sevenDaysAgo), formatDate(today)];
};

// 响应式表单数据
const searchForm = reactive({
  dateType: 'orderDate',
  dateRange: getDefaultDateRange()
});

// MO列表数据
const moList = ref([]);

// 统计表格数据
const statisList = ref([]);
const showStatisTable = ref(false);
const statisCurrentPage = ref(1);
const statisPageSize = ref(10);

// 计算分页后的统计数据
const paginatedStatisList = computed(() => {
  const start = (statisCurrentPage.value - 1) * statisPageSize.value;
  const end = start + statisPageSize.value;
  return statisList.value.slice(start, end);
});

// 分页相关变量
const currentPage = ref(1);
const pageSize = ref(10);

// 计算分页后的MO列表
const paginatedMoList = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return moList.value.slice(start, end);
});

// 加载状态
const loading = ref(false);
const exportLoading = ref(false);
const detailExportLoading = ref(false); // 新增交叉工序明细导出加载状态
const scanExportLoading = ref(false); // 新增扫描记录导出加载状态

// 导出上下文：'query' | 'statistics' | 'mo'
const exportContext = ref('mo');

// 对话框相关变量
const scanRecordVisible = ref(false);
const processVisible = ref(false);
const crossProcessVisible = ref(false); // 新增交叉工序明细对话框
const scanRecords = ref([]);
const processSteps = ref([]);
const processDetails = ref([]);
const currentProcessStep = ref(0);
const currentMoNo = ref(''); // 新增当前MO号
const currentCrossProcessList = ref([]); // 新增当前交叉工序列表
const processRecords = ref([]); // 新增工艺记录数据
const processRecordVisible = ref(false); // 新增工艺记录对话框显示控制

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    'completed': '已完成',
    'processing': '进行中',
    'pending': '待开始'
  };
  return statusMap[status] || status;
};

// 获取状态类型
const getStatusType = (status: string) => {
  if (status === 'completed') {
    return 'success';
  } else if (status === 'processing') {
    return 'info';
  } else if (status === 'pending') {
    return 'warning';
  }
  return 'info';
};

// 切换MO选择状态
const toggleMoSelection = (mo: any) => {
  mo.selected = !mo.selected;
};

// 处理行点击事件
const handleRowClick = (row: any) => {
  toggleMoSelection(row);
  if (row.crossProcessList && row.crossProcessList.length > 0) {
    currentMoNo.value = row.moNo;
    currentCrossProcessList.value = row.crossProcessList;
    crossProcessVisible.value = true;
  }
};

// 查看MO工艺制程
const viewMoProcess = async (mo: any) => {
  try {
    // 这里应该调用实际的API获取工艺制程数据
    // const res = await getMoProcessData(mo.moNo);
    // processSteps.value = res.data.steps || [];
    // processDetails.value = res.data.details || [];
    // currentProcessStep.value = res.data.currentStep || 0;

    // 临时显示空数据
    processSteps.value = [];
    processDetails.value = [];
    currentProcessStep.value = 0;

    processVisible.value = true;
  } catch (err: any) {
    ElMessage.error('获取工艺制程失败：' + err.message);
  }
};

// 查看扫描记录
const viewScanRecords = async (row: any) => {
  try {
    const res = await getStatis5Data3({ moNumber: row.moNo });
    scanRecords.value = res.data || [];
    scanRecordVisible.value = true;
  } catch (err: any) {
    ElMessage.error('获取扫描记录失败：' + err.message);
  }
};

// 查看工艺记录
const viewProcessRecords = async (row: any) => {
  try {
    const res = await getStatis5Data4({ moNumber: row.moNo });
    processRecords.value = res.data || [];
    processRecordVisible.value = true;
  } catch (err: any) {
    ElMessage.error('获取工艺记录失败：' + err.message);
  }
};

// 双击图号查看图纸
const handleViewPic = async (row: any) => {
  const moNo = row?.moNo || row?.moNumber;
  const drawNo = row?.drawNumber || row?.drawingNumber;
  if (!moNo || !drawNo) {
    ElMessage.warning('缺少MO号或图号');
    return;
  }

  await getPicUrl({ moNumber: moNo, drawNumber: drawNo })
    .then((res) => {
      if (res.data === 'DATA_NOT_FOUND') {
        window.open(`https://tuku.world-machining.com/sap_drawing/${moNo}.pdf`, '_blank');
      } else {
        window.open(res.data, '_blank');
      }
    })
    .catch((error) => {
      ElMessage.error(error);
    });
};

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
};

// 处理统计表格页码变化
const handleStatisCurrentChange = (val: number) => {
  statisCurrentPage.value = val;
};

// 处理统计表格每页条数变化
const handleStatisSizeChange = (val: number) => {
  statisPageSize.value = val;
  statisCurrentPage.value = 1;
};

// 查询方法（search5）
const handleQuery5 = async () => {
  loading.value = true;
  try {
    const res = await getStatis5Data5(searchForm);

    const list = Array.isArray(res?.data) ? res.data : [];

    statisList.value = list;
    showStatisTable.value = list.length > 0;

    statisCurrentPage.value = 1;

    exportContext.value = showStatisTable.value ? 'query' : 'mo';

    if (!showStatisTable.value) {
      ElMessage.info('没有匹配的数据');
    } else {
      ElMessage.success('查询成功');
    }

    moList.value = [];
  } catch (err: any) {
    ElMessage.error('查询失败：' + (err.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 重置方法
const handleReset = () => {
  searchForm.dateType = 'orderDate';
  searchForm.dateRange = getDefaultDateRange();
  currentPage.value = 1;
  moList.value.forEach((mo) => (mo.selected = false));
  showStatisTable.value = false;
  exportContext.value = 'mo';
};

// 导出方法
const handleExport = async () => {
  exportLoading.value = true;
  try {
    let exportData: any[] = [];
    let headers: Record<string, string> = {};
    let fileName = '';
    let sheetName = '';

    if (showStatisTable.value) {
      if (statisList.value.length === 0) {
        ElMessage.warning('没有数据可以导出');
        return;
      }

      exportData = statisList.value.map((item) => ({
        '状态': item.moState,
        '接单日期': item.bargainDate,
        '备注': item.moRem,
        '客户代码': item.custNo,
        '客户要求交期': item.deliveryDate,
        'PMC要求交期': item.pmcReqDate,
        '零件分类': item.partType,
        'MO_NO': item.moNo,
        '行号': item.soItm,
        '图号': item.drawNumber,
        '订单数量': item.orderQty,
        '完成部门': item.mfgDept,
        '生产车间': item.workshop,
        '末工序时间': item.lastOpTime,
        '跟单人': item.merchandiser,
        '工艺编制人': item.bomCharge
      }));

      headers = {
        '状态': '状态',
        '接单日期': '接单日期',
        '备注': '备注',
        '客户代码': '客户代码',
        '客户要求交期': '客户要求交期',
        'PMC要求交期': 'PMC要求交期',
        '零件分类': '零件分类',
        'MO_NO': 'MO_NO',
        '行号': '行号',
        '图号': '图号',
        '订单数量': '订单数量',
        '完成部门': '完成部门',
        '生产车间': '生产车间',
        '末工序时间': '末工序时间',
        '跟单人': '跟单人',
        '工艺编制人': '工艺编制人'
      };

      const ts = new Date().getTime();
      if (exportContext.value === 'statistics') {
        fileName = `统计结果_${ts}`;
        sheetName = '统计结果';
      } else if (exportContext.value === 'query') {
        fileName = `查询结果_${ts}`;
        sheetName = '查询结果';
      } else {
        fileName = `统计结果_${ts}`;
        sheetName = '统计结果';
      }
    } else {
      if (moList.value.length === 0) {
        ElMessage.warning('没有数据可以导出');
        return;
      }

      exportData = moList.value.map((mo) => ({
        'MO号': mo.moNo
      }));

      headers = {
        'MO号': 'MO号'
      };

      const ts = new Date().getTime();
      fileName = `交叉工序MO查询结果_${ts}`;
      sheetName = '交叉工序MO查询结果';
    }

    await exportToExcel(exportData, headers, fileName, sheetName, '409EFF');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    exportLoading.value = false;
  }
};

// 交叉工序方法
const handleCrossProcess = async () => {
  loading.value = true;
  try {
    const res = await getStatis5Data(searchForm);

    moList.value = (res?.data || []).map((item: any) => ({
      ...item,
      selected: false,
      crossProcessList: item.crossProcessList || []
    }));

    // 切换到MO列表视图
    showStatisTable.value = false;
    currentPage.value = 1;
    exportContext.value = 'mo';

    if (moList.value.length === 0) {
      ElMessage.info('没有匹配的交叉工序数据');
    } else {
      ElMessage.success('已加载交叉工序MO列表');
    }
  } catch (err: any) {
    ElMessage.error('交叉工序查询失败：' + (err.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

const handleCrossProcessOld = () => {
  const selectedMOs = moList.value.filter((mo) => mo.selected);
  if (selectedMOs.length === 0) {
    ElMessage.warning('请先选择要处理的MO');
    return;
  }
  if (selectedMOs.length > 1) {
    ElMessage.warning('请选择一个MO进行交叉工序处理');
    return;
  }
  const selectedMo = selectedMOs[0];
  if (!selectedMo.crossProcessList || selectedMo.crossProcessList.length === 0) {
    ElMessage.info('该MO暂无交叉工序数据');
    return;
  }
  currentMoNo.value = selectedMo.moNo;
  currentCrossProcessList.value = selectedMo.crossProcessList;
  crossProcessVisible.value = true;
};

// 交叉工序明细导出方法
const handleExportDetail = async () => {
  if (currentCrossProcessList.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  detailExportLoading.value = true;
  try {
    const exportData = currentCrossProcessList.value.map((item) => ({
      '工序号': item.gxItm,
      '工序名称': item.gxName,
      '工作中心': item.gxArbpl,
      '工序说明': item.gxRem,
      '设置时间': item.gsTs,
      '运行时间': item.gsRun,
      '总工时': item.gsSum,
      '数量': item.qty,
      '开始日期': item.bdate,
      '结束日期': item.edate
    }));

    const headers = {
      '工序号': '工序号',
      '工序名称': '工序名称',
      '工作中心': '工作中心',
      '工序说明': '工序说明',
      '设置时间': '设置时间',
      '运行时间': '运行时间',
      '总工时': '总工时',
      '数量': '数量',
      '开始日期': '开始日期',
      '结束日期': '结束日期'
    };

    const fileName = `交叉工序明细_${currentMoNo.value}_${new Date().getTime()}`;
    await exportToExcel(exportData, headers, fileName, '交叉工序明细', '409EFF');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    detailExportLoading.value = false;
  }
};

// 扫描记录导出方法
const handleExportScanRecords = async () => {
  if (scanRecords.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  scanExportLoading.value = true;
  try {
    const exportData = scanRecords.value.map((item) => ({
      'ID': item.id,
      'PJ号': item.pjNumber,
      '工序号': item.processNumber,
      '工序名称': item.processName,
      '计划类型': item.planType,
      '标识': item.identifier,
      '操作员': item.operator,
      '姓名': item.name,
      '机床': item.machineTool,
      '动作': item.action,
      '开始时间': item.startTime,
      '结束时间': item.endTime,
      '通过数量': item.passQuantity,
      '报废数量': item.scrapQuantity,
      '总进度': item.totalProgress,
      '当前进度': item.currentProgress,
      '工艺工时': item.processHours,
      '实际时间': item.actualTime,
      '备注': item.remark,
      '作法描述': item.methodDescription,
      '供应商': item.supplier,
      '送货单号': item.deliveryNoteNumber,
      '送货时间': item.deliveryTime,
      '送检单号': item.inspectionNoteNumber,
      '收货凭证': item.receiptVoucher,
      'BAT_NO': item.batNo,
      '订单类别': item.orderCategory,
      '客户代码': item.customerCode,
      '炉号': item.furnaceNumber,
      '加工厂址': item.processingPlant,
      '过账日期': item.postingDate,
      '移动类型': item.movementType,
      '公司': item.company,
      '工厂': item.factory,
      '物料编号': item.materialNumber,
      '物料描述': item.materialDescription,
      '库存地点': item.storageLocation,
      '采购PO号': item.purchasePoNumber,
      '行项目': item.lineItem,
      '同批批号': item.sameBatchNumber,
      '单位': item.unit,
      '成本中心': item.costCenter,
      '图号': item.drawingNumber,
      '版本': item.version,
      '多余数量': item.excessQuantity,
      '行业': item.industry,
      'FHID': item.fhid,
      '送货单': item.deliveryNote,
      'DN单号': item.dnNumber,
      'DN行号': item.dnLineNumber,
      '部门代号': item.departmentCode,
      '部门': item.department,
      '库位': item.storagePosition,
      '领料人': item.materialRequester,
      '领料姓名': item.materialRequesterName,
      '出厂申请': item.factoryExitApplication,
      '零件分类': item.partClassification,
      '品号': item.productNumber,
      '是否合单': item.isCombinedOrder
    }));

    const headers = {
      'ID': 'ID',
      'PJ号': 'PJ号',
      '工序号': '工序号',
      '工序名称': '工序名称',
      '计划类型': '计划类型',
      '标识': '标识',
      '操作员': '操作员',
      '姓名': '姓名',
      '机床': '机床',
      '动作': '动作',
      '开始时间': '开始时间',
      '结束时间': '结束时间',
      '通过数量': '通过数量',
      '报废数量': '报废数量',
      '总进度': '总进度',
      '当前进度': '当前进度',
      '工艺工时': '工艺工时',
      '实际时间': '实际时间',
      '备注': '备注',
      '作法描述': '作法描述',
      '供应商': '供应商',
      '送货单号': '送货单号',
      '送货时间': '送货时间',
      '送检单号': '送检单号',
      '收货凭证': '收货凭证',
      'BAT_NO': 'BAT_NO',
      '订单类别': '订单类别',
      '客户代码': '客户代码',
      '炉号': '炉号',
      '加工厂址': '加工厂址',
      '过账日期': '过账日期',
      '移动类型': '移动类型',
      '公司': '公司',
      '工厂': '工厂',
      '物料编号': '物料编号',
      '物料描述': '物料描述',
      '库存地点': '库存地点',
      '采购PO号': '采购PO号',
      '行项目': '行项目',
      '同批批号': '同批批号',
      '单位': '单位',
      '成本中心': '成本中心',
      '图号': '图号',
      '版本': '版本',
      '多余数量': '多余数量',
      '行业': '行业',
      'FHID': 'FHID',
      '送货单': '送货单',
      'DN单号': 'DN单号',
      'DN行号': 'DN行号',
      '部门代号': '部门代号',
      '部门': '部门',
      '库位': '库位',
      '领料人': '领料人',
      '领料姓名': '领料姓名',
      '出厂申请': '出厂申请',
      '零件分类': '零件分类',
      '品号': '品号',
      '是否合单': '是否合单'
    };

    const fileName = `扫描记录_${new Date().getTime()}`;
    await exportToExcel(exportData, headers, fileName, '扫描记录', '409EFF');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    scanExportLoading.value = false;
  }
};

// 统计方法
const handleStatistics = async () => {
  loading.value = true;
  try {
    const res = await getStatis5Data2(searchForm);

    if (!res || !res.data || !Array.isArray(res.data)) {
      ElMessage.warning('返回数据格式不正确');
      statisList.value = [];
      showStatisTable.value = false;
      exportContext.value = 'mo';
      return;
    }

    statisList.value = res.data;
    showStatisTable.value = res.data.length > 0;
    exportContext.value = showStatisTable.value ? 'statistics' : 'mo';

    if (!showStatisTable.value) {
      ElMessage.info('没有匹配的统计数据');
    }
  } catch (err: any) {
    if (err.name === 'AxiosError') {
      ElMessage.error('网络错误，请检查连接后重试');
    } else {
      ElMessage.error('统计失败：' + (err.message || '未知错误'));
    }
  } finally {
    loading.value = false;
  }
};

// 计算导出按钮是否禁用
const isExportDisabled = computed(() => {
  if (showStatisTable.value) {
    return statisList.value.length === 0;
  }
  return moList.value.length === 0;
});

onMounted(() => {
  // 初始化数据
});
</script>

<style scoped>
/* 表格行选中样式 */
.el-table__row:hover {
  background-color: #f5f7fa !important;
}

.el-table__row.current-row {
  background-color: #e6f7ff !important;
}
</style>
