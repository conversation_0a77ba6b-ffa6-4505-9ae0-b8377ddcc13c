<template>
  <div class="p-4">
    <!-- 搜索卡片 -->
    <el-card class="mb-6 shadow-md">
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">条件搜索</h3>
          <span style="color: #0a11b4">物料类型Z002，只统计当天</span>
        </div>
      </template>

      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="统计日期">
              <el-input
                v-model="displayDate"
                readonly
                disabled
                style="width: 240px"
                placeholder="统计日期"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="项目号">
              <el-input v-model="searchForm.projectNo" clearable/>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="DR单号">
              <el-input
                v-model="searchForm.drNumber"
                type="textarea"
                :rows="2"
                placeholder="可输入多个DR单号，用逗号分隔"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24" class="flex justify-end">
            <el-button type="primary" :loading="loading" @click="handleQuery">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon>
                <Refresh />
              </el-icon>
              重置
            </el-button>
            <el-button type="success" :loading="exportLoading" @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>
              导出Excel
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-table :data="paginatedData" :loading="loading" border stripe>
      <el-table-column prop="id" label="ID" />
      <el-table-column prop="date" label="日期" />
      <el-table-column prop="unreceivedTotalItems" label="未收货总项数">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'unreceivedTotalItemDetails')">{{ row.unreceivedTotalItems }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="dailyPrCreatedItems" label="单日PR创建项数">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'dailyPrCreatedItemDetails')">{{ row.dailyPrCreatedItems }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="unconvertedPoItems" label="未转PO项数">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'unconvertedPoItemDetails')">{{ row.unconvertedPoItems }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="poIssuedUnpromisedDeliveryItems" label="PO下达未承诺交期总项数">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'poIssuedUnpromisedDeliveryItemDetails')">{{ row.poIssuedUnpromisedDeliveryItems }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="prIssued3DaysUnpromisedItems" label="PR下达3天后未承诺交期项数">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'prIssued3DaysUnpromisedItemDetails')">{{ row.prIssued3DaysUnpromisedItems }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="exceedPmcRequiredDeliveryItems" label="超PMC要求交期项数">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'exceedPmcRequiredDeliveryItemDetails')">{{ row.exceedPmcRequiredDeliveryItems }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="purchaseReplyDeliveryDueItems" label="按采购回复交期应交项数">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'purchaseReplyDeliveryDueItemDetails')">{{ row.purchaseReplyDeliveryDueItems }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="purchaseReplyDeliveryDelayedItems" label="按采购回复交期延误项数">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'purchaseReplyDeliveryDelayedItemDetails')">{{ row.purchaseReplyDeliveryDelayedItems }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="purchaseReplyDeliveryAchievementRate" label="采购回复交期达成率" />
      <el-table-column prop="pmcRequiredDeliveryDueItems" label="按PMC要求交期应交项数">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'pmcRequiredDeliveryDueItemDetails')">{{ row.pmcRequiredDeliveryDueItems }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="pmcRequiredDeliveryDelayedItems" label="按PMC要求交期延误项数">
        <template #default="{ row }">
          <span class="cursor-pointer text-blue-500 hover:text-blue-700" @click="showDetail(row, 'pmcRequiredDeliveryDelayedItemDetails')">{{ row.pmcRequiredDeliveryDelayedItems }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="pmcRequiredDeliveryAchievementRate" label="PMC要求交期达成率" />
    </el-table>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailVisible" title="详细信息" width="80%" append-to-body>
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-semibold">详细信息 (共{{ detailData.length }}条记录)</span>
          <el-button type="primary" :loading="detailExportLoading" @click="handleDetailExport">
            <el-icon><Download /></el-icon>
            下载Excel
          </el-button>
        </div>
      </template>

      <el-table :data="paginatedDetailData" border stripe>
        <el-table-column prop="moNo" label="MO_NO" />
        <el-table-column prop="materialRequestStatus" label="申购物料状态" />
        <el-table-column prop="materialCode" label="物料编号" />
        <el-table-column prop="materialName" label="物料名称" />
        <el-table-column prop="shouldIssueQuantity" label="应发数量" />
        <el-table-column prop="stockQuantity" label="库存数量" />
        <el-table-column prop="requestQuantity" label="请购数量" />
        <el-table-column prop="pmcRequiredDate" label="PMC要求交期" />
        <el-table-column prop="purchaseOrderNo" label="采购单号" />
        <el-table-column prop="purchaseOrderLineNo" label="采购单行号" />
        <el-table-column prop="purchaseReplyDate" label="采购回复交期" />
        <el-table-column prop="poProjectText" label="PO项目文本" />
        <el-table-column prop="delayDays" label="延误天数" />
        <el-table-column prop="purchaseRequestApprovalDate" label="采购申请审批日期" />
        <el-table-column prop="purchaseRequestNo" label="采购申请号" />
        <el-table-column prop="purchaseRequestLineNo" label="采购申请行号" />
        <el-table-column prop="purchaseOrderCreateDate" label="采购订单创建日期" />
        <el-table-column prop="deliveredQuantity" label="已交数量" />
        <el-table-column prop="undeliveredQuantity" label="未交数量" />
        <el-table-column prop="firstDeliveryDate" label="第一次交货日期" />
        <el-table-column prop="purchaseCycle" label="采购周期" />
        <el-table-column prop="supplier" label="供应商" />
        <el-table-column prop="bomEngineer" label="BOM工程师" />
        <el-table-column prop="brand" label="品牌" />
        <el-table-column prop="projectNo" label="项目号" />
        <el-table-column prop="drNo" label="DR单号" />
        <el-table-column prop="drLineNo" label="DR行号" />
        <el-table-column prop="customerCode" label="客户代码" />
        <el-table-column prop="moStatus" label="MO状态" />
        <el-table-column prop="purchaseOrderCreator" label="采购订单创建人" />
        <el-table-column prop="purchaser" label="采购员" />
        <el-table-column prop="pmcFollower" label="PMC跟单人" />
        <el-table-column prop="inDate" label="in_date" />
        <el-table-column prop="sysDate" label="sysdt" />
        <el-table-column prop="host" label="host" />
        <el-table-column prop="yw2" label="yw2" />
        <el-table-column prop="pm" label="PM" />
        <el-table-column prop="orderClerk" label="下单员" />
        <el-table-column prop="profitCenter" label="利润中心" />
      </el-table>

      <!-- 详情对话框分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="detailCurrentPage"
          v-model:page-size="detailPageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="detailData.length"
          @size-change="handleDetailSizeChange"
          @current-change="handleDetailCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-dialog>

    <!-- 分页 -->
    <div class="mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        :total="totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="shadow-sm"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import {
  ElButton,
  ElCard,
  ElDialog,
  ElForm,
  ElFormItem,
  ElIcon,
  ElInput,
  ElMessage,
  ElPagination,
  ElTable,
  ElTableColumn
} from 'element-plus';
import { Download, Refresh, Search } from '@element-plus/icons-vue';
import { getStatis3Data } from '@/api/pmc/statis3';
import { exportToExcel } from '@/utils/excel';

// 获取昨天的日期
const getYesterday = () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return yesterday.toISOString().split('T')[0];
};

// 显示的日期（昨天）
const displayDate = ref(getYesterday());

// 响应式表单数据
const searchForm = reactive({
  inDate: getYesterday(), // 对应明细表in_date字段
  projectNo: '',
  drNumber: '' // 改为支持多个DR单号
});

// 表格数据
const tableData = ref<any[]>([]);

// 分页相关变量
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

// 详情对话框分页相关变量
const detailCurrentPage = ref(1);
const detailPageSize = ref(10);

// 计算分页后的数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return tableData.value.slice(start, end);
});

// 计算详情对话框分页后的数据
const paginatedDetailData = computed(() => {
  const start = (detailCurrentPage.value - 1) * detailPageSize.value;
  const end = start + detailPageSize.value;
  return detailData.value.slice(start, end);
});

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
};

// 处理详情对话框页码变化
const handleDetailCurrentChange = (val: number) => {
  detailCurrentPage.value = val;
};

// 处理详情对话框每页条数变化
const handleDetailSizeChange = (val: number) => {
  detailPageSize.value = val;
  detailCurrentPage.value = 1;
};

// 显示详情
const showDetail = (row: any, detailType: string) => {
  // 根据点击的列显示对应的详情数据
  detailData.value = row[detailType] || [];
  currentDetailType.value = detailType;
  // 重置详情对话框分页状态
  detailCurrentPage.value = 1;
  detailPageSize.value = 10;
  detailVisible.value = true;
};

const loading = ref(false);
const exportLoading = ref(false);

// 详情对话框相关
const detailVisible = ref(false);
const detailExportLoading = ref(false);
const detailData = ref([]);
const currentDetailType = ref(''); // 记录当前详情类型，用于生成文件名

// 查询方法
const handleQuery = async () => {
  loading.value = true;
  try {
    const res = await getStatis3Data(searchForm);
    tableData.value = res.data;
    totalItems.value = res.data.length;
    currentPage.value = 1; // 重置到第一页
  } catch (err: any) {
    ElMessage.error(err.message || '查询失败');
  } finally {
    loading.value = false;
  }
};

// 重置方法
const handleReset = () => {
  searchForm.projectNo = '';
  searchForm.drNumber = '';
  // 统计日期保持为昨天，不重置
  currentPage.value = 1; // 重置到第一页
  tableData.value = [];
  totalItems.value = 0;
};

// 导出方法
const handleExport = async () => {
  if (!tableData.value || tableData.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  exportLoading.value = true;
  try {
    // 定义主表格的表头映射
    const mainHeaders = {
      id: 'ID',
      date: '日期',
      unreceivedTotalItems: '未收货总项数',
      dailyPrCreatedItems: '单日PR创建项数',
      unconvertedPoItems: '未转PO项数',
      poIssuedUnpromisedDeliveryItems: 'PO下达未承诺交期总项数',
      prIssued3DaysUnpromisedItems: 'PR下达3天后未承诺交期项数',
      exceedPmcRequiredDeliveryItems: '超PMC要求交期项数',
      purchaseReplyDeliveryDueItems: '按采购回复交期应交项数',
      purchaseReplyDeliveryDelayedItems: '按采购回复交期延误项数',
      purchaseReplyDeliveryAchievementRate: '采购回复交期达成率',
      pmcRequiredDeliveryDueItems: '按PMC要求交期应交项数',
      pmcRequiredDeliveryDelayedItems: '按PMC要求交期延误项数',
      pmcRequiredDeliveryAchievementRate: 'PMC要求交期达成率'
    };

    const fileName = `PMC统计报表_${new Date().getTime()}`;

    // 使用绿色表头（与原导出按钮颜色保持一致）
    await exportToExcel(tableData.value, mainHeaders, fileName, '标准件采购完成状况统计表', '67C23A');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    exportLoading.value = false;
  }
};

// 详情导出方法
const handleDetailExport = async () => {
  if (!detailData.value || detailData.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  detailExportLoading.value = true;
  try {
    // 定义表头映射
    const headers = {
      moNo: 'MO_NO',
      materialRequestStatus: '申购物料状态',
      materialCode: '物料编号',
      materialName: '物料名称',
      shouldIssueQuantity: '应发数量',
      stockQuantity: '库存数量',
      requestQuantity: '请购数量',
      pmcRequiredDate: 'PMC要求交期',
      purchaseOrderNo: '采购单号',
      purchaseOrderLineNo: '采购单行号',
      purchaseReplyDate: '采购回复交期',
      poProjectText: 'PO项目文本',
      delayDays: '延误天数',
      purchaseRequestApprovalDate: '采购申请审批日期',
      purchaseRequestNo: '采购申请号',
      purchaseRequestLineNo: '采购申请行号',
      purchaseOrderCreateDate: '采购订单创建日期',
      deliveredQuantity: '已交数量',
      undeliveredQuantity: '未交数量',
      firstDeliveryDate: '第一次交货日期',
      purchaseCycle: '采购周期',
      supplier: '供应商',
      bomEngineer: 'BOM工程师',
      brand: '品牌',
      projectNo: '项目号',
      drNo: 'DR单号',
      drLineNo: 'DR行号',
      customerCode: '客户代码',
      moStatus: 'MO状态',
      purchaseOrderCreator: '采购订单创建人',
      purchaser: '采购员',
      pmcFollower: 'PMC跟单人',
      inDate: 'in_date',
      sysDate: 'sysdt',
      host: 'host',
      yw2: 'yw2',
      pm: 'PM',
      orderClerk: '下单员',
      profitCenter: '利润中心'
    };

    // 生成文件名
    const typeNameMap: Record<string, string> = {
      unreceivedTotalItemDetails: '未收货总项数',
      dailyPrCreatedItemDetails: '单日PR创建项数',
      unconvertedPoItemDetails: '未转PO项数',
      poIssuedUnpromisedDeliveryItemDetails: 'PO下达未承诺交期总项数',
      prIssued3DaysUnpromisedItemDetails: 'PR下达3天后未承诺交期项数',
      exceedPmcRequiredDeliveryItemDetails: '超PMC要求交期项数',
      purchaseReplyDeliveryDueItemDetails: '按采购回复交期应交项数',
      purchaseReplyDeliveryDelayedItemDetails: '按采购回复交期延误项数',
      pmcRequiredDeliveryDueItemDetails: '按PMC要求交期应交项数',
      pmcRequiredDeliveryDelayedItemDetails: '按PMC要求交期延误项数'
    };

    const typeName = typeNameMap[currentDetailType.value] || '详情数据';
    const fileName = `标准件采购完成状况统计表_${typeName}_${new Date().getTime()}`;

    // 使用蓝色表头
    await exportToExcel(detailData.value, headers, fileName, '详情数据', '4472C4');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    detailExportLoading.value = false;
  }
};

onMounted(() => {
  // 页面初始化
});
</script>

<style scoped>
.el-textarea {
  width: 100%;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
