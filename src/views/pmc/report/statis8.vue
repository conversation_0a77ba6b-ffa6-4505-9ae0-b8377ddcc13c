<template>
  <div class="p-4">
    <!-- 搜索卡片 -->
    <el-card class="mb-6 shadow-md">
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-red-500">【自动化】外发全工序、多工序交货准时率--【按日统计】</h3>
        </div>
      </template>

      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-row :gutter="24" class="items-center mb-6">
          <el-col :span="8">
            <el-form-item label="要求交期" class="text-red-500">
              <el-date-picker
                v-model="searchForm.deliveryDateRange"
                type="daterange"
                range-separator="--"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 260px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16" class="flex justify-end gap-3">
            <el-button type="primary" :loading="loading" @click="handleQuery" size="default">
              <el-icon>
                <Search />
              </el-icon>
              查询
            </el-button>
            <el-button @click="handleReset" size="default">
              <el-icon>
                <Refresh />
              </el-icon>
              重置
            </el-button>
            <el-button type="success" :loading="exportLoading" @click="handleExport" :disabled="isExportDisabled" size="default">
              <el-icon>
                <Download />
              </el-icon>
              导出
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 统计表格区域 -->
    <el-card class="shadow-md">
      <el-table :data="statisData" border stripe>
        <el-table-column prop="id" label="ID" width="60" align="center" />
        <el-table-column prop="pmcDeliveryDate" label="PMC要求交期" width="150" align="center">
          <template #default="{ row }">
            <span>
              {{ row.pmcDeliveryDate }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="expectedDeliveryCount" label="应交货数数" width="120" align="center">
          <template #default="{ row }">
            <span>
              {{ row.expectedDeliveryCount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="actualDeliveryCount" label="已交货数数" width="120" align="center">
          <template #default="{ row }">
            <span>
              {{ row.actualDeliveryCount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="onTimeDeliveryCount" label="准时交货数数" width="130" align="center">
          <template #default="{ row }">
            <span>
              {{ row.onTimeDeliveryCount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="delayCount" label="延误数数" width="100" align="center">
          <template #default="{ row }">
            <span>
              {{ row.delayCount }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="achievementRate" label="达成率" width="100" align="center">
          <template #default="{ row }">
            <span>
              {{ row.achievementRate }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, computed } from 'vue';
import {
  ElButton,
  ElCard,
  ElCheckbox,
  ElDatePicker,
  ElForm,
  ElFormItem,
  ElIcon,
  ElMessage,
  ElTable,
  ElTableColumn
} from 'element-plus';
import { Refresh, Search, Download } from '@element-plus/icons-vue';
import { getStatis8Data } from '@/api/pmc/statis8';
import { exportToExcel } from '@/utils/excel';

// 响应式表单数据
const searchForm = reactive({
  deliveryDateRange: null as [string, string] | null
});

// 统计数据
const statisData = ref([]);

// 加载状态
const loading = ref(false);
const exportLoading = ref(false);

// 计算导出按钮是否禁用
const isExportDisabled = computed(() => {
  return statisData.value.length === 0;
});

// 查询方法
const handleQuery = async () => {
  loading.value = true;
  try {
    const res = await getStatis8Data(searchForm);
    if (res.data) {
      statisData.value = res.data;
    }

    ElMessage.success('查询成功');
  } catch (err: any) {
    ElMessage.error('查询失败：' + (err.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 重置方法
const handleReset = () => {
  searchForm.deliveryDateRange = null;
};

// 导出方法
const handleExport = async () => {
  if (statisData.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  exportLoading.value = true;
  try {
    const exportData = statisData.value.map((item) => ({
      'ID': item.id,
      'PMC要求交期': item.pmcDeliveryDate,
      '应交货数数': item.expectedDeliveryCount,
      '已交货数数': item.actualDeliveryCount,
      '准时交货数数': item.onTimeDeliveryCount,
      '延误数数': item.delayCount,
      '达成率': item.achievementRate
    }));

    const headers = {
      'ID': 'ID',
      'PMC要求交期': 'PMC要求交期',
      '应交货数数': '应交货数数',
      '已交货数数': '已交货数数',
      '准时交货数数': '准时交货数数',
      '延误数数': '延误数数',
      '达成率': '达成率'
    };

    const fileName = `外发全工序多工序交货准时率统计_${new Date().getTime()}`;
    await exportToExcel(exportData, headers, fileName, '统计信息', '409EFF');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    exportLoading.value = false;
  }
};

onMounted(() => {
});
</script>

<style scoped>
/* 表格行选中样式 */
.el-table__row:hover {
  background-color: #f5f7fa !important;
}

.el-table__row.current-row {
  background-color: #e6f7ff !important;
}

/* 红色标题样式 */
.text-red-500 {
  color: #ef4444;
}

/* 蓝色背景样式 */
.bg-blue-600 {
  background-color: #2563eb;
}

/* 绿色背景样式 */
.bg-green-600 {
  background-color: #16a34a;
}
</style>
