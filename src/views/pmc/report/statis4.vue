<template>
  <div class="p-4">
    <!-- 搜索卡片 -->
    <el-card class="mb-6 shadow-md">
      <template #header>
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">条件搜索</h3>
          <span style="color: #0a11b4">精密零部件外发一次性合格率统计，指外发全工序(含多工序)交货后未产生退货的；电镀后退货不计入此范围</span>
        </div>
      </template>

      <el-form :model="searchForm" label-width="100px" :inline="true">
        <el-row :gutter="20" class="items-center">
          <el-col :span="8">
            <el-form-item label="统计时间">
              <el-date-picker
                v-model="searchForm.searchDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 240px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="16" class="flex justify-end">
            <el-button type="primary" :loading="loading" @click="handleQuery">
              <el-icon>
                <Search />
              </el-icon>
              统计
            </el-button>
            <el-button @click="handleReset">
              <el-icon>
                <Refresh />
              </el-icon>
              重置
            </el-button>
            <el-button type="success" :loading="exportLoading" @click="handleExport">
              <el-icon>
                <Download />
              </el-icon>
              导出Excel
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 数据表格 - 重新设计为行标题结构 -->
    <el-table :data="transformedTableData" :loading="loading" border stripe>
      <el-table-column prop="metricName" label="统计指标" width="200" fixed="left" align="center" />
      <el-table-column 
        v-for="week in weekColumns" 
        :key="week" 
        :prop="week" 
        :label="week" 
        width="120" 
        align="center"
      >
        <template #default="{ row }">
          <span 
            v-if="row[week] && row[week].clickable" 
            class="cursor-pointer text-blue-500 hover:text-blue-700" 
            @click="showDetail(row, week)"
          >
            {{ row[week].value }}
          </span>
          <span v-else>
            {{ row[week]?.value !== undefined ? row[week].value : '-' }}
          </span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailVisible" title="详细信息" width="80%" append-to-body>
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-semibold">详细信息 (共{{ detailData.length }}条记录)</span>
          <el-button type="primary" :loading="detailExportLoading" @click="handleDetailExport">
            <el-icon>
              <Download />
            </el-icon>
            下载Excel
          </el-button>
        </div>
      </template>

      <el-table :data="paginatedDetailData" border stripe>
        <el-table-column prop="pjNo" label="PJ号" />
        <el-table-column prop="workSequence" label="工序号" />
        <el-table-column prop="workSequenceName" label="工序名称" />
        <el-table-column prop="scanDate" label="扫描日期" />
        <el-table-column prop="supplier" label="供应商" />
        <el-table-column prop="isRTV" label="是否退货" />
        <el-table-column prop="week" label="周次" />
        <el-table-column prop="automation" label="自动化" />
        <el-table-column prop="precision" label="精密" />
        <el-table-column prop="isSpecialProcess" label="是否全工序" />
        <el-table-column prop="action" label="动作" />
      </el-table>

      <!-- 详情对话框分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="detailCurrentPage"
          v-model:page-size="detailPageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          :total="detailData.length"
          @size-change="handleDetailSizeChange"
          @current-change="handleDetailCurrentChange"
          class="shadow-sm"
        />
      </div>
    </el-dialog>

    <!-- 分页 -->
    <div class="mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next"
        :total="totalItems"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="shadow-sm"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue';
import { ElButton, ElCard, ElDatePicker, ElDialog, ElForm, ElFormItem, ElIcon, ElMessage, ElPagination, ElTable, ElTableColumn } from 'element-plus';
import { Download, Refresh, Search } from '@element-plus/icons-vue';
import { exportToExcel } from '@/utils/excel';
import { getStatis4Data } from '@/api/pmc/statis4';

// 获取默认日期范围（当天的前7天）
const getDefaultDateRange = () => {
  const today = new Date();

  // 计算7天前的日期
  const sevenDaysAgo = new Date(today);
  sevenDaysAgo.setDate(today.getDate() - 7);

  // 格式化为 YYYY-MM-DD
  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  return [formatDate(sevenDaysAgo), formatDate(today)];
};

// 响应式表单数据
const searchForm = reactive({
  searchDate: getDefaultDateRange()
});

// 原始表格数据
const originalTableData = ref([]);

// 转换后的表格数据 - 将列标题转换为行标题
const transformedTableData = computed(() => {
  if (!originalTableData.value || originalTableData.value.length === 0) {
    return [];
  }

  // 定义指标名称映射
  const metricNames = {
    deliveryPrecision: '交货(精密)',
    deliveryAutomation: '交货(自动化)',
    qualifiedBatches: '总送检批次',
    rtvPrecision: 'RTV(精密)',
    rtvAutomation: 'RTV(自动化)',
    rtvTotal: 'RTV合计',
    larTarget: 'LAR目标',
    larPrecision: 'LAR精密',
    larAutomation: 'LAR自动化',
    larComprehensive: 'LAR综合',
    specialProcess: '特殊工序',
    inspectionBatches: '送检批次',
    pass: 'Pass',
    rtv: 'RTV',
    larTarget2: 'LAR目标',
    larActual: 'LAR实际'
  };

  // 定义可点击的指标
  const clickableMetrics = [
    'deliveryPrecision', 'deliveryAutomation', 'qualifiedBatches',
    'rtvPrecision', 'rtvAutomation', 'rtvTotal',
    'inspectionBatches', 'pass', 'rtv'
  ];

  // 获取所有周次
  const weeks = originalTableData.value.map(item => item.week);

  // 转换数据结构
  const transformed = Object.entries(metricNames).map(([key, name]) => {
    const row: any = {
      metricName: name,
      metricKey: key
    };

    // 为每个周次添加数据
    weeks.forEach(week => {
      const weekData = originalTableData.value.find(item => item.week === week);
      if (weekData) {
        const value = weekData[key];
        const isClickable = clickableMetrics.includes(key);
        
        // 特殊处理LAR目标和特殊工序，显示为空
        if (key === 'larTarget' || key === 'larTarget2' || key === 'specialProcess') {
          row[week] = {
            value: '',
            clickable: false
          };
        } else if (isClickable) {
          row[week] = {
            value: value,
            clickable: true,
            details: weekData[`${key}Details`] || []
          };
        } else {
          row[week] = {
            value: value || '',
            clickable: false
          };
        }
      } else {
        // 特殊处理LAR目标和特殊工序，即使没有周次数据也显示空行
        if (key === 'larTarget' || key === 'larTarget2' || key === 'specialProcess') {
          row[week] = { value: '', clickable: false };
        } else {
          row[week] = { value: '-', clickable: false };
        }
      }
    });

    return row;
  });

  return transformed;
});

// 周次列
const weekColumns = computed(() => {
  return originalTableData.value.map(item => item.week);
});

// 分页相关变量
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

const loading = ref(false);
const exportLoading = ref(false);

// 详情对话框相关变量
const detailVisible = ref(false);
const detailExportLoading = ref(false);
const detailData = ref([]);
const currentDetailType = ref(''); // 记录当前详情类型，用于生成文件名

// 详情对话框分页相关变量
const detailCurrentPage = ref(1);
const detailPageSize = ref(10);

// 处理页码变化
const handleCurrentChange = (val: number) => {
  currentPage.value = val;
};

// 计算详情对话框分页后的数据
const paginatedDetailData = computed(() => {
  const start = (detailCurrentPage.value - 1) * detailPageSize.value;
  const end = start + detailPageSize.value;
  return detailData.value.slice(start, end);
});

// 处理每页条数变化
const handleSizeChange = (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
};

// 显示详情
const showDetail = (row: any, week: string) => {
  const details = row[week]?.details || [];
  detailData.value = details;
  currentDetailType.value = `${row.metricKey}_${week}`;
  // 重置详情对话框分页状态
  detailCurrentPage.value = 1;
  detailPageSize.value = 10;
  detailVisible.value = true;
};

// 处理详情对话框分页变化
const handleDetailSizeChange = (val: number) => {
  detailPageSize.value = val;
  detailCurrentPage.value = 1;
};

const handleDetailCurrentChange = (val: number) => {
  detailCurrentPage.value = val;
};

// 查询方法
const handleQuery = async () => {
  loading.value = true;
  try {
    // 这里应该调用实际的API
    const res = await getStatis4Data(searchForm);
    originalTableData.value = res.data;
    totalItems.value = transformedTableData.value.length;
    ElMessage.success('查询成功');
  } catch (err: any) {
    ElMessage.error('查询失败：' + err.message);
  } finally {
    loading.value = false;
  }
};

// 重置方法
const handleReset = () => {
  searchForm.searchDate = getDefaultDateRange();
  currentPage.value = 1;
};

// 导出方法
const handleExport = async () => {
  // 如果没有数据，先尝试查询
  if (!transformedTableData.value || transformedTableData.value.length === 0) {
    ElMessage.warning('没有数据可以导出，请先点击统计按钮查询数据');
    return;
  }

  exportLoading.value = true;
  try {
    // 准备导出数据
    const exportData = transformedTableData.value.map(row => {
      const exportRow: any = {
        '统计指标': row.metricName
      };
      
      weekColumns.value.forEach(week => {
        // 特殊处理LAR目标行和特殊工序行，导出时显示为空
        if (row.metricName === 'LAR目标' || row.metricName === '特殊工序') {
          exportRow[week] = '';
        } else {
          exportRow[week] = row[week]?.value || '-';
        }
      });
      
      return exportRow;
    });

    // 准备表头映射
    const headers: Record<string, string> = {
      '统计指标': '统计指标'
    };
    
    // 添加周次列到表头
    weekColumns.value.forEach(week => {
      headers[week] = week;
    });

    const fileName = `精密零部件外发一次性合格率统计_${new Date().getTime()}`;

    // 使用绿色表头
    await exportToExcel(exportData, headers, fileName, '精密零部件外发一次性合格率统计', '67C23A');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    exportLoading.value = false;
  }
};

// 详情导出方法
const handleDetailExport = async () => {
  if (!detailData.value || detailData.value.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }

  detailExportLoading.value = true;
  try {
    // 定义表头映射
    const headers = {
      pjNo: 'PJ号',
      workSequence: '工序号',
      workSequenceName: '工序名称',
      scanDate: '扫描日期',
      supplier: '供应商',
      isRTV: '是否退货',
      week: '周次',
      automation: '自动化',
      precision: '精密',
      isSpecialProcess: '是否全工序',
      action: '动作'
    };

    // 生成文件名
    const fileName = `精密零部件外委一次性合格率统计_详情数据_${new Date().getTime()}`;

    // 使用蓝色表头
    await exportToExcel(detailData.value, headers, fileName, '详情数据', '4472C4');
    ElMessage.success('导出成功');
  } catch (err: any) {
    ElMessage.error('导出失败：' + err.message);
  } finally {
    detailExportLoading.value = false;
  }
};

onMounted(() => {
  totalItems.value = transformedTableData.value.length;
});
</script>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}
</style>
