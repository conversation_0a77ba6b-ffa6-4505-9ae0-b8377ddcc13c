<template>
  <div class="p-2" v-loading="loading">
    <el-card>
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" @keyup.enter="getDataList()">
        <el-form-item prop="bargainDate" label="统计日期">
          <el-date-picker style="width: 250px" clearable v-model="queryParams.bargainDate" type="daterange"
            value-format="YYYY-MM-DD" start-placeholder="统计日期(开始)" end-placeholder="接单日期(结束)" />
        </el-form-item>
        <el-form-item prop="merchandiser">
          <el-select :style="widthStyle" v-model="queryParams.merchandiser" placeholder="跟单工程师">
            <el-option v-for="item in merchandisers" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button type="primary" :icon="Download" @click="handleExport">
            下载
          </el-button>
        </el-form-item>
      </el-form>

      <el-divider />

      <el-table border :data="dataList" show-overflow-tooltip>
        <el-table-column label="上层订单号" :width="widthMedium" align="center" prop="upMo" />
        <el-table-column label="上层状态" :width="widthMedium" align="center" prop="upStatus" />
        <el-table-column label="Id" :width="widthMedium" align="center" prop="id" />
        <el-table-column label="状态" :width="widthMedium" align="center" prop="moState" />
        <el-table-column label="备注" :width="widthWide" align="center" prop="moRem" />
        <el-table-column label="客户备注" :width="widthMedium" align="center" prop="customerNote" />
        <el-table-column label="接单日期" :width="widthMedium" align="center" prop="bargainDate" />
        <el-table-column label="客户代码" align="center" prop="custNo" />
        <el-table-column label="客户PO号" :width="widthMedium" align="center" prop="poNo" />
        <el-table-column label="客户要求交期" :width="widthMedium" align="center" prop="deliveryDate" />
        <el-table-column label="pmc要求交期" :width="widthMedium" align="center" prop="pmcReqDate" />
        <el-table-column label="行业" align="center" prop="trade" />
        <el-table-column label="零件分类" align="center" prop="partType" />
        <el-table-column label="MO号" :width="widthMedium" align="center" prop="moNo" />
        <el-table-column label="行号" align="center" prop="lineNo" />
        <el-table-column label="图号" :width="widthWide + 150" align="center" prop="drawNumber" />
        <el-table-column label="版本" align="center" prop="partVer" />
        <el-table-column label="订单数量" align="center" prop="orderQty" />
        <el-table-column label="生产数量" align="center" prop="productionQty" />
        <el-table-column label="收料时间" :width="widthMedium" align="center" prop="receiveMaterialTime" />
        <el-table-column label="计划类型" align="center" prop="planType" />
        <el-table-column label="利润中心" :width="widthMedium" align="center" prop="profitCenter" />
        <el-table-column label="生产车间" align="center" prop="workshop" />
        <el-table-column label="订单类别" :width="widthWide" align="center" prop="orderType" />
        <el-table-column label="要求完成日期" :width="widthMedium" align="center" prop="dueDate" />
        <el-table-column label="材料" :width="widthWide" align="center" prop="material" />
        <el-table-column label="末工序时间" :width="widthMedium" align="center" prop="lastOpTime" />
        <el-table-column label="是否已编程" :width="widthMedium" align="center" prop="hasProgramed" />
        <el-table-column label="是否有标准件" :width="widthMedium" align="center" prop="hasStandardComponents" />
        <el-table-column label="工艺收图时间" :width="widthMedium" align="center" prop="technologyReceivedTime" />
        <el-table-column label="图纸难度等级" :width="widthMedium" align="center" prop="drawingsDifficultyLevel" />
        <el-table-column label="是否买料" :width="widthMedium" align="center" prop="isBuyMaterial" />
        <el-table-column label="下单员姓名" :width="widthMedium" align="center" prop="orderName" />
        <el-table-column label="跟单人" :width="widthMedium" align="center" prop="merchandiser" />
        <el-table-column label="工艺编制人" :width="widthMedium" align="center" prop="technologyDesigner" />
        <el-table-column label="当前工序" :width="widthMedium" align="center" prop="currentProcess" />
        <el-table-column label="是否电镀" align="center" prop="isElectroplate" />
        <el-table-column label="急单" align="center" prop="urgent" />
        <el-table-column label="打样标识" align="center" prop="proofMo" />
      </el-table>

      <pagination :hide-on-single-page="true" :total="queryParams.total" v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize" :autoScroll="false" @pagination="getDataList"
        @current-change="pageChange" />
    </el-card>
  </div>
</template>

<script setup name="DeliveredRemains" lang="ts">
import { pageDataList, getMerchandisers } from '@/api/pmc/deliveredRemains/api';
import dayjs from 'dayjs';
import { Download } from '@element-plus/icons-vue'
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const dataList = ref<any[]>([]);
const loading = ref(false);
// const downloadLoading = ref(false);
const merchandisers = ref<any[]>([]);
const widthNarrow = 100
const widthMedium = 150
const widthWide = 200
const widthStyle = ref({
  'width': widthWide + 'px'
})
const minWidthStyle = ref({
  'minWidth': widthWide + 'px'
})
const queryFormRef = ref();
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
  bargainDate: [
    dayjs().startOf('day').subtract(30, 'day').format('YYYY-MM-DD'),
    dayjs().endOf('day').format('YYYY-MM-DD')
  ],
  merchandiser: '',
  drNumber: '',
});

const getQueryParams = (): Object => {
  let query = JSON.parse(JSON.stringify(queryParams));
  query.bargainDate[0] += ' 00:00:00'
  query.bargainDate[1] += ' 23:59:59'
  return query;
}

const getDataList = async () => {
  loading.value = true;
  pageDataList(getQueryParams())
    .then((res) => {
      dataList.value = res.data.rows;
      queryParams.total = res.data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 获取跟单工程师列表 */
const getMerchandiserList = async () => {
  getMerchandisers().then((res) => {
    merchandisers.value = res.data.map((item: any) => {
      return {
        label: item,
        value: item
      }
    });
  });
}

const numberFormatter = (row: any, column: any, cellValue: any) => {
  if (cellValue === null || typeof cellValue === 'undefined' || cellValue === '') {
    return ''
  }
  if (typeof cellValue === 'boolean') {
    return cellValue ? 'Y' : '';
  }
  return (Math.round(cellValue * 100) / 100).toString();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  getDataList();
}

/** 导出按钮操作 */
const handleExport = () => {
  loading.value = true;
  try {
    proxy?.download('/pmc/delivered_remains/statistics/download', {
      ...getQueryParams()
    }, `项目出货完成未领料零件清单${new Date().getTime()}.xlsx`)
  } catch (error) {
    ElMessage.error('导出失败');
  } finally {
    loading.value = false;
  }
}

onMounted(() => {
  getDataList();
  getMerchandiserList();
});

const pageChange = (val: number) => {
  queryParams.pageNum = val;
  getDataList();
}

</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 0;
  margin-right: 16px;
}
</style>
