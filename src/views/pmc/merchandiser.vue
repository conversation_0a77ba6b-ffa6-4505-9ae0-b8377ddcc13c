<template>
  <div class="p-4">
    <el-card v-loading="loading" element-loading-text="查询中">
      <el-form :inline="true" :model="searchForm" label-width="80px">
        <el-form-item label="原跟单人">
          <el-select v-model="searchForm.merchandiser" placeholder="请选择" style="width: 150px" clearable>
            <el-option v-for="item in mData" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="利润中心">
          <el-input v-model="searchForm.profitCenter" style="width: 150px" clearable />
        </el-form-item>
        <el-form-item label="客户代码">
          <el-input v-model="searchForm.custNo" style="width: 150px" clearable />
        </el-form-item>
        <el-form-item label="PM号">
          <el-input v-model="searchForm.pmNo" style="width: 150px" clearable />
        </el-form-item>
        <el-form-item label="DR号">
          <el-input v-model="searchForm.drNo" style="width: 150px" clearable />
        </el-form-item>
        <el-form-item label="DR项次">
          <el-input v-model="searchForm.drItm" style="width: 150px" clearable />
        </el-form-item>
        <el-form-item label="SO号">
          <el-input v-model="searchForm.soNo" style="width: 150px" clearable />
        </el-form-item>
        <el-form-item label="SO项次">
          <el-input v-model="searchForm.soItm" style="width: 150px" clearable />
        </el-form-item>
        <el-form-item label="新跟单人" class="new-merchandiser-label"> <!-- 添加自定义类名 -->
          <el-select v-model="searchForm.newMerchandiser" placeholder="请选择" style="width: 150px" clearable>
            <el-option v-for="item in mData" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="buttonSearch">查询</el-button>
          <el-button type="primary" @click="reset">重置</el-button>
          <el-button type="primary" @click="editTableData">修改</el-button>
          <el-button type="primary" @click="saveEdit">保存</el-button>
          <el-button type="primary" :loading="downLoading" @click="download">下载</el-button>
        </el-form-item>
      </el-form>
      <el-divider></el-divider>
      <el-table :data="pagedTableData" ref="tableRef" border style="width: 100%" row-key="id">
        <el-table-column v-for="col in tableHead" :key="col.dataIndex" :prop="col.dataIndex" :width="col.width"
          :label="col.title" align="center" header-align="center" show-overflow-tooltip>
          <template v-if="col.dataIndex === 'merchandiser'" #default="{ row }">
            <span class="yellow-background">{{ row.merchandiser }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-model:current-page="pagination.current" v-model:page-size="pagination.pageSize"
        :total="pagination.total" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
        @current-change="pageChange" @size-change="pageSizeChange" style="margin-top: 16px; text-align: right;" />
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import {
  editMerchandiserData,
  getMerchandiserList,
  MERCHANDISER_DL_URL,
  queryMerchandiserData
} from '@/api/pmc/merchandiser';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const loading = ref(false);
const downLoading = ref(false);
const exportLoading = ref(false);
const mData = ref<string[]>([]);
const tableData = ref<any[]>([]);
const selectedRows = ref<any[]>([]);
const selectedIds = ref<any[]>([]);
const tableRef = ref();
const widthNarrow = 100
const widthMedium = 150
const widthWide = 200

const tableHead = ref([
  { title: '状态', dataIndex: 'moState', width: widthNarrow },
  { title: '备注', dataIndex: 'moRem', width: widthMedium },
  { title: '接单日期', dataIndex: 'bargainDate', width: widthNarrow },
  { title: '客户代码', dataIndex: 'custNo', width: widthNarrow },
  { title: 'PM_NO', dataIndex: 'pmNo', width: widthNarrow },
  { title: '客户PO号', dataIndex: 'poNo', width: widthMedium },
  { title: 'DR_NO', dataIndex: 'drNo', width: widthNarrow },
  { title: 'DR_ITM', dataIndex: 'drItm', width: widthNarrow },
  { title: '客户要求交期', dataIndex: 'deliveryDate', width: widthMedium },
  { title: '类别描述', dataIndex: 'orderDescription', width: widthWide },
  { title: 'SO_NO', dataIndex: 'soNo', width: widthMedium },
  { title: 'SO_ITM', dataIndex: 'soItm', width: widthMedium },
  { title: 'MO_NO', dataIndex: 'moNo', width: widthMedium },
  { title: '品号', dataIndex: 'partId', width: widthMedium },
  { title: '零件分类', dataIndex: 'partType', width: widthNarrow },
  { title: '图号', dataIndex: 'drawNumber', width: widthWide + 100 },
  { title: '版本', dataIndex: 'partVer', width: widthMedium },
  { title: '描述', dataIndex: 'desc', width: widthWide + 100 },
  { title: '跟单负责人', dataIndex: 'merchandiserCharge', width: widthNarrow },
  { title: '跟单人', dataIndex: 'merchandiser', width: widthNarrow },
  { title: '利润中心', dataIndex: 'profitCenter', width: widthNarrow },
  { title: '订单数量', dataIndex: 'orderQty', width: widthNarrow },
  { title: '生产数量', dataIndex: 'productionQty', width: widthNarrow },
  { title: '制造部门', dataIndex: 'mfgDept', width: widthMedium },
  { title: '生产车间', dataIndex: 'workshop', width: widthNarrow },
  { title: '合单标识', dataIndex: 'mergeMo', width: widthMedium },
  { title: '打样标识', dataIndex: 'proofMo', width: widthMedium },
  { title: '下单员姓名', dataIndex: 'orderName', width: widthNarrow },
  { title: 'bom责任人', dataIndex: 'bomCharge', width: widthMedium }
]);

const searchForm = reactive({
  profitCenter: '',
  custNo: '',
  drNo: '',
  drItm: '',
  soNo: '',
  soItm: '',
  merchandiser: '',
  newMerchandiser: '',
  pmNo: ''
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

const pagedTableData = computed(() => {
  const start = (pagination.current - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;
  return tableData.value.slice(start, end);
});

const reset = () => {
  Object.keys(searchForm).forEach(key => (searchForm[key] = ''));
  tableData.value = [];
};

const download = () => {
  downLoading.value = true;
  proxy?.download(
    MERCHANDISER_DL_URL, { ...searchForm }, `跟单负责人_${new Date().getTime()}.xlsx`
  );
  downLoading.value = false;
};

const handleSelectionChange = (val: any[]) => {
  selectedRows.value = val;
  selectedIds.value = val.map(row => row.id);
};

const restoreSelection = () => {
  nextTick(() => {
    const table = tableRef.value;
    if (table && selectedIds.value.length > 0) {
      table.clearSelection();
      pagedTableData.value.forEach(row => {
        if (selectedIds.value.includes(row.id)) {
          table.toggleRowSelection(row, true);
        }
      });
    }
  });
};

const editTableData = () => {
  pagedTableData.value.forEach((row) => {
    row.merchandiser = searchForm.newMerchandiser;
  });
};

const search = async () => {
  loading.value = true;
  const page = {
    page: pagination.current,
    size: pagination.pageSize
  };
  const param = { ...searchForm, ...page };
  await queryMerchandiserData(param).then((res) => {
    tableData.value = res.data;
    pagination.total = res.data.length;
    ElMessage.success(res.msg);
  }).catch((error) => {
    ElMessage.error(error);
  }).finally(() => {
    loading.value = false;
  });
};

const buttonSearch = () => {
  pagination.current = 1;
  search();
};

const pageChange = (cur: number) => {
  pagination.current = cur;
};
const pageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
};

const saveEdit = async () => {
  if (searchForm.newMerchandiser === '' || pagedTableData.value.length === 0) {
    ElMessage.warning('请选择新跟单人');
    return;
  }
  const data: number[] = [];
  pagedTableData.value.forEach((en) => {
    data.push(en.id);
  });
  const body = {
    ids: data,
    newMerchandiser: searchForm.newMerchandiser
  };
  await editMerchandiserData(body).then((res) => {
    if (res.code === 200) {
      ElMessage.success(res.msg);
      search();
    }
  }).catch((error) => {
    ElMessage.error(error);
  }).finally(() => {
    loading.value = false;
  });
};

const loadMerchandiserData = async () => {
  await getMerchandiserList().then((res) => {
    mData.value = res.data;
  }).catch((err) => {
    ElMessage.error(err);
  });
};

onMounted(() => {
  loadMerchandiserData();
});
</script>

<style scoped>
.yellow-background {
  background: #fffbe6;
  padding: 0 4px;
  border-radius: 2px;
}

/* 添加深度选择器样式 */
:deep(.new-merchandiser-label .el-form-item__label) {
  color: red;
}
</style>
