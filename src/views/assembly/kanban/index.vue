<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="MO号" prop="moNo">
              <el-input v-model="queryParams.moNo" placeholder="请输入MO号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备名称" prop="sbName">
              <el-input v-model="queryParams.sbName" placeholder="请输入设备名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="负责人" prop="mUsr">
              <el-input v-model="queryParams.mUsr" placeholder="请输入负责人" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="负责人2" prop="mUsr2">
              <el-input v-model="queryParams.mUsr2" placeholder="请输入负责人2" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['assembly:kanban:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['assembly:kanban:edit']"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['assembly:kanban:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['assembly:kanban:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="kanbanList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column width="40px" label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="日期" align="center" prop="sysdt" width="100">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sysdt, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="MO号" width="140" :show-overflow-tooltip="true" align="center" prop="moNo" />
        <el-table-column label="设备名称" width="190" :show-overflow-tooltip="true" align="center" prop="sbName" />
        <el-table-column label="装配状态" align="center" prop="sbSta" />
        <el-table-column label="图号" width="220" :show-overflow-tooltip="true" align="center" prop="dwgNo" />
        <el-table-column label="订单数量" width="55" align="center" prop="oQty" />
        <el-table-column label="装配数量" width="55" align="center" prop="zpQty" />
        <el-table-column label="当前进度" width="55" align="center" prop="curJd" />
        <el-table-column label="当前进度" align="center" prop="curJd2" />
        <el-table-column label="产品组" align="center" prop="prdGroup" />
        <el-table-column label="负责人" align="center" prop="mUsr" />
        <el-table-column label="负责人2" align="center" prop="mUsr2" />
        <el-table-column label="标记" align="center" prop="flag" />
        <el-table-column label="MO状态" width="140" :show-overflow-tooltip="true" align="center" prop="moSta" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['assembly:kanban:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['assembly:kanban:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改装配看板记录对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="kanbanFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="日期" prop="sysdt">
          <el-date-picker clearable v-model="form.sysdt" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="MO号" prop="moNo">
          <el-input v-model="form.moNo" placeholder="请输入MO号" />
        </el-form-item>
        <el-form-item label="设备名称" prop="sbName">
          <el-input v-model="form.sbName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="装配状态" prop="sbSta">
          <el-input v-model="form.sbSta" placeholder="请输入装配状态" />
        </el-form-item>
        <el-form-item label="图号" prop="dwgNo">
          <el-input v-model="form.dwgNo" placeholder="请输入图号" />
        </el-form-item>
        <el-form-item label="订单数量" prop="oQty">
          <el-input v-model="form.oQty" placeholder="请输入订单数量" />
        </el-form-item>
        <el-form-item label="装配数量" prop="zpQty">
          <el-input v-model="form.zpQty" placeholder="请输入装配数量" />
        </el-form-item>
        <el-form-item label="当前进度" prop="curJd">
          <el-input v-model="form.curJd" placeholder="请输入当前进度" />
        </el-form-item>
        <el-form-item label="当前进度" prop="curJd2">
          <el-input v-model="form.curJd2" placeholder="请输入当前进度" />
        </el-form-item>
        <el-form-item label="产品组" prop="prdGroup">
          <el-input v-model="form.prdGroup" placeholder="请输入产品组" />
        </el-form-item>
        <el-form-item label="负责人" prop="mUsr">
          <el-input v-model="form.mUsr" placeholder="请输入负责人" />
        </el-form-item>
        <el-form-item label="负责人2" prop="mUsr2">
          <el-input v-model="form.mUsr2" placeholder="请输入负责人2" />
        </el-form-item>
        <el-form-item label="标记" prop="flag">
          <el-input v-model="form.flag" placeholder="请输入标记" />
        </el-form-item>
        <el-form-item label="MO状态" prop="moSta">
          <el-input v-model="form.moSta" placeholder="请输入MO状态" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Kanban" lang="ts">
import { addKanban, delKanban, getKanban, listKanban, updateKanban } from '@/api/assembly/kanban';
import { KanbanForm, KanbanQuery, KanbanVO } from '@/api/assembly/kanban/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const kanbanList = ref<KanbanVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const kanbanFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: KanbanForm = {
  id: undefined,
  sysdt: undefined,
  moNo: undefined,
  sbName: undefined,
  sbSta: undefined,
  dwgNo: undefined,
  oQty: undefined,
  zpQty: undefined,
  curJd: undefined,
  curJd2: undefined,
  prdGroup: undefined,
  mUsr: undefined,
  mUsr2: undefined,
  flag: undefined,
  moSta: undefined
};
const data = reactive<PageData<KanbanForm, KanbanQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    moNo: undefined,
    sbName: undefined,
    mUsr: undefined,
    mUsr2: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询装配看板记录列表 */
const getList = async () => {
  loading.value = true;
  const res = await listKanban(queryParams.value);
  kanbanList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  kanbanFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: KanbanVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加装配看板记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: KanbanVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getKanban(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改装配看板记录';
};

/** 提交按钮 */
const submitForm = () => {
  kanbanFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateKanban(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addKanban(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: KanbanVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除装配看板记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delKanban(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'assembly/kanban/export',
    {
      ...queryParams.value
    },
    `kanban_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
