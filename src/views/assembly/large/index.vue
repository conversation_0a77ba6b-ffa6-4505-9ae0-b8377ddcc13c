<template>
  <el-row class="big-screen">
    <!-- 标题栏 -->
    <div class="header-section">
      <div class="title" @click="toggleFullScreen">沃德产业园设备装配实时状态</div>
      <div class="header-right">
        <div class="time">{{ currentTime }}</div>
        <div class="button-container">
          <div class="button-group">
            <el-button class="status-btn" color="red">&lt;30%</el-button>
            <el-button class="status-btn" color="orange">30%-60%</el-button>
            <el-button class="status-btn" color="green">&gt;=60%</el-button>
          </div>
          <div class="total-count">当前设备总装数量：{{ totalDevices }} 台</div>
        </div>
      </div>
    </div>

    <!-- 设备列表区域 -->
    <div class="device-container">
      <div class="device-row">
        <div class="device-card" v-for="(device, index) in firstRowDevices" :key="`row1-${device.moNo}-${index}`">
          <div class="device-header">
            <span class="mo-no">{{ device.moNo }}</span>
            <div class="status">
              <el-badge status="processing" />
              <span>装配中</span>
            </div>
          </div>
          <div class="device-info">
            <h3 class="device-name">{{ device.sbName }}</h3>
            <div class="device-image">
              <img :src="imgUrl" alt="设备图片" />
            </div>
          </div>
          <div class="quantity">
            <span>数量：</span>
            <span>1</span>
          </div>
          <div class="progress-section">
            <div class="progress-item">
              <span>机构进度：</span>
              <!--              :status="getProgressStatus(device.curJd)"-->
              <el-progress :color="customColorMethod" :percentage="device.curJd" />
            </div>
            <div class="progress-item">
              <span>电气进度：</span>
              <el-progress :color="customColorMethod" :percentage="device.curJd2" />
            </div>
          </div>
          <div class="responsible-persons">
            <span>机构负责人：{{ device.musr || '-' }}</span>
            <span>电气负责人：{{ device.musr2 || '-' }}</span>
          </div>
        </div>
      </div>
      <div class="device-row">
        <div class="device-card" v-for="(device, index) in secondRowDevices" :key="`row2-${device.moNo}-${index}`">
          <div class="device-header">
            <span class="mo-no">{{ device.moNo }}</span>
            <div class="status">
              <el-badge status="processing" />
              <span>装配中</span>
            </div>
          </div>
          <div class="device-info">
            <h3 class="device-name">{{ device.sbName }}</h3>
            <div class="device-image">
              <img width="120" height="120" :src="imgUrl" class="thumbnail-img" alt="设备图片" />
            </div>
          </div>
          <div class="quantity">
            <span>数量：</span>
            <span>1</span>
          </div>
          <div class="progress-section">
            <div class="progress-item">
              <span>机构进度：</span>
              <el-progress :percentage="device.curJd" :color="customColorMethod" />
            </div>
            <div class="progress-item">
              <span>电气进度：</span>
              <el-progress :percentage="device.curJd2" :color="customColorMethod" />
            </div>
          </div>
          <div class="responsible-persons">
            <span>机构负责人：{{ device.musr || '-' }}</span>
            <span>电气负责人：{{ device.musr2 || '-' }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="chart-wrapper">
      <div ref="echartsRef" class="chart-content"></div>
    </div>

    <Copyright class="fixed" />
  </el-row>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useUserStore } from '@/store/modules/user';
import * as echarts from 'echarts';
import { getEcharsData, getInfos } from '@/api/assembly/large';
import imgUrl from '@/assets/images/设备.png';
import { KanbanForm, KanbanQuery } from '@/api/assembly/kanban/types';
import Copyright from '@/components/Copyright/index.vue';

// 状态定义
const deviceList = ref([]);
const totalDevices = ref(0);
const currentTime = ref('');
const currentPage = ref(1);
const totalPages = ref(1);
const pageSize = 10;
// 分页参数
const data = reactive<PageData<KanbanForm, KanbanQuery>>({
  queryParams: {
    pageNum: 1,
    pageSize: 10
  },
  form: undefined,
  rules: undefined
});
// 自定义颜色数组，用于进度条的颜色设置
const customColorMethod = (percentage: number) => {
  if (percentage < 30) {
    return '#b71225'; // 红色
  } else if (percentage >= 30 && percentage < 60) {
    return '#e6a23c'; // 橙色
  } else {
    return '#67c23a'; // 绿色
  }
};
const { queryParams } = toRefs(data);

// 获取进度条状态
const getProgressStatus = (percent: number) => {
  if (percent <= 30) return 'exception';
  if (percent < 60) return 'warning';
  return 'success';
};

// 更新时间
const updateTime = () => {
  const now = new Date();
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

// 添加计算属性来分割设备列表
const firstRowDevices = computed(() => deviceList.value.slice(0, 5));
const secondRowDevices = computed(() => deviceList.value.slice(5, 10));

// 修改fetchDevices函数，添加数据去重处理
const fetchDevices = async () => {
  try {
    const res = await getInfos({
      pageNum: currentPage.value,
      pageSize: pageSize
    });

    if (res?.data) {
      // 确保数据的唯一性
      const uniqueDevices = res.data.data.reduce((acc, current) => {
        const exists = acc.find((item) => item.moNo === current.moNo);
        if (!exists) {
          acc.push(current);
        }
        return acc;
      }, []);

      deviceList.value = uniqueDevices;
      totalDevices.value = res.data.total;
      totalPages.value = Math.ceil(res.data.total / pageSize);

      currentPage.value = currentPage.value >= totalPages.value ? 1 : currentPage.value + 1;
      return true;
    }
    return false;
  } catch (error) {
    console.error('获取设备列表失败:', error);
    return false;
  }
};

const echartsRef = ref<HTMLElement | null>(null);
type EChartsType = echarts.ECharts;
let chartInstance: EChartsType | null = null; // 初始化为 null
// ECharts 配色优化
const initChart = () => {
  if (echartsRef.value) {
    chartInstance = echarts.init(echartsRef.value);
    const option = {
      legend: {
        itemWidth: 7,
        itemHeight: 7,
        left: 'center',
        textStyle: {
          color: '#333',
          fontWeight: 500
        },
        padding: 1,
        top: 'auto'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line'
        },
        backgroundColor: '#fff',
        borderColor: '#1976d2',
        borderWidth: 1,
        textStyle: {
          color: '#1976d2',
          fontSize: 12
        }
      },
      grid: {
        top: '15%',
        bottom: '10%',
        left: '5%',
        right: '5%',
        containLabel: true
      },
      xAxis: {
        boundaryGap: true,
        axisLine: {
          show: true,
          lineStyle: {
            color: '#bbb'
          }
        },
        axisTick: { show: false, alignWithLabel: true },
        axisLabel: {
          color: '#333',
          fontSize: 13,
          interval: 0,
          padding: [-5, 0, 2, 0],
          formatter: (value) => {
            if (value.length > 5) {
              return `${value.substring(0, 5)}\n${value.substring(5)}`;
            }
            return value;
          }
        },
        data: []
      },
      yAxis: [
        {
          type: 'value',
          axisLabel: {
            color: '#333'
          },
          axisLine: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: '#eee'
            }
          }
        },
        {
          type: 'value',
          position: 'right',
          scale: true,
          max: 200,
          min: 0,
          axisLine: {
            lineStyle: {
              color: '#bbb'
            }
          },
          axisLabel: {
            color: '#1976d2',
            showMaxLabel: true,
            fontSize: 10,
            formatter: (value) => `${value.toFixed(2)}%`
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '标准工时',
          type: 'bar',
          data: [],
          barGap: '20%',
          barCategoryGap: '40%',
          itemStyle: {
            color: '#91cc75',
            borderType: 'dashed'
          },
          label: {
            show: true,
            position: 'top',
            color: '#333',
            fontSize: 12
          }
        },
        {
          name: '人员工时',
          type: 'bar',
          itemStyle: { color: '#e6a23c' },
          data: [],
          label: {
            show: true,
            position: 'top',
            color: '#333',
            fontSize: 12
          }
        },
        {
          name: '人员效率',
          type: 'line',
          data: [],
          symbol: 'circle',
          symbolSize: 7,
          yAxisIndex: 1,
          itemStyle: {
            color: '#1976d2',
            borderType: 'solid',
            borderWidth: 2
          },
          lineStyle: {
            color: '#1976d2',
            width: 3
          },
          label: {
            show: true,
            position: 'top',
            color: '#1976d2',
            fontSize: 12,
            formatter: (params) => `${params.value.toFixed(2)}%`
          }
        }
      ]
    };
    chartInstance.setOption(option);
  }
};

// 更新图表数据
const updateChart = async () => {
  if (!chartInstance) return;
  try {
    const res = await getEcharsData();
    if (res?.data) {
      const chartData = res.data as ChartDataItem[];

      chartInstance.setOption({
        xAxis: {
          data: chartData.map((item) => item.stationName)
        },
        series: [
          {
            name: '标准工时',
            data: chartData.map((item) => item.standardWorkHours)
          },
          {
            name: '人员工时',
            data: chartData.map((item) => item.scanningManHours)
          },
          {
            name: '人员效率',
            data: chartData.map((item) => item.personnelEfficiency)
          }
        ]
      });
    }
  } catch (error) {
    console.error('更新图表数据失败:', error);
  }
};

// 全屏切换
const toggleFullScreen = () => {
  const element = document.querySelector('.big-screen');
  if (!element) return;

  if (!document.fullscreenElement) {
    element.requestFullscreen().catch((err) => {
      console.error(`全屏错误: ${err.message}`);
    });
  } else {
    document.exitFullscreen();
  }
};

// 生命周期钩子
onMounted(() => {
  updateTime();
  fetchDevices();
  initChart();
  updateChart();

  // 定时器
  const timeInterval = setInterval(updateTime, 1000);
  const deviceInterval = setInterval(() => fetchDevices(), 30000); // 改为30秒
  const chartInterval = setInterval(() => updateChart(), 180000);

  // 自动全屏（特定用户）
  const userStore = useUserStore();
  const autoFullscreenUsers = ['ittest', 'citrix2F1', 'citrix2F2', 'citrix6F1', 'citrix6F2'];
  if (autoFullscreenUsers.includes(userStore.name)) {
    toggleFullScreen();
  }

  onUnmounted(() => {
    clearInterval(timeInterval);
    clearInterval(deviceInterval);
    clearInterval(chartInterval);
    chartInstance?.dispose();
  });
});
</script>

<style lang="scss" scoped>
.big-screen {
  min-height: 100%;
  background-color: #fff;
  padding: 4px;
  color: #222;
  display: flex;
  flex-direction: column;

  // 全屏样式
  &:fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    width: 100vw;
    height: 100vh;
  }

  .header-section {
    height: 60px;
    position: relative;
    margin-bottom: 15px;
    .title {
      font-size: 30px;
      font-weight: bold;
      text-align: center;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      cursor: pointer;
      color: #222;
      letter-spacing: 2px;
      text-shadow: 0 2px 8px #f0f0f0;
    }
    .header-right {
      position: absolute;
      right: 15px;
      top: 0;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;
      .time {
        font-size: 14px;
        margin-bottom: 4px;
        color: #333;
      }
      .button-container {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 4px;
        .button-group {
          display: flex;
          flex-direction: row;
          gap: 0;
          margin-bottom: 2px;
        }
        .status-btn {
          width: 70px;
          height: 18px;
          padding: 0;
          font-size: 12px;
          line-height: 16px;
          border-radius: 0;
          color: #fff;
          border: none;
          &.el-button {
            box-shadow: none;
          }
          &:first-child {
            background: #f56c6c;
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
          }
          &:nth-child(2) {
            background: #e6a23c;
          }
          &:last-child {
            background: #67c23a;
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
          }
        }
        .total-count {
          font-size: 12px;
          color: #333;
        }
      }
    }
  }

  .device-container {
    margin-top: 40px;
    margin-bottom: 320px;
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 18px;
    padding: 0 24px 0 24px;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: -40px;
      height: 60px;
      pointer-events: none;
      background: linear-gradient(to bottom, rgba(255,255,255,0.01) 0%, rgba(255,255,255,0.5) 60%, #fff 100%);
      z-index: 3;
      filter: blur(3px);
    }
    .device-row {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 18px;
      height: auto;
    }
  }

  .device-card {
    flex: 1;
    min-width: 0;
    background: #fff;
    padding: 16px 12px 12px 12px;
    border-radius: 14px;
    display: flex;
    flex-direction: column;
    height: 220px;
    border: 1.5px solid #e0e7ef;
    position: relative;
    gap: 10px;
    box-shadow: 0 4px 24px 0 rgba(30, 60, 255, 0.08), 0 0 8px 0 #e0e7ef55;
    transition: all 0.25s cubic-bezier(0.4, 2, 0.6, 1);
    &:hover {
      transform: translateY(-6px) scale(1.03);
      box-shadow: 0 8px 32px 0 #3a4fff22, 0 0 16px 0 #3a4fff33;
      border-color: #a3a8f0;
    }
    .device-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 24px;
      .mo-no {
        font-size: 13px;
        font-weight: bold;
        color: #222;
      }
      .status {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #409eff;
      }
    }
    .device-info {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      height: 60px;
      .device-name {
        flex: 1;
        font-size: 14px;
        font-weight: bold;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #222;
      }
      .device-image {
        width: 100px;
        height: 100px;
        flex-shrink: 0;
        border-radius: 4px;
        overflow: hidden;
        background: #f5f7fa;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
    .quantity {
      font-size: 12px;
      height: 20px;
      display: flex;
      align-items: center;
      color: #333;
    }
    .progress-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 8px;
      .progress-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        span {
          min-width: 70px;
          color: #333;
        }
        :deep(.el-progress) {
          flex: 1;
        }
        :deep(.el-progress-bar__outer) {
          background-color: #f0f0f0;
        }
        :deep(.el-progress__text) {
          color: #222;
        }
      }
    }
    .responsible-persons {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 24px;
      font-size: 12px;
      border-top: 1px solid #e0e7ef;
      padding-top: 8px;
      margin-top: auto;
      color: #333;
      span {
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .chart-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 460px;
    background-color: #f8f9fb;
    padding: 12px;
    z-index: 10;
    border-top: 1px solid #e0e7ef;
    &::before {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 12px;
      background: radial-gradient(ellipse at center, #e0e7ef88 0%, #fff0 80%);
      z-index: 11;
      pointer-events: none;
      filter: blur(2px);
    }
    .chart-content {
      width: 100%;
      height: 100%;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px 0 #e0e7ef33;
      border: 1px solid #e0e7ef;
    }
  }
}

// 优化按钮样式
:deep(.el-button) {
  &.status-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    box-shadow: none;
    &:hover {
      transform: translateY(-4px) scale(1.025);
      box-shadow: 0 8px 32px 0 #e0e7ef66, 0 0 16px 0 #e0e7ef99;
      border-color: #a3a8f0;
    }
  }
}

// 样式补充：优化标题和卡片文字颜色
.big-screen {
  .header-section {
    .title {
      color: #222;
      text-shadow: 0 2px 8px #f0f0f0;
    }
    .header-right {
      .time, .total-count {
        color: #333;
      }
    }
  }
  .device-card {
    .device-header .mo-no,
    .device-info .device-name {
      color: #222;
    }
    .quantity, .responsible-persons, .progress-item span {
      color: #333;
    }
  }
}

</style>
