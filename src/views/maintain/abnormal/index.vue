<template>
  <div class="p-4">
    <el-card class="mb-4" shadow="hover">
      <div class="card-header">装配未扫描条码报表</div>
      <el-button type="primary" @click="fetchData" style="margin-top: 20px">刷新</el-button>
      <div v-if="state.isLoading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else>
        <el-table
          :data="paginatedMoData"
          border
          style="width: 100%; margin-bottom: 20px; margin-top: 20px"
          @row-dblclick="openDialog"
          element-loading-text="加载中..."
        >
          <el-table-column prop="moNo" label="MO_NO"></el-table-column>
        </el-table>
        <!-- 分页组件 -->
        <el-pagination
          class="mt-4"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="state.total.mo"
          :page-sizes="[10, 20, 50, 100]"
          v-model:current-page="state.moPagination.pageNum"
          v-model:page-size="state.moPagination.pageSize"
          @size-change="handleSizeChange('mo', $event)"
          @current-change="handleCurrentChange('mo', $event)"
        />
      </div>
    </el-card>

    <!-- 弹窗 -->
    <el-dialog
      v-model="state.dialogVisible"
      :title="`MO号: ${state.selectedRow?.moNo || ''} 未扫描条码明细`"
      width="90%"
      :before-close="handleClose"
      destroy-on-close
      top="5vh"
    >
      <div v-if="state.detailLoading" class="dialog-loading">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else>
        <el-alert v-if="state.total.details === 0" type="info" :closable="false" title="没有找到相关数据" />
        <el-table
          v-else
          :data="paginatedDetailData"
          border
          stripe
          style="font-size: 12px; width: 100%"
          max-height="450px"
          :header-cell-style="{ background: '#f5f7fa', fontSize: '13px' }"
          class="detail-table"
        >
          <el-table-column type="index" label="序号" width="60" fixed="left"></el-table-column>
          <el-table-column prop="id" label="ID" width="150"></el-table-column>
          <el-table-column prop="barCode" label="条码" min-width="150" fixed="left"></el-table-column>
          <el-table-column prop="moNo" label="MO号" min-width="150"></el-table-column>
          <el-table-column prop="actType" label="装配类型" min-width="100"></el-table-column>
          <el-table-column prop="barType" label="条码类型" min-width="100"></el-table-column>
          <el-table-column prop="soNo" label="SO号" min-width="120"></el-table-column>
          <el-table-column prop="soItm" label="SO项次" min-width="100"></el-table-column>
          <el-table-column prop="hmoNo" label="关联号" min-width="120"></el-table-column>
          <el-table-column prop="prdName" label="产品名" min-width="200"></el-table-column>
          <el-table-column prop="prdType" label="产品型号" min-width="120"></el-table-column>
          <el-table-column prop="dwgNo" label="导入图号" min-width="200"></el-table-column>
          <el-table-column prop="pType" label="工序号" min-width="100"></el-table-column>
          <el-table-column prop="pName" label="工序名称" min-width="150"></el-table-column>
          <el-table-column prop="qty" label="数量" width="80"></el-table-column>
          <el-table-column prop="ut" label="单位" width="80"></el-table-column>
          <el-table-column prop="moDwgno" label="MO图号" min-width="200"></el-table-column>
          <el-table-column prop="testGs" label="调试工时" width="90"></el-table-column>
          <el-table-column prop="bpRy" label="标配人员" width="90"></el-table-column>
          <el-table-column prop="basGs" label="标准工时" width="90"></el-table-column>
          <el-table-column prop="wkGs" label="累计工时" width="90"></el-table-column>
          <el-table-column prop="sopBatno" label="关联号" min-width="120"></el-table-column>
          <el-table-column prop="host" label="主机" min-width="100"></el-table-column>
          <el-table-column prop="sysdt" label="操作时间" min-width="200"></el-table-column>
          <el-table-column prop="usrName" label="操作姓名" min-width="100"></el-table-column>
          <el-table-column prop="sfileName" label="文件名" min-width="300"></el-table-column>
          <el-table-column prop="endDate" label="失效日期" min-width="150"></el-table-column>
          <el-table-column prop="usr" label="操作工号" min-width="100"></el-table-column>
        </el-table>
        <!-- 弹窗表格分页组件 -->
        <el-pagination
          class="dialog-pagination"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="state.total.details"
          :page-sizes="[10, 20, 50, 100]"
          v-model:current-page="state.detailPagination.pageNum"
          v-model:page-size="state.detailPagination.pageSize"
          @size-change="handleSizeChange('detail', $event)"
          @current-change="handleCurrentChange('detail', $event)"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <el-card shadow="hover">
      <div class="card-header">进度比例未完成报表</div>
      <el-button type="primary" @click="fetchData2" style="margin-top: 20px">刷新</el-button>
      <div v-if="state.isLoading2" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      <div v-else>
        <el-table :data="paginatednoFinishData" border style="width: 100%; margin-top: 20px" element-loading-text="加载中...">
          <el-table-column prop="moNo" label="MO号"></el-table-column>
          <el-table-column prop="zpType" label="类型"></el-table-column>
          <el-table-column prop="barCode" label="条码"></el-table-column>
          <el-table-column prop="maxProd" label="最大进度"></el-table-column>
        </el-table>
        <el-pagination
          class="mt-4"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="state.total.noFinish"
          :page-sizes="[10, 20, 50, 100]"
          v-model:current-page="state.noFinishPagination.pageNum"
          v-model:page-size="state.noFinishPagination.pageSize"
          @size-change="handleSizeChange('nofinish', $event)"
          @current-change="handleCurrentChange('nofinish', $event)"
        />
      </div>
    </el-card>
  </div>
</template>
<script setup>
import { ref, computed, reactive, shallowRef, onMounted, nextTick } from 'vue';
import { getAbnormalBarcodeNotScan, getAbnormalInfoNoFinish } from '@/api/maintain/index';
import { ElMessage } from 'element-plus';

// 响应式状态管理
const state = reactive({
  isLoading: false,
  detailLoading: false,
  isLoading2: false,
  dialogVisible: false,
  selectedRow: null,
  moPagination: { pageNum: 1, pageSize: 10 },
  detailPagination: { pageNum: 1, pageSize: 10 },
  noFinishPagination: { pageNum: 1, pageSize: 10 },
  total: { mo: 0, details: 0, noFinish: 0 }
});

// 大型数据集使用shallowRef优化性能
const allMoData = shallowRef([]);
const allBarcodeData = shallowRef([]);
const allNoFinishData = shallowRef([]);
const filteredDetails = shallowRef([]);

// 通用分页计算函数
const paginate = (data, { pageNum, pageSize }) => {
  const start = (pageNum - 1) * pageSize;
  return data.slice(start, start + pageSize);
};

const paginatedMoData = computed(() => paginate(allMoData.value, state.moPagination));
const paginatedDetailData = computed(() => paginate(filteredDetails.value, state.detailPagination));
const paginatednoFinishData = computed(() => paginate(allNoFinishData.value, state.noFinishPagination));

// 数据请求逻辑
const fetchData = async () => {
  try {
    state.isLoading = true;
    allBarcodeData.value = [];
    allMoData.value = [];
    const res = await getAbnormalBarcodeNotScan({});
    if (res?.data) {
      allBarcodeData.value = res.data.sfcBarcodeList || [];
      allMoData.value = res.data.moList || [];
      state.total.mo = allMoData.value.length;
    }
  } catch (error) {
    ElMessage.error(`数据加载失败: ${error.message}`);
  } finally {
    state.isLoading = false;
  }
};
// 进度比例未完成报表数据
const fetchData2 = async () => {
  // debugger
  try {
    state.isLoading2 = true;
    allNoFinishData.value = [];
    const res = await getAbnormalInfoNoFinish({});
    if (res?.data) {
      allNoFinishData.value = res.data || [];
      state.total.noFinish = allNoFinishData.value.length;
    }
  } catch (error) {
    ElMessage.error(`数据加载失败: ${error.message}`);
  } finally {
    state.isLoading2 = false;
  }
};

// 分页处理函数
const handleSizeChange = (type, size) => {
  const paginationMap = {
    'mo': state.moPagination,
    'detail': state.detailPagination,
    'nofinish': state.noFinishPagination
  };
  const target = paginationMap[type];
  if (target) {
    target.pageSize = size;
    target.pageNum = 1;
    scrollToTop();
  }
};

const handleCurrentChange = (type, page) => {
  const paginationMap = {
    'mo': state.moPagination,
    'detail': state.detailPagination,
    'nofinish': state.noFinishPagination
  };
  const target = paginationMap[type];
  if (target) {
    target.pageNum = page;
    scrollToTop();
  }
};

// 滚动复位功能
const scrollToTop = () =>
  nextTick(() => {
    const container = document.querySelector('.main-table-container');
    container?.scrollTo(0, 0);
  });

// 弹窗相关逻辑
const openDialog = (row) => {
  state.selectedRow = row;
  state.dialogVisible = true;
  loadMoDetails(row.moNo);
};

const handleClose = () => {
  state.dialogVisible = false;
  // 重置分页参数
  state.detailPagination.pageNum = 1;
  // 清空筛选数据
  filteredDetails.value = [];
  state.total.details = 0;
};

const loadMoDetails = async (moNo) => {
  if (!moNo) return;
  state.detailLoading = true;
  try {
    filteredDetails.value = allBarcodeData.value.filter((item) => item.moNo === moNo);
    state.total.details = filteredDetails.value.length;
    state.detailPagination.pageNum = 1;
  } finally {
    state.detailLoading = false;
  }
};
</script>
<style scoped>
.p-4 {
  padding: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

/* 表头样式 */
.el-table th {
  background-color: #f5f5f5 !important;
  color: #333;
  font-weight: bold;
}

/* 表格行样式 */
.el-table tr:hover {
  background-color: #e0f7fa;
}

.pagination-container {
  margin-top: 15px;
  display: flex;
  justify-content: flex-end;
}

.dialog-loading {
  padding: 20px;
}

.dialog-footer {
  text-align: right;
}

/* 卡片标题样式 */
.card-header {
  font-size: 18px;
  font-weight: bold;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
}

/* 加载容器 */
.loading-container {
  padding: 20px;
  margin: 20px 0;
}

/* 表格内容居中对齐 */
.el-table .cell {
  text-align: center;
}

.detail-table :deep(.el-table__body td) {
  padding: 4px 0;
}

.dialog-pagination {
  margin-top: 20px;
}
</style>
