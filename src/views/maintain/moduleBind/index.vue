<template>
  <div class="p-4">
    <!-- 添加对话框组件 -->
    <el-dialog
      v-model="state.dialogVisible"
      :title="state.dialogTitle"
      width="90%"
      destroy-on-close
    >
      <el-table
        :data="state.reportTableData"
        border
        style="width: 100%"
        v-loading="state.reportLoading"
        height="600"
      >
        <el-table-column label="产品名" width="160" :show-overflow-tooltip="true" align="center" prop="cusName" />
        <el-table-column label="MO号" width="140" :show-overflow-tooltip="true" align="center" prop="moNo" />
        <el-table-column label="图号" width="220" :show-overflow-tooltip="true" align="center" prop="wksDwg" />
        <el-table-column label="楼层" width="130" :show-overflow-tooltip="true" align="center" prop="floorNo" />
        <el-table-column label="工号" align="center" prop="empNo" />
        <el-table-column label="姓名" align="center" prop="empName" />
        <el-table-column label="参与人员工号" align="center" prop="participantNo" />
        <el-table-column label="参与人员姓名" align="center" prop="participantName" />
        <el-table-column label="工站号" align="center" prop="pType" />
        <el-table-column label="标准工时" align="center" prop="basGs" />
        <el-table-column label="标配人力" align="center" prop="bpRy" />
        <el-table-column label="条码" width="160" :show-overflow-tooltip="true" align="center" prop="barCode" />
        <el-table-column label="工站名称" align="center" prop="pName" />
        <el-table-column label="开始时间" align="center" prop="bDtm" width="180" />
        <el-table-column label="结束时间" align="center" prop="eDtm" width="180" />
        <el-table-column label="实际工时" align="center" prop="sjGs" />
        <el-table-column label="良品数量" align="center" prop="qtyOk" />
        <el-table-column label="不良数量" align="center" prop="qtyNg" />
        <el-table-column label="不良原因" align="center" prop="reasonName" />
        <el-table-column label="当前进度" align="center" prop="curProd" />
        <el-table-column label="总进度" align="center" prop="sumProd" />
        <el-table-column label="状态" align="center" prop="mSta" />
        <el-table-column label="备注" align="center" prop="rem" />
        <el-table-column label="装配类型" align="center" prop="zpType" />
        <el-table-column label="ARBPL" align="center" prop="arbpl" />
        <el-table-column label="累计工时" align="center" prop="wkGs" />
      </el-table>
    </el-dialog>

    <!-- 顶部说明和搜索区域 -->
    <div class="flex items-center mb-4">
      <span class="mr-2">说明: 流程模组绑定一对多，也可多对一绑定；</span>
      <el-select v-model="state.searchType" placeholder="选择搜索类型" style="width: 150px" class="mx-2">
        <el-option v-for="item in state.searchTypeOptions" :key="item.value" :label="item.label"
                   :value="item.value"></el-option>
      </el-select>
      <el-input v-model="state.searchKeyword" placeholder="请输入搜索关键词" style="width: 300px"></el-input>
      <el-button type="primary" @click="searchBindingRecord" class="ml-2">查找绑定记录</el-button>
    </div>
    <!-- 流程模组信息表格 -->
    <el-card class="mb-4" shadow="hover">
      <el-table
        :data="state.deviceModuleData"
        border
        style="width: 100%; margin-bottom: 20px"
        :highlight-current-row="true"
        v-loading="state.isLoading"
        :fit="true"
        @cell-dblclick="handleCellDblClick"
      >
        <el-table-column type="index" label="序号" width="60"></el-table-column>
        <el-table-column prop="id" label="ID" min-width="60"></el-table-column>
        <el-table-column prop="ldmzBarcode" label="流道模组条码" min-width="200"></el-table-column>
        <el-table-column prop="ldmzPname" label="流道模组工序名" min-width="120"></el-table-column>
        <el-table-column prop="ldmzMo" label="流道模组MO号" min-width="120"></el-table-column>
        <el-table-column prop="ldmzName" label="流道模组名称" min-width="200"></el-table-column>
        <el-table-column prop="ldmzDwg" label="流道模组图号" min-width="200"></el-table-column>
        <el-table-column prop="ldmzSopBatno" label="流道模组关联号" min-width="120"></el-table-column>
        <el-table-column prop="mzLjfl" label="流道模组类别" min-width="120"></el-table-column>
        <el-table-column prop="moBarcode" label="设备条码" min-width="120"></el-table-column>
        <el-table-column prop="moPname" label="设备工序名" min-width="120"></el-table-column>
        <el-table-column prop="moNo" label="设备MO号" min-width="120"></el-table-column>
        <el-table-column prop="mDesc" label="设备名称" min-width="200"></el-table-column>
        <el-table-column prop="dwgNo" label="设备图号" min-width="200"></el-table-column>
        <el-table-column prop="sopBatno" label="设备关联号" min-width="120"></el-table-column>
        <el-table-column prop="moLjfl" label="设备类别" min-width="100"></el-table-column>
        <el-table-column prop="oOrder" label="订单数" min-width="80"></el-table-column>
        <el-table-column prop="mOrder" label="生产数" min-width="80"></el-table-column>
        <el-table-column prop="rem" label="备注" min-width="100"></el-table-column>
        <el-table-column prop="host" label="主机" min-width="100"></el-table-column>
        <el-table-column prop="sysdt" label="操作时间" min-width="200"></el-table-column>
      </el-table>

      <!-- 分页控件 -->
      <div class="flex justify-end mt-2" v-if="!state.noData">
        <el-pagination
          v-model:current-page="state.pagination.current"
          v-model:page-size="state.pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="state.pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 一对多/多对绑定选项卡 -->
    <el-tabs v-model="state.activeTab" class="mb-4">
      <el-tab-pane label="一对多" name="oneToMany"></el-tab-pane>
    </el-tabs>

    <!-- 绑定关系表格 -->
    <el-card class="mb-4" shadow="hover">
      <el-table :data="state.bindingData" border style="width: 100%">
        <el-table-column width="120">
          <template #header>
            <el-checkbox v-model="state.multiBindEq">多单绑定</el-checkbox>
          </template>
          <template #default="scope">
            <div v-if="scope.$index === 0">绑定设备</div>
            <div v-else>流道模组</div>
          </template>
        </el-table-column>
        <el-table-column label="条码" min-width="150">
          <template #default="scope">
            <el-input
              v-model="scope.row.barcode"
              placeholder="请输入"
              clearable
              @blur="handleBarcodeInput(scope.row, scope.$index)"
              @keyup.enter="handleBarcodeInput(scope.row, scope.$index)"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="模组MO" width="200">
          <template #default="scope">
            <el-input v-model="scope.row.moduleMO" placeholder="自动填充" disabled></el-input>
          </template>
        </el-table-column>
        <el-table-column label="模组名称">
          <template #default="scope">
            <el-input v-model="scope.row.moduleName" placeholder="自动填充" disabled></el-input>
          </template>
        </el-table-column>
        <el-table-column label="模组图号">
          <template #default="scope">
            <el-input v-model="scope.row.moduleNo" placeholder="自动填充" disabled></el-input>
          </template>
        </el-table-column>
        <el-table-column label="关联号" width="180">
          <template #default="scope">
            <el-input v-model="scope.row.relative" placeholder="自动填充" disabled></el-input>
          </template>
        </el-table-column>
        <el-table-column label="分类" width="80">
          <template #default="scope">
            <el-input v-model="scope.row.category" placeholder="自动填充" disabled></el-input>
          </template>
        </el-table-column>
        <el-table-column label="订单数" width="100">
          <template #default="scope">
            <el-input v-model="scope.row.orderNum" placeholder="自动填充" disabled></el-input>
          </template>
        </el-table-column>
        <el-table-column label="生产数" width="100">
          <template #default="scope">
            <el-input v-model="scope.row.productNum" placeholder="自动填充" disabled></el-input>
          </template>
        </el-table-column>
        <el-table-column width="120">
          <template #default="scope">
            <el-button type="primary" @click="scope.$index === 0 ? resetAll() : bindButton()">
              {{ scope.$index === 0 ? '清屏' : '确定' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 绑定信息表格 -->
      <el-card class="mb-4" shadow="hover">
        <el-table :data="state.bindingInfoData" border style="width: 100%; margin-top: 20px"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="id" label="ID"></el-table-column>
          <el-table-column prop="ldmzBarcode" label="流道模组条码" width="180"></el-table-column>
          <el-table-column prop="ldmzPname" label="流道模组工序名" width="180"></el-table-column>
          <el-table-column prop="ldmzMo" label="流道模组MO号" width="180"></el-table-column>
          <el-table-column prop="ldmzName" label="流道模组名称" width="180"></el-table-column>
          <el-table-column prop="ldmzDwg" label="流道模组图号" width="180"></el-table-column>
          <el-table-column prop="ldmzSopBatno" label="流道模组关联号" width="180"></el-table-column>
          <el-table-column prop="mzLjfl" label="流道模组类别" width="180"></el-table-column>
          <el-table-column prop="moBarcode" label="设备条码" width="180"></el-table-column>
          <el-table-column prop="moPname" label="设备工序名" width="180"></el-table-column>
          <el-table-column prop="moNo" label="设备MO号" width="180"></el-table-column>
          <el-table-column prop="mDesc" label="设备名称" width="180"></el-table-column>
          <el-table-column prop="dwgNo" label="设备图号" width="180"></el-table-column>
          <el-table-column prop="sopBatno" label="设备关联号" width="180"></el-table-column>
          <el-table-column prop="moLjfl" label="设备类别" width="180"></el-table-column>
          <el-table-column prop="oOrder" label="订单数"></el-table-column>
          <el-table-column prop="mOrder" label="生产数"></el-table-column>
          <el-table-column prop="rem" label="备注"></el-table-column>
          <el-table-column prop="host" label="主机"></el-table-column>
          <el-table-column prop="sysdt" label="操作时间" width="180"></el-table-column>
        </el-table>
      </el-card>

      <!-- 底部按钮 -->
      <div class="flex justify-start mt-4">
        <el-button type="danger" @click="handleDelete">删除</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { nextTick, onMounted, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getDict, getModuleBindings, queryBarcodeInfo, saveModuleBinding } from '@/api/maintain/index';
import { getInfos } from '@/api/information/sfcInfo/index';

// 响应式状态管理
const state = reactive({
  isLoading: false,
  searchType: 'moduleCode',
  searchKeyword: '',
  searchTypeOptions: [], // 添加搜索类型选项数组
  selectedBindings: [], // 新增：存储选中的绑定记录
  activeTab: 'oneToMany',
  deviceModuleData: [],
  pagination: {
    current: 1,
    size: 10,
    total: 0
  },
  bindingData: [
    {
      barcode: '',
      moduleMO: '',
      moduleName: '',
      moduleNo: '',
      relative: '',
      category: '',
      orderNum: '',
      productNum: '',
      proName: ''
    },
    {
      barcode: '',
      moduleMO: '',
      moduleName: '',
      moduleNo: '',
      relative: '',
      category: '',
      orderNum: '',
      productNum: '',
      proName: ''
    }
  ],
  bindingInfoData: [],
  noData: true,
  noBindingInfoData: true,
  multiBindEq: false,

  // 新增对话框相关的状态
  dialogVisible: false,
  dialogTitle: '扫描记录',
  reportTableData: [],
  reportLoading: false
});

// 处理页码变化
const handleCurrentChange = (current) => {
  state.pagination.current = current;
  searchBindingRecord();
};

// 处理每页条数变化
const handleSizeChange = (size) => {
  state.pagination.size = size;
  state.pagination.current = 1;
  searchBindingRecord();
};

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  state.selectedBindings = selection;
};

// 初始化函数
onMounted(() => {
  getSearchTypeOptions();
  // 可以在这里加载初始数据
});

// 获取搜索类型选项
const getSearchTypeOptions = async () => {
  try {
    const res = await getDict('model_bind');
    if (res?.data) {
      state.searchTypeOptions = res.data.map((item) => ({
        label: item.dictLabel,
        value: item.dictValue
      }));

      // 如果有数据且当前选中值不在选项中，则设置默认值
      if (state.searchTypeOptions.length > 0 && !state.searchTypeOptions.some((option) => option.value === state.searchType)) {
        state.searchType = state.searchTypeOptions[0].value;
      }
    }
  } catch (error) {
    ElMessage.error(`获取搜索类型选项失败: ${error.message || '未知错误'}`);
  }
};

// 处理双击表格单元格事件
const handleCellDblClick = async (row, column, cell, event) => {
  // 检查是否点击的是流道模组条码或设备条码列
  if (column.property === 'ldmzBarcode' || column.property === 'moBarcode') {
    try {
      state.reportLoading = true;
      state.dialogVisible = true;

      // 设置对话框标题
      const titleType = column.property === 'ldmzBarcode' ? '流道模组条码' : '设备条码';
      state.dialogTitle = `${titleType}扫描记录: ${row[column.property]}`;

      // 查询条码详细信息
      const param = { barCode: row[column.property] };
      const res = await getInfos(param);

      if (res?.data) {
        state.reportTableData = res.data;
      } else {
        state.reportTableData = [];
        ElMessage.warning('未找到相关扫描记录');
      }
    } catch (error) {
      ElMessage.error(`获取扫描记录失败: ${error.message || '未知错误'}`);
      state.reportTableData = [];
    } finally {
      state.reportLoading = false;
    }
  }
};

// 搜索绑定记录
const searchBindingRecord = async () => {
  if (!state.searchKeyword) {
    ElMessage.warning('请输入搜索关键词');
    return;
  }

  try {
    state.isLoading = true;
    // 构建分页参数
    const page = {
      page: state.pagination.current,
      size: state.pagination.size
    };
    // 构建查询参数，使用动态键值对
    const queryParam = {};
    queryParam[state.searchType] = state.searchKeyword;
    // 合并参数
    const param = { ...queryParam, ...page };
    const res = await getModuleBindings(param);
    // debugger
    // 直接使用返回的数据结构，不需要额外处理
    if (res?.data) {
      state.deviceModuleData = res.data.records || [];
      state.pagination.total = res.data.total || 0;
      state.pagination.current = res.data.current || 1;
      state.pagination.size = res.data.size || 10;
      state.noData = state.deviceModuleData.length === 0;

      if (state.deviceModuleData.length > 0) {
        ElMessage.success('查询成功');
      } else {
        ElMessage.warning('未找到匹配记录');
      }
    }
  } catch (error) {
    ElMessage.error(`查询失败: ${error.message || '未知错误'}`);
  } finally {
    state.isLoading = false;
  }
};

const handleBarcodeInput = async (row, index) => {
  if (!row.barcode) {
    // 只清空当前行数据，不再调用resetAll
    // row.moduleMO = ''
    // row.moduleName = ''
    // row.moduleNo = ''
    // row.relative = ''
    // row.category = ''
    // row.orderNum = ''
    // row.productNum = ''

    // 确保 state.bindingData 也被更新
    state.bindingData[index].moduleMO = '';
    state.bindingData[index].moduleName = '';
    state.bindingData[index].moduleNo = '';
    state.bindingData[index].relative = '';
    state.bindingData[index].category = '';
    state.bindingData[index].orderNum = '';
    state.bindingData[index].productNum = '';
    state.bindingData[index].proName = '';
    return;
  }

  try {
    const param = {
      barCode: row.barcode
    };
    const res = await queryBarcodeInfo(param);

    // 其余代码保持不变
    if (!res || res.code !== 200) {
      throw new Error(res?.msg || '查询失败');
    }

    if (!res.data) {
      ElMessage.warning('未找到相关信息');
      return;
    }

    if (res.data.notScan) {
      ElMessage.error('条码未扫描');
      return;
    }

    // 同时更新 row 和 state.bindingData[index]
    const moNo = res.data.moNo || '';
    const prdName = res.data.prdName || '';
    const proName = res.data.proName || '';
    const dwgNo = res.data.dwgNo || '';
    const sopBatno = res.data.sopBatno || '';
    const partType = res.data.partType || '';
    const moQty = res.data.moQty || '';
    const productionQty = res.data.productionQty || '';

    // 更新 row 对象
    // row.moduleMO = moNo
    // row.moduleName = prdName
    // row.moduleNo = dwgNo
    // row.relative = sopBatno
    // row.category = partType
    // row.orderNum = moQty
    // row.productNum = productionQty

    // 同时更新 state.bindingData
    state.bindingData[index].moduleMO = moNo;
    state.bindingData[index].moduleName = prdName;
    state.bindingData[index].moduleNo = dwgNo;
    state.bindingData[index].relative = sopBatno;
    state.bindingData[index].category = partType;
    state.bindingData[index].orderNum = moQty;
    state.bindingData[index].productNum = productionQty;
    state.bindingData[index].proName = proName;
  } catch (error) {
    ElMessage.error(error.message || '处理失败');
    // 清空当前行数据
    // row.moduleMO = ''
    // row.moduleName = ''
    // row.moduleNo = ''
    // row.relative = ''
    // row.category = ''
    // row.orderNum = ''
    // row.productNum = ''

    state.bindingData[index].moduleMO = '';
    state.bindingData[index].moduleName = '';
    state.bindingData[index].moduleNo = '';
    state.bindingData[index].relative = '';
    state.bindingData[index].category = '';
    state.bindingData[index].orderNum = '';
    state.bindingData[index].productNum = '';
    state.bindingData[index].proName = '';
  }
};

const bindButton = () => {
  // 获取第一行和第二行数据
  const deviceRow = state.bindingData[0];
  const moduleRow = state.bindingData[1];

  // 验证数据完整性
  if (!deviceRow.barcode || !moduleRow.barcode) {
    ElMessage.error('设备条码和流道模组条码不能为空');
    return;
  }

  // 检查是否已存在相同的绑定关系
  if (state.bindingInfoData.some((item) => item.moBarcode === deviceRow.barcode && item.ldmzBarcode === moduleRow.barcode)) {
    ElMessage.error('该绑定关系已存在');
    return;
  }

  // 生成新ID (找出当前最大ID并加1)
  const maxId = state.bindingInfoData.reduce((max, item) => (item.id > max ? item.id : max), 0);

  // 创建新的绑定记录
  const newBinding = {
    id: maxId + 1,
    // 流道模组信息
    ldmzBarcode: moduleRow.barcode,
    ldmzMo: moduleRow.moduleMO,
    ldmzName: moduleRow.moduleName,
    ldmzDwg: moduleRow.moduleNo,
    ldmzSopBatno: moduleRow.relative,
    mzLjfl: moduleRow.category,
    ldmzPname: moduleRow.proName,

    // 设备信息
    moBarcode: deviceRow.barcode,
    moNo: deviceRow.moduleMO,
    mDesc: deviceRow.moduleName,
    dwgNo: deviceRow.moduleNo,
    sopBatno: deviceRow.relative,
    moLjfl: deviceRow.category,
    moPname: deviceRow.proName,

    // 其他信息
    oOrder: moduleRow.orderNum,
    mOrder: moduleRow.productNum,
    rem: '',
    host: '',
    sysdt: new Date().toLocaleString()
  };

  // 添加到绑定信息表格
  state.bindingInfoData.push(newBinding);
  state.noBindingInfoData = false;

  // 根据多单绑定选项决定是否重置
  if (state.multiBindEq) {
    // 如果是多单绑定，只重置流道模组行
    // 保留第一行数据
    const deviceRow = JSON.parse(JSON.stringify(state.bindingData[0]));

    // 创建空的第二行
    const emptyModuleRow = {
      barcode: '',
      moduleMO: '',
      moduleName: '',
      moduleNo: '',
      relative: '',
      category: '',
      orderNum: '',
      productNum: '',
      proName: ''
    };

    // 只更新第二行，保留第一行
    state.bindingData = [deviceRow, emptyModuleRow];

    // 添加聚焦到流道模组输入框的逻辑
    nextTick(() => {
      // 获取第二行的条码输入框并聚焦
      const inputs = document.querySelectorAll('.el-table__row:nth-child(2) .el-input__inner');
      if (inputs && inputs.length > 0) {
        inputs[0].focus();
      }
    });
  } else {
    // 如果不是多单绑定，重置所有行
    resetAll();
  }

  ElMessage.success('绑定成功');
};

// 修改删除操作，使用选中的记录
const handleDelete = () => {
  if (state.selectedBindings.length === 0) {
    ElMessage.warning('请先选择要删除的绑定记录');
    return;
  }

  ElMessageBox.confirm(`确定要删除选中的 ${state.selectedBindings.length} 条绑定记录吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        // 实际调用API删除数据
        // 如果API支持批量删除，可以传入所有ID
        //const ids = state.selectedBindings.map(item => item.id);
        // await deleteModuleBinding(ids)

        // 从表格数据中移除已删除的记录
        state.bindingInfoData = state.bindingInfoData.filter((item) => !state.selectedBindings.some((selected) => selected.id === item.id));

        state.selectedBindings = [];
        state.noBindingInfoData = state.bindingInfoData.length === 0;
        ElMessage.success('删除成功');
      } catch (error) {
        ElMessage.error(`删除失败: ${error.message || '未知错误'}`);
      }
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 保存操作
const handleSave = async () => {
  if (state.bindingInfoData.length === 0) {
    ElMessage.warning('没有数据可保存');
    return;
  }

  try {
    // 实际调用API保存数据
    await saveModuleBinding(state.bindingInfoData).then(res => {
      if (res.code === 200) {
        resetAll();
        state.bindingInfoData = [];
      }
    });
    ElMessage.success('保存成功');
  } catch (error) {
    ElMessage.error(`保存失败: ${error.message || '未知错误'}`);
  }
};

// 确保resetAll方法正确实现
const resetAll = () => {
  // 直接创建全新的对象，避免引用问题
  const emptyRow = {
    barcode: '',
    moduleMO: '',
    moduleName: '',
    moduleNo: '',
    relative: '',
    category: '',
    orderNum: '',
    productNum: '',
    proName: ''
  };

  // 使用JSON深拷贝创建全新对象
  state.bindingData = [JSON.parse(JSON.stringify(emptyRow)), JSON.parse(JSON.stringify(emptyRow))];

  // 强制更新DOM
  nextTick(() => {
    // 确保视图已更新
    ElMessage.success('清空所有成功');
  });
};
</script>

<style scoped>
.p-4 {
  padding: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mx-2 {
  margin-left: 8px;
  margin-right: 8px;
}

.ml-2 {
  margin-left: 8px;
}

.mr-2 {
  margin-right: 8px;
}

.mt-4 {
  margin-top: 16px;
}

.py-4 {
  padding-top: 16px;
  padding-bottom: 16px;
}

.text-center {
  text-align: center;
}

.text-gray-500 {
  color: #6b7280;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-start {
  justify-content: flex-start;
}

.flex-1 {
  flex: 1 1 0%;
}

/* 表格样式 */
.el-table :deep(th) {
  background-color: #f5f7fa;
  color: #333;
  font-weight: bold;
  padding: 8px 0;
}

.el-table :deep(.el-table__row:hover) {
  background-color: #e0f7fa;
}

/* 卡片样式 */
.el-card {
  margin-bottom: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.el-button + .el-button {
  margin-left: 10px;
}
</style>
