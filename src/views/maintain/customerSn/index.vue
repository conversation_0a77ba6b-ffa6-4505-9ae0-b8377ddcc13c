<template>
  <div>
  <!-- 顶部搜索区域（使用动画过渡） -->
  <el-card v-if="showSearch" class="mb-4" shadow="hover">
    <el-form :model="queryParams" inline label-width="80px" @submit.prevent>
      <el-form-item label="MO号">
        <el-input v-model="queryParams.moNo" placeholder="请输入MO号" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button-group>
          <el-button type="primary" :icon="Search" @click="handleQuery">查询</el-button>
          <el-button :icon="Refresh" @click="resetQuery">重置</el-button>
        </el-button-group>
      </el-form-item>
    </el-form>
  </el-card>

  <!-- 主表格区域 -->
  <el-card shadow="hover">
    <!-- 表格组件（使用虚拟滚动优化渲染性能） -->
    <el-table
      v-loading="loading"
      :data="currentTableData"
      border
      stripe
      style="width: 100%"
      @selection-change="handleSelectionChange"
      :height="tableHeight"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column v-for="col in visibleColumns" :key="col.prop"
        :label="col.label"
        :prop="col.prop"
        :width="col.width"
        align="center"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="操作" width="180" align="center">
        <template #default="scope">
          <el-button link type="primary" @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
<!-- 分页组件 -->
  <el-pagination
        class="mt-4"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

    <!-- 列设置抽屉（来自参考实现<button class="citation-flag" data-index="8">） -->
    <el-drawer v-model="columnSettingsVisible" title="列设置" direction="rtl">
      <el-checkbox-group v-model="selectedColumns">
        <div v-for="col in allColumns" :key="col.prop" class="mb-2">
          <el-checkbox :label="col.prop">{{ col.label }}</el-checkbox>
        </div>
      </el-checkbox-group>
    </el-drawer>

    <!-- 数据操作弹窗 -->
        <!-- 数据操作弹窗 -->
        <el-dialog v-model="dialog.visible" :title="dialog.title" width="50%" append-to-body>
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="DR_NO" prop="drNo">
          <el-input v-model="formData.drNo" placeholder="请输入DR_NO" disabled/>
        </el-form-item>
        <el-form-item label="DR_ITM" prop="drItm">
          <el-input v-model="formData.drItm" placeholder="请输入DR_ITM" disabled/>
        </el-form-item>
        <el-form-item label="订单数量" prop="mqty">
          <el-input v-model="formData.mqty" placeholder="请输入订单数量" type="number" disabled/>
        </el-form-item>
        <el-form-item label="序号" prop="cnt2">
          <el-input v-model="formData.cnt2" placeholder="请输入序号" type="number" disabled/>
        </el-form-item>
        <el-form-item label="WD_SN号" prop="sn">
          <el-input v-model="formData.sn" placeholder="请输入WD_SN号" disabled/>
        </el-form-item>
        <el-form-item label="备注" prop="rem">
          <el-input v-model="formData.rem" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="客户SN号" prop="cusSn">
          <el-input v-model="formData.cusSn" placeholder="请输入客户SN号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </template>
    </el-dialog>
  </el-card>
</div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
// 若 @/store/modules/maintain 是正确路径则替换导入
import { useProductionStore } from '@/store/modules/maintain';
import { Refresh, Search } from '@element-plus/icons-vue';
import { getMolist, saveCusSn } from '@/api/maintain/index';

// 列配置（添加类型声明）
interface TableColumn {
  prop: string
  label: string
  width?: string | number
}

const allColumns = ref<TableColumn[]>([
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'drNo', label: 'DR_NO' },
  { prop: 'drItm', label: 'DR_ITM' },
  { prop: 'moNo', label: 'MO_NO' },
  { prop: 'mqty', label: '订单数量' },
  { prop: 'cnt2', label: '序号' },
  { prop: 'sn', label: 'WD_SN号' },
  { prop: 'rem', label: '备注' },
  { prop: 'cusSn', label: '客户SN号' }
])

// 弹窗状态管理
const dialog = ref({
  title: '',
  visible: false
})
const formRef = ref(null)
// 表单数据
const formData = ref({
  id: '',
  drNo: '',
  drItm: '',
  moNo: '',
  mqty: 0,
  cnt2: 0,
  sn: '',
  rem: '',
  cusSn: '',
  host: '',
  port: '',
  sysdt: ''
})

// 校验规则
const rules = ref({
  cusSn: [
    { required: true, message: '客户SN号不能为空', trigger: 'blur' }
  ]
})

// 分页总数
const total = ref(0)

// 动态列状态管理
const selectedColumns = ref(allColumns.value.map(col => col.prop))
const visibleColumns = computed(() => allColumns.value.filter(col => selectedColumns.value.includes(col.prop)))

// 表格高度自适应（来自最佳实践<button class="citation-flag" data-index="5">）
const tableHeight = computed(() => {
  return window.innerHeight - 300 + 'px'
})

// 其他状态变量...
const showSearch = ref(true)
const loading = ref(false)
const tableData = ref([])
const multipleSelection = ref([])
const columnSettingsVisible = ref(false)
// 新增本地分页数据
const allData = ref([]) // 存储全部数据
const currentTableData = computed(() => {
  const start = (queryParams.value.pageNum - 1) * queryParams.value.pageSize
  return allData.value.slice(start, start + queryParams.value.pageSize)
})

// 查询参数（使用Pinia持久化）
const store = useProductionStore()
const queryParams = computed({
  get: () => store.queryParams,
  set: (val) => store.setQueryParams(val)
})

const getList = async () => {
  loading.value = true
  try {
    const res = await getMolist(queryParams.value.moNo)
    if(res.data) {
      allData.value = res.data // 存储全部数据
      total.value = res.data.length // 总数设为实际数据量
    }
  } finally {
    loading.value = false
  }
}
const handleSizeChange = (val) => {
  queryParams.value.pageSize = val
}

const handleCurrentChange = (val) => {
  queryParams.value.pageNum = val
}

// 列设置交互（参考<button class="citation-flag" data-index="8">）
const toggleColumnSettings = () => {
  columnSettingsVisible.value = true
}

// 表格多选处理
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

// 查询相关方法
const handleQuery = () => {
  getList()
}

const resetQuery = () => {
  queryParams.value = {
    ...queryParams.value,
    moNo: undefined,
  }
  allData.value = [];
  // getList()
}

const handleEdit = (row) => {
  formData.value = { ...row }
  dialog.value = {
    title: '修改客户SN',
    visible: true
  }
}

// 弹窗操作
const cancel = () => {
  dialog.value.visible = false
  //formData.value = {}
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    await saveCusSn(formData.value)
    ElMessage.success('操作成功')
    getList()
    cancel()
  } catch (e) {
    console.error('表单验证失败或提交错误', e)
  }
}
</script>
@/store/modules/maintain
