<template>
  <div class="dashboard-container">
    <!-- 顶部欢迎信息 -->
    <div class="welcome-section">
      <div class="welcome-info">
        <div class="avatar-wrapper">
          <el-avatar :size="72" :src="state.avatar" />
          <div class="status-dot"></div>
        </div>
        <div class="user-info">
          <h2>欢迎回来, {{ state.user.userName }}</h2>
          <p class="user-desc">
            <el-tag size="small" type="info" effect="dark">{{ state.roleGroup }}</el-tag>
            <el-tag size="small" type="success" effect="dark">{{ state.user.deptName }}</el-tag>
            <el-tag size="small" type="warning" effect="dark">{{ state.postGroup }}</el-tag>
          </p>
        </div>
      </div>
      <div class="date-info">
        <div class="date">{{ currentDate }}</div>
        <div class="time">{{ currentTime }}</div>
      </div>
    </div>

    <!-- 数据概览卡片 -->
    <el-row :gutter="20" class="data-overview">
      <el-col :span="6" v-for="(item, index) in overviewData" :key="index">
        <el-card shadow="hover" class="overview-card" :body-style="{ padding: '20px' }">
          <div class="card-content">
            <div class="icon-wrapper" :style="{ background: `linear-gradient(135deg, ${item.color} 0%, ${item.color}80 100%)` }">
              <el-icon>
                <component :is="item.icon" />
              </el-icon>
            </div>
            <div class="info">
              <div class="title">{{ item.title }}</div>
              <div class="value">{{ item.value }}</div>
              <div class="trend">
                <span :class="['trend-value', item.trend > 0 ? 'up' : 'down']"> {{ item.trend > 0 ? '+' : '' }}{{ item.trend }}% </span>
                <span class="trend-label">较上周</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-section">
      <el-col :span="16">
        <el-card class="chart-card" :body-style="{ padding: '20px' }">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon>
                  <TrendCharts />
                </el-icon>
                <span>数据趋势</span>
              </div>
              <el-radio-group v-model="chartTimeRange" size="small">
                <el-radio-button value="week">本周</el-radio-button>
                <el-radio-button value="month">本月</el-radio-button>
                <el-radio-button value="year">全年</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div ref="chartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="pie-card" :body-style="{ padding: '20px' }">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon>
                  <PieChart />
                </el-icon>
                <span>数据分布</span>
              </div>
            </div>
          </template>
          <div ref="pieRef" class="pie-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 公告信息和待办事项 -->
    <el-row :gutter="20" class="notice-section">
      <el-col :span="16">
        <el-card class="notice-card" :body-style="{ padding: '20px' }">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon>
                  <Bell />
                </el-icon>
                <span>最新公告</span>
              </div>
              <el-button link type="primary">查看更多</el-button>
            </div>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="(notice, index) in noticeList"
              :key="index"
              :timestamp="notice.createTime"
              :type="notices[notice.noticeType]"
              class="timeline-item"
            >
              <div class="notice-content">
                <h4>{{ notice.noticeTitle }}</h4>
                <p>{{ notice.noticeContent }}</p>
                <div class="notice-footer">
                  <el-tag size="small" :type="notices[notice.noticeType]">{{ notice.noticeType }}</el-tag>
                  <span class="notice-time">{{ notice.createTime }}</span>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card class="todo-card" :body-style="{ padding: '20px' }">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon>
                  <List />
                </el-icon>
                <span>待办事项</span>
              </div>
              <el-button link type="primary">查看全部</el-button>
            </div>
          </template>
          <el-scrollbar height="400px">
            <div v-for="(todo, index) in todos" :key="index" class="todo-item">
              <el-checkbox v-model="todo.done" class="todo-checkbox">
                <span class="todo-content">{{ todo.content }}</span>
              </el-checkbox>
              <div class="todo-info">
                <el-tag size="small" :type="todo.priority">{{ todo.priorityText }}</el-tag>
                <span class="todo-time">{{ todo.time }}</span>
              </div>
            </div>
          </el-scrollbar>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import defAva from '@/assets/images/profile.jpeg';
import { UserVO } from '@/api/system/user/types';
import { getUserProfile } from '@/api/system/user';
import { getAuthList } from '@/api/system/social/auth';
import { getOnline } from '@/api/monitor/online';
import { listNotice } from '@/api/system/notice';
import { NoticeForm, NoticeQuery, NoticeVO } from '@/api/system/notice/types';
import * as echarts from 'echarts';
import { TrendCharts, PieChart } from '@element-plus/icons-vue';

const data = reactive<PageData<NoticeForm, NoticeQuery>>({
  queryParams: {
    pageNum: 1,
    pageSize: 10
  }
});

interface State {
  user: Partial<UserVO>;
  roleGroup: string;
  postGroup: string;
  auths: any;
  devices: any;
  avatar: any;
}
const state = ref<State>({
  user: {},
  avatar: '',
  roleGroup: '',
  postGroup: '',
  auths: [],
  devices: []
});

const { queryParams, form, rules } = toRefs(data);

const userForm = ref({});

const getUser = async () => {
  const res = await getUserProfile();
  state.value.user = res.data.user;
  userForm.value = { ...res.data.user };
  state.value.roleGroup = res.data.roleGroup;
  state.value.postGroup = res.data.postGroup;
  state.value.avatar == '' || res.data.user.avatar == null ? defAva : res.data.user.avatar;
};
const getAuths = async () => {
  const res = await getAuthList();
  state.value.auths = res.data;
};
const getOnlines = async () => {
  const res = await getOnline();
  state.value.devices = res.rows;
};
// 当前时间
const currentDate = ref('');
const currentTime = ref('');
let timer: NodeJS.Timer;

const updateTime = () => {
  const now = new Date();
  currentDate.value = now.toLocaleDateString();
  currentTime.value = now.toLocaleTimeString();
};

// 图表相关
const chartRef = ref();
const pieRef = ref();
let chart: echarts.ECharts | null = null;
let pieChart: echarts.ECharts | null = null;
const chartTimeRange = ref('week');

// 数据概览
const overviewData = ref([
  { title: '待处理工单', value: '12', icon: 'Document', color: '#409EFF', trend: 5.2 },
  { title: '进行中项目', value: '5', icon: 'Setting', color: '#67C23A', trend: -2.1 },
  { title: '待审批', value: '3', icon: 'Bell', color: '#E6A23C', trend: 8.5 },
  { title: '系统消息', value: '8', icon: 'User', color: '#F56C6C', trend: 3.7 }
]);
const noticeList = ref<NoticeVO[]>([]);
// 公告信息
const notices = ref(['warning', 'success', 'info']);
/** 查询公告列表 */
const getList = async () => {
  const res = await listNotice(queryParams.value);
  noticeList.value = res.rows;
};
// 待办事项
const todos = ref([
  { content: '完成项目周报', time: '今天 14:00', done: false, priority: 'danger', priorityText: '紧急' },
  { content: '审核代码提交', time: '今天 16:00', done: false, priority: 'warning', priorityText: '重要' },
  { content: '准备周会材料', time: '明天 09:00', done: false, priority: 'info', priorityText: '普通' }
]);

// 初始化图表
const initCharts = () => {
  // 折线图
  chart = echarts.init(chartRef.value);
  chart.setOption({
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '访问量',
        type: 'line',
        smooth: true,
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        areaStyle: {
          opacity: 0.1
        }
      }
    ]
  });

  // 饼图
  pieChart = echarts.init(pieRef.value);
  pieChart.setOption({
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '访问来源',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '搜索引擎' },
          { value: 735, name: '直接访问' },
          { value: 580, name: '邮件营销' },
          { value: 484, name: '联盟广告' },
          { value: 300, name: '视频广告' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  });
};

// 监听窗口大小变化
const handleResize = () => {
  chart?.resize();
  pieChart?.resize();
};

onMounted(() => {
  updateTime();
  timer = setInterval(updateTime, 1000);
  getUser();
  getAuths();
  getOnlines();
  getList();
  initCharts();
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  clearInterval(timer);
  window.removeEventListener('resize', handleResize);
  chart?.dispose();
  pieChart?.dispose();
});
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);
  min-height: 100vh;

  .welcome-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);

    .welcome-info {
      display: flex;
      align-items: center;
      gap: 24px;

      .avatar-wrapper {
        position: relative;

        .status-dot {
          position: absolute;
          bottom: 2px;
          right: 2px;
          width: 12px;
          height: 12px;
          background-color: #67c23a;
          border-radius: 50%;
          border: 2px solid #fff;
        }
      }

      .user-info {
        h2 {
          margin: 0;
          font-size: 28px;
          color: #303133;
          font-weight: 600;
          background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .user-desc {
          margin: 12px 0 0;
          display: flex;
          gap: 8px;
        }
      }
    }

    .date-info {
      text-align: right;

      .date {
        font-size: 16px;
        color: #606266;
        margin-bottom: 4px;
      }

      .time {
        font-size: 24px;
        color: #303133;
        font-weight: 500;
      }
    }
  }

  .data-overview {
    margin-bottom: 24px;

    .overview-card {
      transition: all 0.3s ease;
      border: none;
      background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }

      .card-content {
        display: flex;
        align-items: center;
        gap: 20px;

        .icon-wrapper {
          width: 56px;
          height: 56px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          .el-icon {
            font-size: 28px;
            color: #fff;
          }
        }

        .info {
          .title {
            color: #909399;
            font-size: 14px;
            margin-bottom: 8px;
          }
          .value {
            color: #303133;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 4px;
          }
          .trend {
            font-size: 12px;
            .trend-value {
              &.up {
                color: #67c23a;
              }
              &.down {
                color: #f56c6c;
              }
            }
            .trend-label {
              color: #909399;
              margin-left: 4px;
            }
          }
        }
      }
    }
  }

  .chart-section {
    margin-bottom: 24px;

    .chart-card,
    .pie-card {
      border: none;
      background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);

      .chart-container {
        height: 400px;
      }

      .pie-container {
        height: 300px;
      }
    }
  }

  .notice-section {
    .notice-card,
    .todo-card {
      border: none;
      background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 20px;
        border-bottom: 1px solid #ebeef5;

        .header-left {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 16px;
          font-weight: 500;
          color: #303133;

          .el-icon {
            font-size: 18px;
            color: #409eff;
          }
        }
      }
    }

    .timeline-item {
      .notice-content {
        h4 {
          margin: 0 0 8px;
          font-size: 15px;
          color: #303133;
        }
        p {
          margin: 0;
          font-size: 13px;
          color: #909399;
        }
        .notice-footer {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-top: 8px;
          .notice-time {
            color: #909399;
            font-size: 12px;
          }
        }
      }
    }

    .todo-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #ebeef5;

      &:last-child {
        border-bottom: none;
      }

      .todo-checkbox {
        .todo-content {
          font-size: 14px;
          color: #606266;
        }
      }

      .todo-info {
        display: flex;
        align-items: center;
        gap: 8px;
        .todo-time {
          color: #909399;
          font-size: 13px;
        }
      }
    }
  }
}
</style>
