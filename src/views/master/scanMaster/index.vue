<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="状态" prop="moState">
              <el-input v-model="queryParams.moState" placeholder="请输入状态" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户代码" prop="custNo">
              <el-input v-model="queryParams.custNo" placeholder="请输入客户代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目号" prop="pmNo">
              <el-input v-model="queryParams.pmNo" placeholder="请输入项目号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="PO号" prop="poNo">
              <el-input v-model="queryParams.poNo" placeholder="请输入客户PO号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="DR号" prop="drNo">
              <el-input v-model="queryParams.drNo" placeholder="请输入DR号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="MO号" prop="moNo">
              <el-input v-model="queryParams.moNo" placeholder="请输入MO号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="品号" prop="partId">
              <el-input v-model="queryParams.partId" placeholder="请输入品号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <!--      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['master:scanMaster:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['master:scanMaster:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['master:scanMaster:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['master:scanMaster:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>-->

      <el-table v-loading="loading" border :data="scanMasterList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="自增id" align="center" prop="id" v-if="true" />
        <!--        <el-table-column
          label="master表主键id"
          align="center"
          prop="mid"
        />-->
        <el-table-column label="状态" align="center" prop="moState" />
        <!--        <el-table-column label="备注" align="center" prop="moRem" />
        <el-table-column label="CUS_REM" align="center" prop="cusRem" />-->
        <el-table-column label="接单日期" align="center" prop="bargainDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.bargainDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="客户代码" align="center" prop="custNo" />
        <el-table-column label="项目号" width="160" :show-overflow-tooltip="true" align="center" prop="pmNo" />
        <el-table-column label="客户PO号" width="160" :show-overflow-tooltip="true" align="center" prop="poNo" />
        <el-table-column label="DR号" align="center" prop="drNo" />
        <!--        <el-table-column
          label="DR日期"
          align="center"
          prop="drDd"
          width="180"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.drDd, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>-->
        <el-table-column label="DR行号" align="center" prop="drItm" />
        <el-table-column label="客户要求交期" align="center" prop="deliveryDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.deliveryDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="承诺交期" align="center" prop="promiseDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.promiseDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="再次承诺" align="center" prop="secondDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.secondDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>-->
        <el-table-column label="工厂" align="center" prop="plant" />
        <!--        <el-table-column
          label="行业"
          align="center"
          prop="trade"
        />-->
        <el-table-column label="产品组" align="center" prop="productGroup" />
        <el-table-column label="订单类别" align="center" prop="orderType" />
        <el-table-column label="类别描述" show-overflow-tooltip align="center" prop="orderDescription" />
        <!--        <el-table-column
          label="海外特急"
          align="center"
          prop="extraUrgent"
        />
        <el-table-column
          label="SO号"
          align="center"
          prop="soNo"
        />
        <el-table-column
          label="SO行号"
          align="center"
          prop="soItm"
        />-->
        <el-table-column label="MO号" align="center" prop="moNo" />
        <el-table-column label="品号" width="160" :show-overflow-tooltip="true" align="center" prop="partId" />
        <el-table-column label="仓库" align="center" prop="moWh" />
        <el-table-column label="零件分类" align="center" prop="partType" />
        <el-table-column label="图号" width="160" :show-overflow-tooltip="true" align="center" prop="drawNumber" />
        <!--        <el-table-column
          label="版本"
          align="center"
          prop="partVer"
        />-->
        <el-table-column label="描述" width="160" :show-overflow-tooltip="true" align="center" prop="desc" />
        <el-table-column label="PO行号" align="center" prop="poItem" />
        <el-table-column label="客户料号" align="center" prop="custPartId" />
        <el-table-column label="订单数量" align="center" prop="orderQty" />
        <el-table-column label="生产数量" align="center" prop="productionQty" />
        <el-table-column label="报废数量" align="center" prop="scrapQty" />
        <el-table-column label="计划类型" align="center" prop="planType" />
        <el-table-column label="制造部门" align="center" prop="mfgDept" />
        <el-table-column label="生产车间" align="center" prop="workshop" />
        <el-table-column label="合单标识" align="center" prop="mergeMo" />
        <el-table-column label="打样标识" align="center" prop="proofMo" />
        <el-table-column label="下单员" align="center" prop="orderSalm" />
        <el-table-column label="下单员姓名" align="center" prop="orderName" />
        <el-table-column label="跟单负责人" align="center" prop="merchandiserCharge" />
        <el-table-column label="bom责任人" show-overflow-tooltip align="center" prop="bomCharge" />
        <el-table-column label="动作" align="center" prop="act" />
        <el-table-column label="当前工序" align="center" prop="currentProcess" />
        <el-table-column label="末工序时间" align="center" prop="lastOpTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.lastOpTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="工艺数量" align="center" prop="technologyQty" />
        <el-table-column label="pmc要求交期" align="center" prop="pmcReqDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.pmcReqDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="生产运营交期" align="center" prop="multipleOpDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.multipleOpDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="要求完成日期" align="center" prop="dueDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.dueDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="pmc要求外发日期" align="center" prop="pmcWfDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.pmcWfDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="外发要求交期" align="center" prop="wfRequestDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.wfRequestDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已入仓数" align="center" prop="inStorageQty" />
        <el-table-column label="欠入仓数" align="center" prop="outstandingInQty" />
        <el-table-column label="入仓日期" align="center" prop="inDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.inDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实际交货数量" align="center" prop="deliveryQty" />
        <el-table-column label="欠交货数量" align="center" prop="outstandingQty" />
        <el-table-column label="实际交期" align="center" prop="actualDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.actualDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="已领出数" align="center" prop="issueQty" />
        <el-table-column label="现有库存数" align="center" prop="stockQty" />
        <el-table-column label="上层订单号" align="center" prop="upMo" />
        <el-table-column label="状态1" align="center" prop="moState1" />
        <el-table-column label="急单" align="center" prop="urgent" />
        <el-table-column label="责任工程师" align="center" prop="dutyEngineer" />
        <el-table-column label="DN号" align="center" prop="dnNo" />
        <el-table-column label="成本中心" align="center" prop="cca" />
        <el-table-column label="多余件库存" align="center" prop="surplus" />
        <el-table-column label="利润中心" align="center" prop="profitCenter" />
        <el-table-column label="PMC备注" align="center" prop="pmcRem" />
        <el-table-column label="项目类型" align="center" prop="projectType" />
        <el-table-column label="批号" align="center" prop="batNo" />
        <el-table-column label="是否放行" align="center" prop="moPass" />
        <el-table-column label="跟单人" align="center" prop="merchandiser" />
        <el-table-column label="更新时间" align="center" prop="sysDt" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.sysDt, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="单位净重" align="center" prop="netWeight" />
        <el-table-column label="材料" align="center" prop="material" />
        <!--        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['master:scanMaster:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['master:scanMaster:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>-->
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改sapMaster信息对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="scanMasterFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="master表主键id" prop="mid">
          <el-input v-model="form.mid" placeholder="请输入master表主键id" />
        </el-form-item>
        <el-form-item label="状态" prop="moState">
          <el-input v-model="form.moState" placeholder="请输入状态" />
        </el-form-item>
        <!--        <el-form-item label="备注" prop="moRem">
          <el-input v-model="form.moRem" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="CUS_REM" prop="cusRem">
          <el-input v-model="form.cusRem" placeholder="请输入CUS_REM" />
        </el-form-item>-->
        <el-form-item label="接单日期" prop="bargainDate">
          <el-date-picker clearable v-model="form.bargainDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择接单日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="客户代码" prop="custNo">
          <el-input v-model="form.custNo" placeholder="请输入客户代码" />
        </el-form-item>
        <el-form-item label="项目号" prop="pmNo">
          <el-input v-model="form.pmNo" placeholder="请输入项目号" />
        </el-form-item>
        <el-form-item label="客户PO号" prop="poNo">
          <el-input v-model="form.poNo" placeholder="请输入客户PO号" />
        </el-form-item>
        <el-form-item label="DR号" prop="drNo">
          <el-input v-model="form.drNo" placeholder="请输入DR号" />
        </el-form-item>
        <el-form-item label="DR日期" prop="drDd">
          <el-date-picker clearable v-model="form.drDd" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择DR日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="DR行号" prop="drItm">
          <el-input v-model="form.drItm" placeholder="请输入DR行号" />
        </el-form-item>

        <el-form-item label="工厂" prop="plant">
          <el-input v-model="form.plant" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="行业" prop="trade">
          <el-input v-model="form.trade" placeholder="请输入行业" />
        </el-form-item>
        <el-form-item label="产品组" prop="productGroup">
          <el-input v-model="form.productGroup" placeholder="请输入产品组" />
        </el-form-item>
        <el-form-item label="类别描述" prop="orderDescription">
          <el-input v-model="form.orderDescription" placeholder="请输入类别描述" />
        </el-form-item>
        <el-form-item label="海外特急" prop="extraUrgent">
          <el-input v-model="form.extraUrgent" placeholder="请输入海外特急" />
        </el-form-item>
        <el-form-item label="SO号" prop="soNo">
          <el-input v-model="form.soNo" placeholder="请输入SO号" />
        </el-form-item>
        <el-form-item label="SO行号" prop="soItm">
          <el-input v-model="form.soItm" placeholder="请输入SO行号" />
        </el-form-item>
        <el-form-item label="MO号" prop="moNo">
          <el-input v-model="form.moNo" placeholder="请输入MO号" />
        </el-form-item>
        <el-form-item label="品号" prop="partId">
          <el-input v-model="form.partId" placeholder="请输入品号" />
        </el-form-item>
        <el-form-item label="仓库" prop="moWh">
          <el-input v-model="form.moWh" placeholder="请输入仓库" />
        </el-form-item>
        <el-form-item label="图号" prop="drawNumber">
          <el-input v-model="form.drawNumber" placeholder="请输入图号" />
        </el-form-item>
        <el-form-item label="版本" prop="partVer">
          <el-input v-model="form.partVer" placeholder="请输入版本" />
        </el-form-item>
        <el-form-item label="描述" prop="desc">
          <el-input v-model="form.desc" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="PO行号" prop="poItem">
          <el-input v-model="form.poItem" placeholder="请输入PO行号" />
        </el-form-item>
        <el-form-item label="客户料号" prop="custPartId">
          <el-input v-model="form.custPartId" placeholder="请输入客户料号" />
        </el-form-item>
        <el-form-item label="订单数量" prop="orderQty">
          <el-input v-model="form.orderQty" placeholder="请输入订单数量" />
        </el-form-item>
        <el-form-item label="生产数量" prop="productionQty">
          <el-input v-model="form.productionQty" placeholder="请输入生产数量" />
        </el-form-item>
        <el-form-item label="报废数量" prop="scrapQty">
          <el-input v-model="form.scrapQty" placeholder="请输入报废数量" />
        </el-form-item>
        <el-form-item label="制造部门" prop="mfgDept">
          <el-input v-model="form.mfgDept" placeholder="请输入制造部门" />
        </el-form-item>
        <el-form-item label="生产车间" prop="workshop">
          <el-input v-model="form.workshop" placeholder="请输入生产车间" />
        </el-form-item>
        <el-form-item label="合单标识" prop="mergeMo">
          <el-input v-model="form.mergeMo" placeholder="请输入合单标识" />
        </el-form-item>
        <el-form-item label="打样标识" prop="proofMo">
          <el-input v-model="form.proofMo" placeholder="请输入打样标识" />
        </el-form-item>
        <el-form-item label="下单员" prop="orderSalm">
          <el-input v-model="form.orderSalm" placeholder="请输入下单员" />
        </el-form-item>
        <el-form-item label="下单员姓名" prop="orderName">
          <el-input v-model="form.orderName" placeholder="请输入下单员姓名" />
        </el-form-item>
        <el-form-item label="跟单负责人" prop="merchandiserCharge">
          <el-input v-model="form.merchandiserCharge" placeholder="请输入跟单负责人" />
        </el-form-item>
        <el-form-item label="bom责任人" prop="bomCharge">
          <el-input v-model="form.bomCharge" placeholder="请输入bom责任人" />
        </el-form-item>
        <el-form-item label="动作" prop="act">
          <el-input v-model="form.act" placeholder="请输入动作" />
        </el-form-item>
        <el-form-item label="当前工序" prop="currentProcess">
          <el-input v-model="form.currentProcess" placeholder="请输入当前工序" />
        </el-form-item>
        <el-form-item label="末工序时间" prop="lastOpTime">
          <el-date-picker clearable v-model="form.lastOpTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择末工序时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="工艺数量" prop="technologyQty">
          <el-input v-model="form.technologyQty" placeholder="请输入工艺数量" />
        </el-form-item>
        <el-form-item label="pmc要求交期" prop="pmcReqDate">
          <el-date-picker clearable v-model="form.pmcReqDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择pmc要求交期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="生产运营交期" prop="multipleOpDate">
          <el-date-picker clearable v-model="form.multipleOpDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择生产运营交期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="要求完成日期" prop="dueDate">
          <el-date-picker clearable v-model="form.dueDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择要求完成日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="pmc要求外发日期" prop="pmcWfDate">
          <el-date-picker clearable v-model="form.pmcWfDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择pmc要求外发日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="外发要求交期" prop="wfRequestDate">
          <el-date-picker clearable v-model="form.wfRequestDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择外发要求交期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="已入仓数" prop="inStorageQty">
          <el-input v-model="form.inStorageQty" placeholder="请输入已入仓数" />
        </el-form-item>
        <el-form-item label="欠入仓数" prop="outstandingInQty">
          <el-input v-model="form.outstandingInQty" placeholder="请输入欠入仓数" />
        </el-form-item>
        <el-form-item label="入仓日期" prop="inDate">
          <el-date-picker clearable v-model="form.inDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择入仓日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="实际交货数量" prop="deliveryQty">
          <el-input v-model="form.deliveryQty" placeholder="请输入实际交货数量" />
        </el-form-item>
        <el-form-item label="欠交货数量" prop="outstandingQty">
          <el-input v-model="form.outstandingQty" placeholder="请输入欠交货数量" />
        </el-form-item>
        <el-form-item label="实际交期" prop="actualDate">
          <el-date-picker clearable v-model="form.actualDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择实际交期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="已领出数" prop="issueQty">
          <el-input v-model="form.issueQty" placeholder="请输入已领出数" />
        </el-form-item>
        <el-form-item label="现有库存数" prop="stockQty">
          <el-input v-model="form.stockQty" placeholder="请输入现有库存数" />
        </el-form-item>
        <el-form-item label="上层订单号" prop="upMo">
          <el-input v-model="form.upMo" placeholder="请输入上层订单号" />
        </el-form-item>
        <el-form-item label="状态1" prop="moState1">
          <el-input v-model="form.moState1" placeholder="请输入状态1" />
        </el-form-item>
        <el-form-item label="急单" prop="urgent">
          <el-input v-model="form.urgent" placeholder="请输入急单" />
        </el-form-item>
        <el-form-item label="责任工程师" prop="dutyEngineer">
          <el-input v-model="form.dutyEngineer" placeholder="请输入责任工程师" />
        </el-form-item>
        <el-form-item label="DN号" prop="dnNo">
          <el-input v-model="form.dnNo" placeholder="请输入DN号" />
        </el-form-item>
        <el-form-item label="成本中心" prop="cca">
          <el-input v-model="form.cca" placeholder="请输入成本中心" />
        </el-form-item>
        <el-form-item label="多余件库存" prop="surplus">
          <el-input v-model="form.surplus" placeholder="请输入多余件库存" />
        </el-form-item>
        <el-form-item label="利润中心" prop="profitCenter">
          <el-input v-model="form.profitCenter" placeholder="请输入利润中心" />
        </el-form-item>
        <el-form-item label="PMC备注" prop="pmcRem">
          <el-input v-model="form.pmcRem" placeholder="请输入PMC备注" />
        </el-form-item>
        <el-form-item label="批号" prop="batNo">
          <el-input v-model="form.batNo" placeholder="请输入批号" />
        </el-form-item>
        <el-form-item label="是否放行" prop="moPass">
          <el-input v-model="form.moPass" placeholder="请输入是否放行" />
        </el-form-item>
        <el-form-item label="跟单人" prop="merchandiser">
          <el-input v-model="form.merchandiser" placeholder="请输入跟单人" />
        </el-form-item>
        <el-form-item label="更新时间" prop="sysDt">
          <el-date-picker clearable v-model="form.sysDt" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择更新时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="单位净重" prop="netWeight">
          <el-input v-model="form.netWeight" placeholder="请输入单位净重" />
        </el-form-item>
        <el-form-item label="材料" prop="material">
          <el-input v-model="form.material" placeholder="请输入材料" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ScanMaster" lang="ts">
import { addScanMaster, delScanMaster, getScanMaster, listScanMaster, updateScanMaster } from '@/api/master/scanMaster';
import { ScanMasterForm, ScanMasterQuery, ScanMasterVO } from '@/api/master/scanMaster/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const scanMasterList = ref<ScanMasterVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const scanMasterFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ScanMasterForm = {
  id: undefined,
  mid: undefined,
  moState: undefined,
  moRem: undefined,
  cusRem: undefined,
  bargainDate: undefined,
  custNo: undefined,
  pmNo: undefined,
  poNo: undefined,
  drNo: undefined,
  drDd: undefined,
  drItm: undefined,
  deliveryDate: undefined,
  promiseDate: undefined,
  secondDate: undefined,
  plant: undefined,
  trade: undefined,
  productGroup: undefined,
  orderType: undefined,
  orderDescription: undefined,
  extraUrgent: undefined,
  soNo: undefined,
  soItm: undefined,
  moNo: undefined,
  partId: undefined,
  moWh: undefined,
  partType: undefined,
  drawNumber: undefined,
  partVer: undefined,
  desc: undefined,
  poItem: undefined,
  custPartId: undefined,
  orderQty: undefined,
  productionQty: undefined,
  scrapQty: undefined,
  planType: undefined,
  mfgDept: undefined,
  workshop: undefined,
  mergeMo: undefined,
  proofMo: undefined,
  orderSalm: undefined,
  orderName: undefined,
  merchandiserCharge: undefined,
  bomCharge: undefined,
  act: undefined,
  currentProcess: undefined,
  lastOpTime: undefined,
  technologyQty: undefined,
  multipleOpDate: undefined,
  dueDate: undefined,
  pmcWfDate: undefined,
  wfRequestDate: undefined,
  inStorageQty: undefined,
  outstandingInQty: undefined,
  inDate: undefined,
  deliveryQty: undefined,
  outstandingQty: undefined,
  actualDate: undefined,
  issueQty: undefined,
  stockQty: undefined,
  upMo: undefined,
  moState1: undefined,
  urgent: undefined,
  dutyEngineer: undefined,
  dnNo: undefined,
  cca: undefined,
  surplus: undefined,
  profitCenter: undefined,
  pmcRem: undefined,
  projectType: undefined,
  batNo: undefined,
  moPass: undefined,
  merchandiser: undefined,
  sysDt: undefined,
  netWeight: undefined,
  material: undefined
};
const data = reactive<PageData<ScanMasterForm, ScanMasterQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    moState: undefined,
    moRem: undefined,
    custNo: undefined,
    pmNo: undefined,
    poNo: undefined,
    drNo: undefined,
    moNo: undefined,
    partId: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '自增id不能为空', trigger: 'blur' }],
    mid: [{ required: true, message: 'master表主键id不能为空', trigger: 'blur' }],
    moState: [{ required: true, message: '状态不能为空', trigger: 'blur' }],
    moRem: [{ required: true, message: '备注不能为空', trigger: 'blur' }],
    cusRem: [{ required: true, message: 'CUS_REM不能为空', trigger: 'blur' }],
    bargainDate: [{ required: true, message: '接单日期不能为空', trigger: 'blur' }],
    custNo: [{ required: true, message: '客户代码不能为空', trigger: 'blur' }],
    pmNo: [{ required: true, message: '项目号不能为空', trigger: 'blur' }],
    poNo: [{ required: true, message: '客户PO号不能为空', trigger: 'blur' }],
    drNo: [{ required: true, message: 'DR号不能为空', trigger: 'blur' }],
    drDd: [{ required: true, message: 'DR日期不能为空', trigger: 'blur' }],
    drItm: [{ required: true, message: 'DR行号不能为空', trigger: 'blur' }],
    deliveryDate: [{ required: true, message: '客户要求交期不能为空', trigger: 'blur' }],
    promiseDate: [{ required: true, message: '承诺交期不能为空', trigger: 'blur' }],
    secondDate: [{ required: true, message: '再次承诺不能为空', trigger: 'blur' }],
    plant: [{ required: true, message: '工厂不能为空', trigger: 'blur' }],
    trade: [{ required: true, message: '行业不能为空', trigger: 'blur' }],
    productGroup: [{ required: true, message: '产品组不能为空', trigger: 'blur' }],
    orderType: [{ required: true, message: '订单类别不能为空', trigger: 'change' }],
    orderDescription: [{ required: true, message: '类别描述不能为空', trigger: 'blur' }],
    extraUrgent: [{ required: true, message: '海外特急不能为空', trigger: 'blur' }],
    soNo: [{ required: true, message: 'SO号不能为空', trigger: 'blur' }],
    soItm: [{ required: true, message: 'SO行号不能为空', trigger: 'blur' }],
    moNo: [{ required: true, message: 'MO号不能为空', trigger: 'blur' }],
    partId: [{ required: true, message: '品号不能为空', trigger: 'blur' }],
    moWh: [{ required: true, message: '仓库不能为空', trigger: 'blur' }],
    partType: [{ required: true, message: '零件分类不能为空', trigger: 'change' }],
    drawNumber: [{ required: true, message: '图号不能为空', trigger: 'blur' }],
    partVer: [{ required: true, message: '版本不能为空', trigger: 'blur' }],
    desc: [{ required: true, message: '描述不能为空', trigger: 'blur' }],
    poItem: [{ required: true, message: 'PO行号不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询sapMaster信息列表 */
const getList = async () => {
  loading.value = true;
  const res = await listScanMaster(queryParams.value);
  scanMasterList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  scanMasterFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ScanMasterVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加sapMaster信息';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ScanMasterVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getScanMaster(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改sapMaster信息';
};

/** 提交按钮 */
const submitForm = () => {
  scanMasterFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateScanMaster(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addScanMaster(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ScanMasterVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除sapMaster信息编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delScanMaster(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'sfc/master/scanMaster/export',
    {
      ...queryParams.value
    },
    `scanMaster_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.p-2 {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: 100vh;

  // 搜索区域样式优化
  .search-section {
    margin-bottom: 16px;

    .el-card {
      .el-form {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 16px;
        padding: 16px;

        .el-form-item {
          margin-bottom: 0;

          :deep(.el-form-item__label) {
            font-weight: 500;
            color: #606266;
          }

          :deep(.el-input) {
            .el-input__wrapper {
              box-shadow: 0 0 0 1px #dcdfe6 inset;

              &:hover {
                box-shadow: 0 0 0 1px #c0c4cc inset;
              }

              &.is-focus {
                box-shadow: 0 0 0 1px #409eff inset;
              }
            }
          }
        }

        // 按钮组样式
        .el-form-item:last-child {
          grid-column: 1 / -1;
          margin-top: 8px;
          display: flex;
          justify-content: center;
          gap: 12px;

          .el-button {
            min-width: 80px;

            &:hover {
              opacity: 0.9;
            }
          }
        }
      }
    }
  }

  // 表格区域样式优化
  .el-card {
    margin-bottom: 16px;

    :deep(.el-card__body) {
      padding: 0;
    }

    .el-table {
      // 表头样式
      :deep(.el-table__header) {
        th {
          background-color: #f5f7fa;
          color: #606266;
          font-weight: 600;
          height: 45px;
          padding: 8px;
          border-bottom: 1px solid #ebeef5;
        }
      }

      // 表格内容样式
      :deep(.el-table__body) {
        td {
          padding: 8px;
          height: 45px;

          .cell {
            line-height: 1.5;
          }
        }

        // 斑马纹样式
        tr:nth-child(even) {
          background-color: #fafafa;
        }

        // 鼠标悬停效果
        tr:hover td {
          background-color: #f5f7fa;
        }
      }
    }

    // 分页器样式
    .pagination {
      padding: 16px;
      display: flex;
      justify-content: flex-end;
      background-color: #fff;
      border-top: 1px solid #ebeef5;
    }
  }

  // 弹窗样式优化
  :deep(.el-dialog) {
    border-radius: 8px;
    overflow: hidden;

    .el-dialog__header {
      margin: 0;
      padding: 16px 20px;
      border-bottom: 1px solid #ebeef5;
      background-color: #f5f7fa;

      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .el-dialog__body {
      padding: 20px;

      .el-form {
        .el-form-item {
          margin-bottom: 18px;

          .el-form-item__label {
            font-weight: 500;
          }

          .el-input,
          .el-select,
          .el-date-picker {
            width: 100%;
          }
        }
      }
    }

    .el-dialog__footer {
      padding: 16px 20px;
      border-top: 1px solid #ebeef5;

      .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;

        .el-button {
          min-width: 80px;
        }
      }
    }
  }
}

// 全局覆盖样式
:deep(.el-card) {
  border-radius: 8px;
  border: none;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-button) {
  border-radius: 4px;
  font-weight: 500;

  &:focus-visible {
    outline: none;
  }
}

// 添加版权信息
.copyright {
  text-align: center;
  padding: 16px;
  color: #909399;
  font-size: 12px;
  border-top: 1px solid #ebeef5;
}
</style>
