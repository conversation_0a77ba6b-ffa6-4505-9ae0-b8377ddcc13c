<template>
  <div>
    <el-dialog
      v-model="firstVisible"
      title="装配楼层"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      @close="modalClose"
      style="width: 500px"
    >
      <template #footer>
        <el-button @click="modalCancel">取消</el-button>
        <el-button type="primary" @click="handleOk">确定</el-button>
      </template>
      <el-select v-model="floorValue" style="width: 320px" placeholder="请选择" @visible-change="loadFloorData">
        <el-option v-for="item in floorData" :key="item.acnCode" :value="item.id" :label="item.acnDesc" />
      </el-select>
    </el-dialog>
    <el-card>
      <el-space direction="vertical" alignment="flex-start">
        <el-space style="position: relative; left: 64px">
          <el-select ref="typeSelect" v-model="zpType" style="width: 195px" placeholder="请选择类型" @change="clearAll">
            <el-option v-for="item of zpData" :key="item.acnDesc" :value="item.acnDesc" :label="item.acnDesc" />
          </el-select>
          <el-text style="position: relative; left: 23px">装配楼层</el-text>
          <el-input v-model="floorDesc" style="position: relative; left: 24px; width: 200px" disabled />
        </el-space>
        <el-space>
          <el-text>操作工号</el-text>
          <el-input ref="empInput" v-model="salNo" style="width: 100px" placeholder="" clearable @keyup.enter="enterSalNo" />
          <el-input v-model="empName" style="width: 100px" placeholder="" clearable />
          <el-input v-model="dptName" style="width: 200px" placeholder="" clearable />
        </el-space>
        <el-space>
          <el-text>扫描条码</el-text>
          <el-input ref="barcodeInput" v-model="barcode" style="width: 416px" clearable @keyup.enter="enterBarcode" @clear="resetBarcode" />
        </el-space>
        <el-space>
          <el-text>MO号</el-text>
          <el-input v-model="mo" style="width: 140px; position: relative; left: 18px" clearable />
          <el-text style="position: relative; left: 13px">订单数量</el-text>
          <el-input ref="moNumInput" v-model="moNum" style="width: 150px; position: relative; left: 11px" clearable @blur="changeValue"/>
          <el-text style="position: relative; left: 11px">参与人员</el-text>
          <el-input v-model="people" style="width: 100px; position: relative; left: 11px" clearable @keyup.enter="enterPeople(people, $event)" />
          <el-text style="position: relative; left: 11px">产品名称</el-text>
          <el-input v-model="prdName" style="width: 258px; position: relative; left: 11px" placeholder="" clearable />
          <el-text style="position: relative; left: 11px">工站图号</el-text>
          <el-input v-model="dwgNo" style="width: 200px; position: relative; left: 11px" placeholder="" clearable />
          <el-text style="position: relative; left: 11px">工站名称</el-text>
          <el-input v-model="gsName" style="width: 200px; position: relative; left: 11px" clearable />
        </el-space>
        <el-space>
          <el-text>良品数量</el-text>
          <el-input v-model="okNum" style="width: 90px; position: relative; left: 0px" clearable @blur="changeValue" />
          <el-text style="position: relative; left: 10px">备注/不良原因</el-text>
          <el-select
            v-model="ngValue"
            style="width: 150px; position: relative; left: 10px"
            value-key="acnCode"
            @visible-change="loadNgData"
            @change="changeValue"
          >
            <el-option v-for="item in ngData" :key="item.acnCode" :value="item" :label="item.acnDesc" />
          </el-select>
          <el-text style="position: relative; left: 10px">不良数量</el-text>
          <el-input v-model="ngNum" style="width: 100px; position: relative; left: 10px" clearable @blur="changeValue('ng')" @clear="clearNg" />
          <el-text style="position: relative; left: 10px">当前进度</el-text>
          <el-input
            ref="curProInput"
            v-model="progressNow"
            style="width: 100px; position: relative; left: 10px"
            clearable
            @blur="changeValue('progress')"
          />
          <el-text style="position: relative; left: 10px">总进度</el-text>
          <el-input v-model="progressSum" disabled style="width: 100px; position: relative; left: 10px" />
          <el-text style="position: relative; left: 10px">标准工时</el-text>
          <el-input v-model="basGs" disabled style="width: 100px; position: relative; left: 10px" clearable />
          <el-text style="position: relative; left: 10px">已用工时</el-text>
          <el-input v-model="sumGs" disabled style="width: 100px; position: relative; left: 10px" clearable />
          <el-text style="position: relative; left: 10px">剩余工时</el-text>
          <el-input v-model="syGs" disabled style="width: 128px; position: relative; left: 10px" clearable />
        </el-space>
      </el-space>
      <el-divider></el-divider>
      <el-table :data="infoTableDataExist" border size="small" :row-key="getRowKey" @row-dblclick="dbClick">
        <el-table-column prop="cusName" label="产品名"></el-table-column>
        <el-table-column prop="moNo" label="MO号"></el-table-column>
        <el-table-column prop="wksDwg" label="图号"></el-table-column>
        <el-table-column prop="empNo" label="工号"></el-table-column>
        <el-table-column prop="empName" label="姓名"></el-table-column>
        <el-table-column prop="pType" label="工站号"></el-table-column>
        <el-table-column prop="pName" label="工站名称"></el-table-column>
        <el-table-column prop="basGs" label="标准工时"></el-table-column>
        <el-table-column prop="bpRy" label="标配人力"></el-table-column>
        <el-table-column prop="barCode" label="条码"></el-table-column>
        <el-table-column prop="bDtm" label="开始时间"></el-table-column>
        <el-table-column prop="eDtm" label="结束时间"></el-table-column>
        <el-table-column prop="sjGs" label="实际工时"></el-table-column>
        <el-table-column prop="qtyOk" label="良品数量"></el-table-column>
        <el-table-column prop="qtyNg" label="不良数量"></el-table-column>
        <el-table-column prop="reasonName" label="不良原因"></el-table-column>
        <el-table-column prop="curProd" label="当前进度"></el-table-column>
        <el-table-column prop="sumProd" label="总进度"></el-table-column>
        <el-table-column prop="mSta" label="状态"></el-table-column>
        <el-table-column prop="rem" label="备注"></el-table-column>
        <el-table-column prop="zpType" label="装配类型"></el-table-column>
        <el-table-column prop="arbpl" label="ARBPL"></el-table-column>
        <el-table-column prop="wkGs" label="累计工时"></el-table-column>
      </el-table>
      <el-divider></el-divider>
      <el-table :data="infoTableData" border :row-key="getRowKey" size="small" @selection-change="handleRowSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="cusName" label="产品名"></el-table-column>
        <el-table-column prop="moNo" label="MO号"></el-table-column>
        <el-table-column prop="wksDwg" label="图号"></el-table-column>
        <el-table-column prop="empNo" label="工号"></el-table-column>
        <el-table-column prop="empName" label="姓名"></el-table-column>
        <el-table-column prop="pType" label="工站号"></el-table-column>
        <el-table-column prop="pName" label="工站名称"></el-table-column>
        <el-table-column prop="basGs" label="标准工时"></el-table-column>
        <el-table-column prop="bpRy" label="标配人力"></el-table-column>
        <el-table-column prop="barCode" label="条码"></el-table-column>
        <el-table-column prop="bDtm" label="开始时间"></el-table-column>
        <el-table-column prop="eDtm" label="结束时间"></el-table-column>
        <el-table-column prop="sjGs" label="实际工时"></el-table-column>
        <el-table-column prop="qtyOk" label="良品数量"></el-table-column>
        <el-table-column prop="qtyNg" label="不良数量"></el-table-column>
        <el-table-column prop="reasonName" label="不良原因"></el-table-column>
        <el-table-column prop="curProd" label="当前进度"></el-table-column>
        <el-table-column prop="sumProd" label="总进度"></el-table-column>
        <el-table-column prop="mSta" label="状态"></el-table-column>
        <el-table-column prop="rem" label="备注"></el-table-column>
        <el-table-column prop="zpType" label="装配类型"></el-table-column>
        <el-table-column prop="arbpl" label="ARBPL"></el-table-column>
        <el-table-column prop="wkGs" label="累计工时"></el-table-column>
      </el-table>
      <el-space class="button1">
        <el-button type="primary" @click="deleteRecord">删除</el-button>
        <el-button type="primary" :loading="loading" @click="saveRecord">保存</el-button>
      </el-space>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { getCodeRuleList } from '@/api/information/codeRule';
import { CodeRuleModel } from '@/api/information/codeRule/types';
import { FileInfoModel } from '@/api/information/fileList/types';
import { SfcInfoModel } from '@/api/information/sfcInfo/types';
import { useTagsViewStore } from '@/store/modules/tagsView';
import { useRoute, useRouter } from 'vue-router';
import { getSalmInfo } from '@/api/salm';
import { getInfos, saveInfos } from '@/api/information/sfcInfo';
import { queryBarcodeInfo } from '@/api/barcode/equipBarcode';
import { onMounted, ref } from 'vue';
import { ElMessage } from 'element-plus';

const tagsViewStore = useTagsViewStore();
const route = useRoute();
const router = useRouter();

const barcodeInput = ref<HTMLInputElement | null>(null);
const empInput = ref<HTMLInputElement | null>(null);
const moNumInput = ref<HTMLInputElement | null>(null);
const floorValue = ref('');
const ngValue = ref<CodeRuleModel>();
const zpData = ref<CodeRuleModel[]>([]);
const floorData = ref<CodeRuleModel[]>([]);
const ngData = ref<CodeRuleModel[]>([]);
const floorDesc = ref('');
const zpType = ref('');
const mo = ref('');
const sopBatno = ref('');
const salNo = ref('');
const empName = ref('');
const dptName = ref('');
const prdName = ref('');
const prdGroup = ref('');
const people = ref('');
const moNum = ref('');
const okNum = ref('');
const ngNum = ref('');
const dwgNo = ref('');
const gsNo = ref('');
const gsName = ref('');
const gzvs = ref('');
const basGs = ref('');
const bpRy = ref('');
const sumGs = ref('');
const syGs = ref('');
const barcode = ref('');
const progressSum = ref('');
const progressNow = ref('');
const loading = ref(false);
const firstVisible = ref(true);
const curProInput = ref<HTMLInputElement | null>(null);
const typeSelect = ref<HTMLSelectElement | null>(null);
const recordId = ref();

// 存储选中的行
const selectedRows = ref([]);

const tableData = ref<FileInfoModel[]>([]);
const infoTableData = ref<SfcInfoModel[]>([]);
const infoTableDataExist = ref<SfcInfoModel[]>([]);
const infoTableDataTemp = ref({
  zpType,
  floorNo: floorDesc,
  cusName: prdName,
  moNo: mo,
  sopBatno,
  wksDwg: dwgNo,
  pType: gsNo,
  pName: gsName,
  empNo: salNo,
  empName,
  bDtm: '',
  eDtm: '',
  sjGs: '',
  gyGs: '',
  qtyOk: okNum,
  qtyNg: ngNum,
  reasonCode: ngValue.value?.acnCode,
  reasonName: ngValue.value?.acnDesc,
  sumProd: progressSum,
  curProd: progressNow,
  barCode: barcode,
  flag: '',
  wkGs: sumGs,
  basGs,
  bpRy,
  dep: dptName,
  arbpl: gzvs,
  prdGroup,
  oQty: moNum
});

const loadFloorData = async (visible) => {
  if (visible) {
    await getCodeRuleList('FLOR')
      .then((res) => {
        floorData.value = res.data;
      })
      .catch((err) => {
        ElMessage.error(err);
      });
  }
};

const handleOk = () => {
  if (beforeOk()) {
    firstVisible.value = false;
  }
};

const modalCancel = () => {
  // firstVisible.value = false;
  tagsViewStore.delView(route);
  router.go(-1);
};

const modalClose = () => {
  // tagsViewStore.delView(route);
  // router.go(-1);
};

const resetSalm = () => {
  empName.value = '';
  dptName.value = '';
  infoTableDataExist.value = [];
};

const resetBarcode = () => {
  barcode.value = '';
  dwgNo.value = '';
  prdName.value = '';
  moNum.value = '';
  mo.value = '';
  gsName.value = '';
  gsNo.value = '';
  prdGroup.value = '';
  okNum.value = '';
  ngNum.value = '';
  progressSum.value = '';
  progressNow.value = '';
  sumGs.value = '';
  basGs.value = '';
  syGs.value = '';
  bpRy.value = '';
  gzvs.value = '';
};

const beforeOk = () => {
  if (floorValue.value === '') {
    ElMessage.warning('请先选择');
    return false;
  }

  const data = floorData.value.find((en) => en.id === floorValue.value);
  if (data) {
    floorDesc.value = data.acnDesc;
  }
  return true;
};

// 生成行的唯一key
const getRowKey = (row) => {
  return `${row.empNo}_${row.barCode}`;
};

// 处理行选择变化的回调
const handleRowSelectionChange = (selectedRowKeys) => {
  console.log(selectedRowKeys);
  // selectedRows保存row-key
  selectedRows.value = selectedRowKeys.map((row) => `${row.empNo}_${row.barCode}`);
};

// 处理 Checkbox 状态变更的事件
const onCheckboxChange = (record, event) => {
  // 更新数据源中的对应记录的 Checkbox 状态
  record.flag = event ? '1' : '0';
  console.log(event);
};

// 处理 Checkbox 状态变更的事件
const checkAll = (check) => {
  // 更新数据源中的对应记录的 Checkbox 状态
  console.log(check);
  tableData.value.forEach((record) => {
    record.flag = check ? '1' : '0';
    return record;
  });
};

const enterSalNo = async () => {
  if (zpType.value === '') {
    ElMessage.warning('请先选择类型');
    salNo.value = '';
    return;
  }
  if (salNo.value === '') {
    resetSalm();
    return;
  }

  await getSalmInfo(salNo.value)
    .then((res) => {
      if (res.code !== 200) {
        ElMessage.error(res.msg);
        return;
      }
      if (res.data === null) {
        resetSalm();
        return;
      }
      empName.value = res.data.name;
      dptName.value = res.data.dptname;
      barcodeInput.value?.focus();
    })
    .catch((err) => {
      ElMessage.error(err);
    });

  if (empName.value && zpType.value !== '') {
    const param = { empNo: salNo.value, zpType: zpType.value };
    await getInfos(param)
      .then((res) => {
        const barcodeInfo = res.data.filter((re) => re.eDtm === null);
        if (barcodeInfo.length > 0) {
          infoTableDataExist.value = barcodeInfo;
        } else if (mo.value !== '' && empName.value !== '') {
          if (infoTableData.value.filter((en) => en.zpType === zpType.value && en.barCode === barcode.value).length > 0) {
            ElMessage.error('同一类型的条码不能重复开单');
          } else if (infoTableDataExist.value.filter((en) => en.zpType === zpType.value && en.barCode === barcode.value).length === 0) {
            infoTableData.value.push(JSON.parse(JSON.stringify(infoTableDataTemp.value)));
          }
        }
      })
      .catch((err) => {
        ElMessage.error(err);
      });
  }
};

const enterPeople = async (value, event) => {
  if (value === '') return;

  try {
    const res = await getSalmInfo(value);
    if (res.code !== 200) {
      ElMessage.error(res.msg);
      return;
    }
    if (!res.data) return;

    if (infoTableData.value.find((row) => row.empNo === people.value && row.barCode === barcode.value)) {
      ElMessage.error('不能添加重复人员');
      return;
    }

    const originalRow = infoTableData.value.find((row) => row.empNo === salNo.value && row.barCode === barcode.value);
    if (originalRow) {
      const { id, ...restOfTheRow } = originalRow;
      const newRow = { ...restOfTheRow, empNo: people.value, empName: res.data.name, dep: res.data.dptname };
      infoTableData.value = [...infoTableData.value, newRow];
    }
  } catch (err) {
    ElMessage.error(err);
  } finally {
    people.value = '';
  }
};

const enterBarcode = () => {
  if (barcode.value === '') {
    resetBarcode();
    return;
  }
  const par = { barCode: barcode.value };
  queryBarcodeInfo(par)
    .then((res) => {
      if (res.code === 200 && res.data !== null) {
        if (res.data.barType === '机构') {
          zpType.value = '机构装配';
        }
        if (res.data.barType === '电气') {
          zpType.value = '电气装配';
        }
      } else {
        resetBarcode();
        ElMessage.error('条码不存在');
        return Promise.reject(); // 确保返回一个Promise
      }
      // 返回一个Promise，确保下个then等待这个Promise的解决
      return Promise.resolve();
    })
    .then(() => {
      const infoParam = { barCode: barcode.value, zpType: zpType.value };
      return getInfos(infoParam);
    })
    .then((res) => {
      const result = res.data as [];
      if (res.data.length > 0) {
        const maxData = res.data.reduce((max, en) => (max.sumProd > en.sumProd ? max : en));
        progressSum.value = maxData.sumProd === null ? 0 : maxData.sumProd;
        if (maxData.sumProd === 100) {
          ElMessage.warning('总进度100不需要报工');
          resetBarcode();
          return Promise.reject(); // 确保返回一个Promise
        }
        sumGs.value = res.data.reduce((sum, en) => sum + en.sjGs, 0);
        if (res.data.find((re) => re.eDtm === null)) {
          if (salNo.value === '') {
            infoTableDataExist.value = res.data.filter((re) => re.eDtm === null);
          } else if (res.data.find((re) => re.eDtm === null && re.empNo === salNo.value)) {
            infoTableDataExist.value = res.data.filter((re) => re.eDtm === null && re.empNo === salNo.value);
          } else {
            return Promise.resolve();
          }
          return Promise.reject();
        }
      } else {
        progressSum.value = '';
        sumGs.value = '';
      }
      // 返回一个Promise，确保下个then等待这个Promise的解决
      return Promise.resolve();
    })
    .then(() => {
      const data = { barCode: barcode.value, actType: zpType.value };
      return queryBarcodeInfo(data);
    })
    .then((res) => {
      if (res.code !== 200) {
        resetBarcode();
        return Promise.reject(new Error(res.msg)); // 转换错误为Promise.reject
      }
      return res;
    })
    .then((res) => {
      // 否则，处理getOneOrderInfo的结果
      if (res.data === null) {
        resetBarcode();
        return;
      }
      mo.value = res.data?.moNo;
      // sfc_barcode表的设备项次字段为sopMo
      sopBatno.value = res.data?.sopMo;
      moNum.value = res.data?.moQty;
      dwgNo.value = res.data?.dwgNo;
      prdGroup.value = res.data?.productGroup;
      prdName.value = res.data?.prdName;
      gsNo.value = res.data?.proType;
      gsName.value = res.data?.proName;
      basGs.value = res.data?.basGs;
      bpRy.value = res.data?.bpRy;
      gzvs.value = res.data?.gzvs;
      if (sumGs.value === '') {
        sumGs.value = res.data?.wkGs === null ? 0 : res.data?.wkGs;
      }
      syGs.value = (Number(basGs.value) - Number(sumGs.value)).toString();
      if (progressSum.value === '') {
        progressSum.value = '0';
      }
      if (progressNow.value === '') {
        progressNow.value = '0';
      }
      if (mo.value !== '' && empName.value !== '') {
        if (infoTableData.value.filter((en) => en.zpType === zpType.value && en.barCode === barcode.value).length > 0) {
          ElMessage.error('同一类型的条码不能重复开单');
        } else if (infoTableDataExist.value.filter((en) => en.zpType === zpType.value && en.barCode === barcode.value).length === 0) {
          infoTableData.value.push(JSON.parse(JSON.stringify(infoTableDataTemp.value)));
        }
      }
      if (moNum.value === '') {
        moNumInput.value?.focus();
      }
    })
    .catch((err) => {
      resetBarcode();
      ElMessage.error(err.message || err);
    });
};

const clearNg = () => {
  ngValue.value = undefined;
};

const clearAll = () => {
  resetBarcode();
  resetSalm();
  clearNg();
  salNo.value = '';
  barcode.value = '';
  people.value = '';
  infoTableData.value = [];
};

const changeValue = (type) => {
  if (type === 'ng' && ngValue.value === undefined) {
    ElMessage.warning('请先选择不良原因');
    ngNum.value = '';
    return;
  }
  if (type === 'progress' && +progressNow.value + +progressSum.value > 100) {
    ElMessage.warning('进度之和不能超过100');
    return;
  }
  infoTableData.value.forEach((record) => {
    if (barcode.value === record.barCode) {
      record.curProd = progressNow.value;
      record.oQty = moNum.value;
      record.qtyNg = ngNum.value;
      record.qtyOk = okNum.value;
      record.reasonCode = ngValue.value?.acnCode;
      record.reasonName = ngValue.value?.acnDesc;
    }
    return record;
  });
};

const loadNgData = async (visible) => {
  if (visible) {
    await getCodeRuleList('BLYY')
      .then((res) => {
        console.log(res);
        ngData.value = res.data;
      })
      .catch((err) => {
        ElMessage.error(err);
      });
  }
};

const deleteRecord = () => {
  if (selectedRows.value.length === 0) {
    return;
  }
  infoTableData.value = infoTableData.value.filter((row) => !selectedRows.value?.includes(`${row.empNo}_${row.barCode}`));
  // 清空选择状态
  selectedRows.value = [];
};

const saveRecord = async () => {
  if (+progressNow.value + +progressSum.value > 100) {
    ElMessage.warning('进度之和不能超过100');
    progressNow.value = '';
    return;
  }
  if (infoTableData.value.filter((en) => en.bDtm !== '' && (String(en.curProd) === '0' || String(en.curProd) === '')).length > 0) {
    ElMessage.error('关单请输入当前进度');
    return;
  }
  if (infoTableData.value.filter((en) => String(en.oQty) === '').length > 0) {
    ElMessage.error('请输入数量');
    moNumInput.value?.focus();
    return;
  }

  loading.value = true;
  await saveInfos(infoTableData.value)
    .then((res) => {
      if (res.code === 200) {
        ElMessage.success(res.msg);
        clearAll();
        empInput.value?.focus();
      }
    })
    .catch((err) => {
      ElMessage.error(err);
    })
    .finally(() => {
      loading.value = false;
    });
};

const dbClick = (record, ev) => {
  people.value = '';
  barcode.value = record.barCode;
  const infoParam = { barCode: barcode.value, zpType: zpType.value };
  getInfos(infoParam)
    .then((res) => {
      if (res.data !== null) {
        const maxData = res.data.reduce((max, en) => (max.sumProd > en.sumProd ? max : en));
        progressSum.value = maxData.sumProd === null ? 0 : maxData.sumProd;
        if (maxData.sumProd === 100) {
          ElMessage.warning('总进度100不需要报工');
          return Promise.reject(new Error('总进度100不需要报工')); // 确保返回一个Promise
        }
        sumGs.value = res.data.reduce((sum, en) => sum + en.sjGs, 0);
        recordId.value = record.id;
        salNo.value = record.empNo;
        empName.value = record.empName;
        dptName.value = record.dep;
        dwgNo.value = record.wksDwg;
        prdName.value = record.cusName;
        moNum.value = record.oQty;
        mo.value = record.moNo;
        sopBatno.value = record.sopBatno;
        gsName.value = record.pName;
        gsNo.value = record.pType;
        prdGroup.value = record.prdGroup;
        okNum.value = record.qtyOk;
        ngNum.value = record.qtyNg;
        basGs.value = record.basGs;
        progressNow.value = record.curProd;
        record.sumProd = progressSum.value;
        syGs.value = (Number(basGs.value) - Number(sumGs.value)).toString();
        if (infoTableData.value.filter((en) => en.zpType === zpType.value && en.barCode === barcode.value).length === 0) {
          infoTableData.value.push(record);
        }
        clearNg();
        curProInput.value?.focus();
      }
      // 返回一个Promise，确保下个then等待这个Promise的解决
      return Promise.resolve();
    })
    .catch((err) => {
      console.log(err.message || err);
      ElMessage.error(err.message);
    });
};

onMounted(() => {
  console.log('sfc_barcode');
  // 加载初始选项数据
  getCodeRuleList('ZPTP')
    .then((res) => {
      if (res.code === 200) {
        zpData.value = res.data.filter((en) => en.rem?.includes('设备'));
      }
    })
    .catch((err) => {
      ElMessage.error(err);
    });
});
</script>

<style scoped></style>
