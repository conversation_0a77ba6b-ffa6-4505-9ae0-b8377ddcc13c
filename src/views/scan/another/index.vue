<template>
  <div>
    <el-dialog v-model="firstVisible" title="装配楼层" :close-on-click-modal="false" :close-on-press-escape="false"
               :show-close="false" style="width: 500px">
      <template #footer>
        <el-button @click="modalCancel">取消</el-button>
        <el-button type="primary" @click="handleOk">确定</el-button>
      </template>
      <el-select v-model="floorValue" style="width: 320px" placeholder="请选择" @visible-change="loadFloorData">
        <el-option v-for="item of floorData" :key="item.acnCode" :value="item.id" :label="item.acnDesc" />
      </el-select>
    </el-dialog>
    <el-card>
      <el-space direction="vertical" alignment="flex-start">
        <el-space style="position: relative; left: 63px">
          <el-input v-model="zpType" style="width: 100px" disabled />
          <el-text style="position: relative; left: 23px">装配楼层</el-text>
          <el-input v-model="floorDesc" style="position: relative; left: 24px; width: 200px" disabled />
        </el-space>
        <el-space>
          <el-text>操作工号</el-text>
          <el-input ref="empInput" v-model="salNo" style="width: 100px; position: relative; left: 0px" clearable
                    @keyup.enter="enterSalNo" />
          <el-input v-model="empName" style="width: 100px; position: relative; left: 0px" placeholder="" clearable />
          <el-input v-model="dptName" style="width: 229px; position: relative; left: 0px" placeholder="" clearable />
        </el-space>
        <el-space>
          <el-text>扫描条码</el-text>
          <el-input ref="barcodeInput" v-model="barcode" style="width: 445px; position: relative; left: 0px" clearable
                    @keyup.enter="enterBarcode" />
        </el-space>
        <el-space>
          <el-text>参与人员</el-text>
          <el-input v-model="people" style="width: 100px" placeholder="" clearable
                    @keyup.enter="enterPeople(people, $event)" />
          <el-text>工序号/名称</el-text>
          <el-input v-model="gsNo" style="width: 65px" placeholder="" clearable />
          <el-input v-model="gsName" style="width: 180px" placeholder="" clearable />
        </el-space>
      </el-space>
      <el-divider></el-divider>
      <el-table :data="infoTableDataExist" border size="small" row-key="empNo" @row-dblclick="dbClick">
        <!-- <el-table-column prop="cusName" label="产品名"></el-table-column> -->
        <!-- <el-table-column prop="moNo" label="MO号"></el-table-column>
        <el-table-column prop="wksDwg" label="图号"></el-table-column> -->
        <el-table-column prop="empNo" label="工号"></el-table-column>
        <el-table-column prop="empName" label="姓名"></el-table-column>
        <el-table-column prop="dep" label="部门"></el-table-column>
        <el-table-column prop="pType" label="工站号"></el-table-column>
        <el-table-column prop="pName" label="工站名称"></el-table-column>
        <!--      <el-table-column prop="basGs" label="标准工时"></el-table-column>-->
        <!--      <el-table-column prop="bpRy" label="标配人力"></el-table-column>-->
        <el-table-column prop="barCode" label="条码"></el-table-column>
        <el-table-column prop="bDtm" label="开始时间"></el-table-column>
        <el-table-column prop="eDtm" label="结束时间"></el-table-column>
        <el-table-column prop="sjGs" label="实际工时"></el-table-column>
        <!--      <el-table-column prop="qtyOk" label="良品数量"></el-table-column>-->
        <!--      <el-table-column prop="qtyNg" label="不良数量"></el-table-column>-->
        <!--      <el-table-column prop="reasonName" label="不良原因"></el-table-column>-->
        <!--      <el-table-column prop="curProd" label="当前进度"></el-table-column>-->
        <!--      <el-table-column prop="sumProd" label="总进度"></el-table-column>-->
        <el-table-column prop="mSta" label="状态"></el-table-column>
        <el-table-column prop="rem" label="备注"></el-table-column>
        <el-table-column prop="zpType" label="装配类型"></el-table-column>
        <!--      <el-table-column prop="arbpl" label="ARBPL"></el-table-column>-->
        <el-table-column prop="wkGs" label="累计工时"></el-table-column>
      </el-table>
      <el-divider></el-divider>
      <el-table :data="infoTableData" border size="small" row-key="empNo" @selection-change="handleRowSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <!-- <el-table-column prop="cusName" label="产品名"></el-table-column> -->
        <!-- <el-table-column prop="moNo" label="MO号"></el-table-column>
        <el-table-column prop="wksDwg" label="图号"></el-table-column> -->
        <el-table-column prop="empNo" label="工号"></el-table-column>
        <el-table-column prop="empName" label="姓名"></el-table-column>
        <el-table-column prop="dep" label="部门"></el-table-column>
        <el-table-column prop="pType" label="工站号"></el-table-column>
        <el-table-column prop="pName" label="工站名称"></el-table-column>
        <!--      <el-table-column prop="basGs" label="标准工时"></el-table-column>-->
        <!--      <el-table-column prop="bpRy" label="标配人力"></el-table-column>-->
        <el-table-column prop="barCode" label="条码"></el-table-column>
        <el-table-column prop="bDtm" label="开始时间"></el-table-column>
        <el-table-column prop="eDtm" label="结束时间"></el-table-column>
        <el-table-column prop="sjGs" label="实际工时"></el-table-column>
        <!--      <el-table-column prop="qtyOk" label="良品数量"></el-table-column>-->
        <!--      <el-table-column prop="qtyNg" label="不良数量"></el-table-column>-->
        <!--      <el-table-column prop="reasonName" label="不良原因"></el-table-column>-->
        <!--      <el-table-column prop="curProd" label="当前进度"></el-table-column>-->
        <!--      <el-table-column prop="sumProd" label="总进度"></el-table-column>-->
        <el-table-column prop="mSta" label="状态"></el-table-column>
        <el-table-column prop="rem" label="备注"></el-table-column>
        <el-table-column prop="zpType" label="装配类型"></el-table-column>
        <!--      <el-table-column prop="arbpl" label="ARBPL"></el-table-column>-->
        <el-table-column prop="wkGs" label="累计工时"></el-table-column>
      </el-table>
      <el-space class="button1">
        <el-button type="primary" @click="deleteRecord">删除</el-button>
        <el-button type="primary" :loading="loading" @click="saveRecord">保存</el-button>
      </el-space>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getSalmInfo } from '@/api/salm';
import { getInfos, saveAnotherInfos, SfcInfoModel } from '@/api/information/sfcInfo';
import { queryAnotherBarcodeInfo } from '@/api/barcode/equipBarcode';
import { getCodeRuleList } from '@/api/information/codeRule';
import { CodeRuleModel } from '@/api/information/codeRule/types';
import { useTagsViewStore } from '@/store/modules/tagsView';

const tagsViewStore = useTagsViewStore();
const route = useRoute();
const router = useRouter();

const barcodeInput = ref<HTMLInputElement | null>(null);
const empInput = ref<HTMLInputElement | null>(null);
const floorValue = ref('');
const floorData = ref<CodeRuleModel[]>([]);
const floorDesc = ref('');
const zpType = ref('');
const mo = ref('');
const salNo = ref('');
const empName = ref('');
const dptName = ref('');
const prdName = ref('');
const prdGroup = ref('');
const moNum = ref('');
const okNum = ref('');
const dwgNo = ref('');
const gsNo = ref('');
const gsName = ref('');
const gzvs = ref('');
const sumGs = ref('');
const barcode = ref('');
const people = ref('');
const loading = ref(false);
const firstVisible = ref(true);

// 存储选中的行
const selectedRows = ref([]);

const infoTableData = ref<SfcInfoModel[]>([]);
const infoTableDataExist = ref<SfcInfoModel[]>([]);
const infoTableDataTemp = ref({
  zpType,
  floorNo: floorDesc,
  cusName: prdName,
  moNo: mo,
  wksDwg: dwgNo,
  pType: gsNo,
  pName: gsName,
  empNo: salNo,
  empName,
  bDtm: '',
  eDtm: '',
  sjGs: '',
  qtyOk: okNum,
  barCode: barcode,
  flag: '',
  wkGs: sumGs,
  dep: dptName,
  arbpl: gzvs,
  prdGroup,
  oQty: moNum
});

const resetSalm = () => {
  empName.value = '';
  dptName.value = '';
  infoTableDataExist.value = [];
};

const resetBarcode = () => {
  dwgNo.value = '';
  prdName.value = '';
  moNum.value = '';
  mo.value = '';
  gsName.value = '';
  gsNo.value = '';
  prdGroup.value = '';
  okNum.value = '';
  gzvs.value = '';
  barcode.value = '';
  zpType.value = '';
  infoTableData.value = [];
};

// 处理行选择变化的回调
const handleRowSelectionChange = (selectedRowKeys) => {
  selectedRows.value = selectedRowKeys.map(row => row.empNo);
};

const enterSalNo = async () => {
  if (salNo.value === '') {
    resetSalm();
    return;
  }
  await getSalmInfo(salNo.value)
    .then((res) => {
      if (res.code !== 200) {
        ElMessage.error(res.msg);
        return;
      }
      if (res.data === null) {
        resetSalm();
        return;
      }
      empName.value = res.data.name;
      dptName.value = res.data.dptname;
      barcodeInput.value?.focus();
    })
    .catch((err) => {
      ElMessage.error(err);
    });
};

const enterPeople = async (value, event) => {
  if (value === '') {
    return;
  }
  await getSalmInfo(value)
    .then((res) => {
      if (res.code !== 200) {
        ElMessage.error(res.msg);
        return;
      }
      if (res.data !== null) {
        if (infoTableData.value.find((row) => row.empNo === people.value)) {
          ElMessage.error('不能添加重复人员');
          return;
        }
        const originalRow = infoTableData.value[0];
        // 使用解构赋值来复制除了ID以外的所有字段
        if (originalRow) {
          const { id, ...restOfTheRow } = originalRow;
          const newRow = { ...restOfTheRow };
          newRow.empNo = people.value;
          newRow.empName = res.data.name;
          newRow.dep = res.data.dptname;
          infoTableData.value = [...infoTableData.value, newRow];
          // 清空选择状态
          selectedRows.value = [];
        }
      }
    })
    .catch((err) => {
      ElMessage.error(err);
    })
    .finally(() => {
      people.value = '';
    });
};

const enterBarcode = () => {
  if (barcode.value === '') {
    resetBarcode();
    return;
  }
  const infoParam = { barCode: barcode.value };
  getInfos(infoParam).then((res) => {
    const result = res.data as [];
    if (res.data.length > 0) {
      if (res.data.find((re) => re.eDtm === null)) {
        if (salNo.value === '') {
          infoTableDataExist.value = res.data.filter((re) => re.eDtm === null);
        } else if (res.data.find((re) => re.eDtm === null && re.empNo === salNo.value)) {
          infoTableDataExist.value = res.data.filter((re) => re.eDtm === null && re.empNo === salNo.value);
        } else {
          return Promise.resolve();
        }
        return Promise.reject();
      }
    }
    // 返回一个Promise，确保下个then等待这个Promise的解决
    return Promise.resolve();
  }).then(() => {
    const data = { barCode: barcode.value };
    return queryAnotherBarcodeInfo(data);
  }).then((res) => {
    if (res.code !== 200) {
      resetBarcode();
      return Promise.reject(new Error(res.msg)); // 转换错误为Promise.reject
    }
    return res;
  }).then((res) => {
    // 否则，处理getOneOrderInfo的结果
    if (res.data === null) {
      resetBarcode();
      return;
    }
    mo.value = res.data?.moNo;
    moNum.value = res.data?.moQty;
    dwgNo.value = res.data?.dwgNo;
    prdGroup.value = res.data?.productGroup;
    prdName.value = res.data?.prdName;
    gsNo.value = res.data?.proType;
    gsName.value = res.data?.proName;
    gzvs.value = res.data?.gzvs;
    zpType.value = res.data?.actType;
    if (mo.value !== '' && empName.value !== '') {
      if (infoTableData.value.filter((en) => en.zpType === zpType.value && en.barCode === barcode.value).length > 0) {
        ElMessage.error('同一类型的条码不能重复开单');
      } else if (infoTableDataExist.value.filter((en) => en.zpType === zpType.value && en.barCode === barcode.value).length === 0) {
        infoTableData.value.push(JSON.parse(JSON.stringify(infoTableDataTemp.value)));
      }
    }
  }).catch((err) => {
    ElMessage.error(err.message || err);
  });
};

const deleteRecord = () => {
  if (selectedRows.value.length === 0) {
    return;
  }
  infoTableData.value = infoTableData.value.filter(
    (row) => !selectedRows.value.includes(row.empNo)
  );
  // 清空选择状态
  selectedRows.value = [];
};

const saveRecord = async () => {
  loading.value = true;
  await saveAnotherInfos(infoTableData.value)
    .then((res) => {
      ElMessage.success(res.msg);
      if (res.code === 200) {
        infoTableData.value = [];
        infoTableDataExist.value = [];
        resetSalm();
        resetBarcode();
        salNo.value = '';
        barcode.value = '';
        people.value = '';
        empInput.value?.focus();
      }
    })
    .catch((err) => {
      ElMessage.error(err);
    })
    .finally(() => {
      loading.value = false;
    });
};

const dbClick = (record, ev) => {
  salNo.value = record.empNo;
  empName.value = record.empName;
  dptName.value = record.dep;
  barcode.value = record.barCode;
  dwgNo.value = record.wksDwg;
  prdName.value = record.cusName;
  moNum.value = record.oQty;
  mo.value = record.moNo;
  gsName.value = record.pName;
  gsNo.value = record.pType;
  prdGroup.value = record.prdGroup;
  okNum.value = record.qtyOk;
  zpType.value = record.zpType;
  if (infoTableData.value.filter((en) => en.zpType === zpType.value && en.barCode === barcode.value).length === 0) {
    infoTableData.value.push(record);
  }
};

const modalCancel = () => {
  tagsViewStore.delView(route);
  router.go(-1);
};

const handleOk = () => {
  if (beforeOk()) {
    firstVisible.value = false;
  }
};

const beforeOk = () => {
  if (floorValue.value === '') {
    ElMessage.warning('请先选择');
    return false;
  }

  const data = floorData.value.find((en) => en.id === floorValue.value);
  if (data) {
    floorDesc.value = data.acnDesc;
  }
  return true;
};

const loadFloorData = async (visible) => {
  if (visible) {
    await getCodeRuleList('FLOR')
      .then((res) => {
        floorData.value = res.data;
      })
      .catch((err) => {
        ElMessage.error(err);
      });
  }
};

onMounted(() => {
  console.log('sfc_barcode');
});
</script>

<style scoped>
</style>
