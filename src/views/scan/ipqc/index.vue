<template>
  <div>
    <el-dialog v-model="firstVisible" title="装配楼层" :close-on-click-modal="false" :close-on-press-escape="false"
               :show-close="false" @close="modalClose" style="width: 500px">
      <template #footer>
        <el-button @click="modalCancel">取消</el-button>
        <el-button type="primary" @click="handleOk">确定</el-button>
      </template>
      <el-select v-model="selectValue" style="width: 320px" placeholder="请选择" @visible-change="loadFloorData">
        <el-option v-for="item in floorData" :key="item.acnCode" :value="item.id" :label="item.acnDesc" />
      </el-select>
    </el-dialog>
    <el-dialog v-model="visible" title="装配文件" width="auto" :show-close="false" :close-on-click-modal="false"
               draggable>
      <el-space direction="vertical" alignment="flex-start">
        <el-space>
          <el-text>MO号</el-text>
          <el-input v-model="mo" style="width: 200px" placeholder="" />
          <el-input v-model="prdName" style="width: 300px" placeholder="" />
          <el-input v-model="dwgNo" style="width: 400px" placeholder="" />
        </el-space>
        <el-space>
          <el-checkbox v-model="checkedAll" @change="checkAll(checkedAll)">全选</el-checkbox>
          <el-button type="primary" @click="saveFileInfo">保存</el-button>
        </el-space>
        <el-table :data="tableData" border size="small">
          <el-table-column prop="fileItm" label="序号"></el-table-column>
          <el-table-column prop="moNo" label="MO号"></el-table-column>
          <el-table-column prop="fileListName" label="文件名"></el-table-column>
          <el-table-column prop="flag" label="是否有">
            <template #default="scope">
              <el-checkbox :model-value="scope.row.flag === '1'"
                           @change="onCheckboxChange(scope.row, $event)"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="chkDept" label="检查部门"></el-table-column>
          <el-table-column prop="host" label="操作电脑"></el-table-column>
          <el-table-column prop="sysdt" label="操作时间"></el-table-column>
        </el-table>
      </el-space>
      <template #footer>
        <el-button type="primary" @click="okExit">{{ okText }}</el-button>
      </template>
    </el-dialog>
    <el-card>
      <el-space direction="vertical" alignment="flex-start">
        <el-space style="position: relative; left: 63px">
          <el-select ref="typeSelect" v-model="zpType" style="width: 120px" placeholder="请选择类型">
            <el-option key="IPQC检验" value="IPQC检验" label="IPQC检验" />
          </el-select>
          <el-text style="position: relative; left: 23px">装配楼层</el-text>
          <el-input v-model="floorDesc" style="position: relative; left: 24px; width: 200px" disabled />
        </el-space>
        <el-space>
          <el-text>操作工号</el-text>
          <el-input ref="empInput" v-model="salNo" style="position: relative; left: 0px; width: 100px"
                    @keyup.enter="enterSalNo(salNo,$event)" placeholder="" clearable />
          <el-input v-model="empName" style="position: relative; left: 0px; width: 100px" placeholder="" clearable />
          <el-input v-model="dptName" style="position: relative; left: 0px; width: 200px" placeholder="" clearable />
          <el-text style="position: relative; left: 30px">参与人员</el-text>
          <el-input v-model="people" style="position: relative; left: 30px; width: 150px" clearable
                    @blur="enterPeople(people,$event)" @keyup.enter="enterPeople(people,$event)" />
          <el-text style="position: relative; left: 30px">工作中心</el-text>
          <el-input v-model="gzvs" style="position: relative; left: 30px; width: 235px" placeholder="" clearable />
        </el-space>
        <el-space>
          <el-text>扫描条码</el-text>
          <el-input ref="barcodeInput" v-model="barcode" style="position: relative; left: 0px; width: 415px"
                    clearable @keyup.enter="enterBarcode" />
          <el-text style="position: relative; left: 30px">MO号</el-text>
          <el-input v-model="mo" style="position: relative; left: 50px; width: 150px" placeholder="" clearable />
          <el-text style="position: relative; left: 50px">产品名称</el-text>
          <el-input v-model="prdName" style="position: relative; left: 50px; width: 443px" placeholder="" clearable />
        </el-space>
        <el-space>
          <el-text>订单数量</el-text>
          <el-input v-model="moNum" style="width: 100px" placeholder="" clearable />
          <el-text>产品组</el-text>
          <el-input v-model="prdGroup" style="position: relative; left: 15px; width: 243px" placeholder="" clearable />
          <el-text style="position: relative; left: 45px">图号</el-text>
          <el-input v-model="dwgNo" style="position: relative; left: 72px; width: 464px" placeholder="" clearable />
        </el-space>
        <el-space>
          <el-text>总进度</el-text>
          <el-input v-model="progressSum" disabled style="position: relative; left: 14px; width: 100px" />
          <el-text style="position: relative; left: 15px">当前进度</el-text>
          <el-input ref="curProInput" v-model="progressNow" style="position: relative; left: 15px; width: 100px"
                    clearable @blur="changeValue('progress')" />
          <el-text style="position: relative; left: 15px">累计工时</el-text>
          <el-input v-model="sumGs" disabled style="position: relative; left: 15px; width: 100px" clearable />
          <el-text style="position: relative; left: 15px">良品数量</el-text>
          <el-input v-model="okNum" style="position: relative; left: 15px; width: 100px" clearable
                    @blur="changeValue" />
          <el-text style="position: relative; left: 15px">不良数量</el-text>
          <el-input v-model="ngNum" style="position: relative; left: 15px; width: 100px" clearable
                    @blur="changeValue" />
          <el-text style="position: relative; left: 15px">工序号/名称</el-text>
          <el-input v-model="gsNo" style="position: relative; left: 15px; width: 100px" clearable />
          <el-input v-model="gsName" style="position: relative; left: 15px; width: 200px" clearable />
        </el-space>
      </el-space>
      <el-divider></el-divider>
      <el-table :data="infoTableDataExist" border size="small" row-key="empNo" @row-dblclick="dbClick">
        <el-table-column prop="cusName" label="产品名"></el-table-column>
        <el-table-column prop="moNo" label="MO号"></el-table-column>
        <el-table-column prop="wksDwg" label="图号"></el-table-column>
        <el-table-column prop="empNo" label="工号"></el-table-column>
        <el-table-column prop="empName" label="姓名"></el-table-column>
        <el-table-column prop="pType" label="工站号"></el-table-column>
        <el-table-column prop="pName" label="工站名称"></el-table-column>
        <el-table-column prop="basGs" label="标准工时"></el-table-column>
        <el-table-column prop="bpRy" label="标配人力"></el-table-column>
        <el-table-column prop="barCode" label="条码"></el-table-column>
        <el-table-column prop="bDtm" label="开始时间"></el-table-column>
        <el-table-column prop="eDtm" label="结束时间"></el-table-column>
        <el-table-column prop="sjGs" label="实际工时"></el-table-column>
        <el-table-column prop="qtyOk" label="良品数量"></el-table-column>
        <el-table-column prop="qtyNg" label="不良数量"></el-table-column>
        <el-table-column prop="reasonName" label="不良原因"></el-table-column>
        <el-table-column prop="curProd" label="当前进度"></el-table-column>
        <el-table-column prop="sumProd" label="总进度"></el-table-column>
        <el-table-column prop="mSta" label="状态"></el-table-column>
        <el-table-column prop="rem" label="备注"></el-table-column>
        <el-table-column prop="zpType" label="装配类型"></el-table-column>
        <el-table-column prop="arbpl" label="ARBPL"></el-table-column>
        <el-table-column prop="wkGs" label="累计工时"></el-table-column>
      </el-table>
      <el-divider></el-divider>
      <el-table :data="infoTableData" border size="small" row-key="empNo" @selection-change="handleRowSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="cusName" label="产品名"></el-table-column>
        <el-table-column prop="moNo" label="MO号"></el-table-column>
        <el-table-column prop="wksDwg" label="图号"></el-table-column>
        <el-table-column prop="empNo" label="工号"></el-table-column>
        <el-table-column prop="empName" label="姓名"></el-table-column>
        <el-table-column prop="pType" label="工站号"></el-table-column>
        <el-table-column prop="pName" label="工站名称"></el-table-column>
        <el-table-column prop="basGs" label="标准工时"></el-table-column>
        <el-table-column prop="bpRy" label="标配人力"></el-table-column>
        <el-table-column prop="barCode" label="条码"></el-table-column>
        <el-table-column prop="bDtm" label="开始时间"></el-table-column>
        <el-table-column prop="eDtm" label="结束时间"></el-table-column>
        <el-table-column prop="sjGs" label="实际工时"></el-table-column>
        <el-table-column prop="qtyOk" label="良品数量"></el-table-column>
        <el-table-column prop="qtyNg" label="不良数量"></el-table-column>
        <el-table-column prop="reasonName" label="不良原因"></el-table-column>
        <el-table-column prop="curProd" label="当前进度"></el-table-column>
        <el-table-column prop="sumProd" label="总进度"></el-table-column>
        <el-table-column prop="mSta" label="状态"></el-table-column>
        <el-table-column prop="rem" label="备注"></el-table-column>
        <el-table-column prop="zpType" label="装配类型"></el-table-column>
        <el-table-column prop="arbpl" label="ARBPL"></el-table-column>
        <el-table-column prop="wkGs" label="累计工时"></el-table-column>
      </el-table>
      <el-space class="button1">
        <el-button type="primary" @click="deleteRecord">删除</el-button>
        <el-button type="primary" :loading="loading" @click="saveRecord">保存</el-button>
      </el-space>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from 'element-plus';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';
import { useTagsViewStore } from '@/store/modules/tagsView';

// 根据实际API路径调整
import { getCodeRuleList } from '@/api/information/codeRule';
import { CodeRuleModel } from '@/api/information/codeRule/types';
import { FileInfoModel } from '@/api/information/fileList/types';
import { getSalmInfo } from '@/api/salm';
import { getInfos, saveInfos } from '@/api/information/sfcInfo';
import { SfcInfoModel } from '@/api/information/sfcInfo/types';
import { queryBarcodeInfo } from '@/api/barcode/equipBarcode';
import { getFileInfos, saveFileInfos } from '@/api/information/fileInfo';
import { getIpqcEmpOne } from '@/api/information/ipqcEmp';

const { t } = useI18n();
const router = useRouter();
const route = useRoute();
const tagsViewStore = useTagsViewStore();

const barcodeInput = ref<HTMLInputElement | null>(null);
const empInput = ref<HTMLInputElement | null>(null);
const selectValue = ref('');
const floorData = ref<CodeRuleModel[]>([]);
const floorDesc = ref('');
const zpType = ref('IPQC检验');
const mo = ref('');
const salNo = ref('');
const empName = ref('');
const dptName = ref('');
const prdName = ref('');
const prdGroup = ref('');
const moNum = ref('');
const okNum = ref('');
const ngNum = ref('');
const dwgNo = ref('');
const gsNo = ref('');
const gsName = ref('');
const gzvs = ref('');
const sumGs = ref('');
const barcode = ref('');
const people = ref('');
const progressSum = ref('');
const progressNow = ref('');
const checkedAll = ref(false);
const visible = ref(false);
const loading = ref(false);
const firstVisible = ref(true);
const okText = computed(() => '退出');
const curProInput = ref<HTMLInputElement | null>(null);
const recordId = ref();

// 存储选中的行
const selectedRows = ref([]);

const tableData = ref<FileInfoModel[]>([]);
const tableHead = ref([]);
const infoTableHead = ref([]);
const infoTableData = ref<SfcInfoModel[]>([]);
const infoTableDataExist = ref<SfcInfoModel[]>([]);
const infoTableDataTemp = ref([
  {
    zpType,
    floorNo: floorDesc,
    cusName: prdName,
    moNo: mo,
    wksDwg: dwgNo,
    pType: gsNo,
    pName: gsName,
    empNo: salNo,
    empName,
    bDtm: '',
    eDtm: '',
    sjGs: '',
    gyGs: '',
    qtyOk: okNum,
    qtyNg: ngNum,
    sumProd: progressSum,
    curProd: progressNow,
    barCode: barcode,
    flag: '',
    wkGs: sumGs,
    dep: dptName,
    arbpl: gzvs,
    prdGroup,
    oQty: moNum
  }
]);

const resetSalm = () => {
  empName.value = '';
  dptName.value = '';
  infoTableDataExist.value = [];
};

const resetBarcode = () => {
  barcode.value = '';
  dwgNo.value = '';
  prdName.value = '';
  moNum.value = '';
  mo.value = '';
  gsName.value = '';
  gsNo.value = '';
  prdGroup.value = '';
  okNum.value = '';
  ngNum.value = '';
  progressSum.value = '';
  progressNow.value = '';
  sumGs.value = '';
  gzvs.value = '';
  infoTableData.value = [];
};

const modalCancel = () => {
  tagsViewStore.delView(route);
  router.go(-1);
};

const modalClose = () => {
  // 空实现，保持原有逻辑
};

const handleOk = () => {
  if (beforeOk()) {
    firstVisible.value = false;
  }
};

const beforeOk = () => {
  if (selectValue.value === '') {
    ElMessage.warning('请先选择');
    return false;
  }

  const data = floorData.value.find((en) => en.id === selectValue.value);
  if (data) {
    floorDesc.value = data.acnDesc;
  }
  return true;
};

// 处理行选择变化的回调
const handleRowSelectionChange = (selectedRowKeys) => {
  console.log('Selected row keys:', selectedRowKeys);
  selectedRows.value = selectedRowKeys.map(row => row.empNo);
};

// 处理 Checkbox 状态变更的事件
const onCheckboxChange = (record, event) => {
  // 更新数据源中的对应记录的 Checkbox 状态
  record.flag = event ? '1' : '0';
  console.log(event);
};

// 处理 Checkbox 状态变更的事件
const checkAll = (check) => {
  // 更新数据源中的对应记录的 Checkbox 状态
  console.log(check);
  tableData.value.forEach((record) => {
    record.flag = check ? '1' : '0';
    return record;
  });
};

const enterSalNo = async (value, event) => {
  if (value === '') {
    resetSalm();
    return;
  }
  const param = { empNo: value };
  await getIpqcEmpOne(param).then((res) => {
    if (res.code !== 200) {
      ElMessage.error(res.msg);
      return;
    }
    if (res.data === null) {
      resetSalm();
      return;
    }
    empName.value = res.data.empName;
    dptName.value = res.data.dept;
    barcodeInput.value?.focus();
  }).catch((err) => {
    ElMessage.error(err);
  });
  if (empName.value) {
    const infoParam = { empNo: salNo.value, zpType: 'IPQC检验' };
    await getInfos(infoParam).then((res) => {
      infoTableDataExist.value = res.data.filter((re) => re.eDtm === null);
    }).catch((err) => {
      ElMessage.error(err);
    });
  }
};

const enterPeople = async (value, event) => {
  if (value === '') {
    return;
  }
  await getSalmInfo(value)
    .then((res) => {
      if (res.code !== 200) {
        ElMessage.error(res.msg);
        return;
      }
      if (res.data !== null) {
        if (infoTableData.value.find((row) => row.empNo === people.value)) {
          ElMessage.error('不能添加重复人员');
          return;
        }
        const originalRow = infoTableData.value.find(
          (row) => row.empNo === salNo.value && row.barCode === barcode.value
        );
        // 使用解构赋值来复制除了ID以外的所有字段
        if (originalRow) {
          const { id, ...restOfTheRow } = originalRow;
          const newRow = { ...restOfTheRow };
          newRow.empNo = people.value;
          newRow.empName = res.data.name;
          newRow.dep = res.data.dptname;
          infoTableData.value = [...infoTableData.value, newRow];
        }
      }
    })
    .catch((err) => {
      ElMessage.error(err);
    })
    .finally(() => {
      people.value = '';
    });
};

const okExit = async () => {
  okNum.value = '1';
  infoTableData.value = infoTableDataTemp.value;
  visible.value = false;
};

const enterBarcode = () => {
  if (barcode.value === '') {
    resetBarcode();
    return;
  }
  const infoParam = { barCode: barcode.value, zpType: 'IPQC检验' };
  getInfos(infoParam).then((res) => {
    const result = res.data as [];
    if (res.data.length > 0) {
      const maxData = res.data.reduce((max, en) =>
        max.sumProd > en.sumProd ? max : en
      );
      progressSum.value = maxData.sumProd === null ? 0 : maxData.sumProd;
      if (maxData.sumProd === 100) {
        ElMessage.warning('总进度100不需要报工');
        resetBarcode();
        return Promise.reject(); // 确保返回一个Promise
      }
      sumGs.value = res.data.reduce((sum, en) => sum + en.sjGs, 0);
      if (res.data.find((re) => re.eDtm === null)) {
        if (salNo.value === '') {
          infoTableDataExist.value = res.data.filter((re) => re.eDtm === null);
        } else if (res.data.find((re) => re.eDtm === null && re.empNo === salNo.value)) {
          infoTableDataExist.value = res.data.filter((re) => re.eDtm === null && re.empNo === salNo.value);
        } else {
          return Promise.resolve();
        }
        return Promise.reject();
      }
    }
    // 返回一个Promise，确保下个then等待这个Promise的解决
    return Promise.resolve();
  }).then(() => {
    const data = { barCode: barcode.value, actType: zpType.value };
    return queryBarcodeInfo(data);
  }).then((res) => {
    if (res.code !== 200) {
      return Promise.reject(new Error(res.msg)); // 转换错误为Promise.reject
    }
    return res;
  }).then((res) => {
    // 否则，处理getOneOrderInfo的结果
    if (res.data === null) {
      resetBarcode();
      return Promise.reject();
    }
    mo.value = res.data?.moNo;
    moNum.value = res.data?.moQty;
    dwgNo.value = res.data?.dwgNo;
    prdGroup.value = res.data?.productGroup;
    prdName.value = res.data?.prdName;
    gsNo.value = res.data?.proType;
    gsName.value = res.data?.proName;
    gzvs.value = res.data?.gzvs;
    if (progressSum.value === '') {
      progressSum.value = '0';
    }
    if (progressNow.value === '') {
      progressNow.value = '0';
    }
    // 返回一个Promise，确保下个then等待这个Promise的解决
    return Promise.resolve();
  }).then(() => {
    const param = { moNo: mo.value, type: 'IPQC' };
    return getFileInfos(param);
  }).then((res) => {
    if (res.data.find((en) => en.flag === '0')) {
      visible.value = true;
      tableData.value = res.data;
    } else {
      okExit();
    }
  }).catch((err) => {
    ElMessage.error(err);
  });
};

const changeValue = (type) => {
  if (type === 'progress' && +progressNow.value + +progressSum.value > 100) {
    ElMessage.warning('进度之和不能超过100');
    return;
  }
  infoTableData.value.forEach((record) => {
    if (barcode.value === record.barCode) {
      record.curProd = progressNow.value;
      record.qtyNg = ngNum.value;
      record.qtyOk = okNum.value;
    }
    return record;
  });
};

const saveFileInfo = async () => {
  await saveFileInfos(tableData.value).then((res) => {
    if (res.code === 200) {
      ElMessage.success(res.msg);
      okExit();
    }
  }).catch((err) => {
    ElMessage.error(err);
  });
};

const loadFloorData = async (visible) => {
  if (visible) {
    await getCodeRuleList('FLOR')
      .then((res) => {
        console.log(res);
        floorData.value = res.data;
      })
      .catch((err) => {
        ElMessage.error(err);
      });
  }
};

const deleteRecord = () => {
  if (selectedRows.value.length === 0) {
    return;
  }
  infoTableData.value = infoTableData.value.filter((row) => !selectedRows.value.includes(row.empNo));
  // 清空选择状态
  selectedRows.value = [];
};

const saveRecord = async () => {
  if (+progressNow.value + +progressSum.value > 100) {
    ElMessage.warning('进度之和不能超过100');
    progressNow.value = '';
    return;
  }
  if (infoTableData.value.filter((en) => en.bDtm !== '' && (String(en.curProd) === '0' || String(en.curProd) === '')).length > 0) {
    ElMessage.error('关单请输入当前进度');
    return;
  }
  loading.value = true;
  await saveInfos(infoTableData.value).then((res) => {
    if (res.code === 200) {
      infoTableData.value = [];
      infoTableDataExist.value = [];
      resetSalm();
      resetBarcode();
      salNo.value = '';
      barcode.value = '';
      people.value = '';
      empInput.value?.focus();
      ElMessage.success(res.msg);
    }
  }).catch((err) => {
    ElMessage.error(err);
  }).finally(() => {
    loading.value = false;
  });
};

const dbClick = (record, ev) => {
  barcode.value = record.barCode;
  const infoParam = { barCode: barcode.value, zpType: 'IPQC检验' };
  getInfos(infoParam)
    .then((res) => {
      if (res.data !== null) {
        const maxData = res.data.reduce((max, en) =>
          max.sumProd > en.sumProd ? max : en
        );
        progressSum.value = maxData.sumProd === null ? 0 : maxData.sumProd;
        if (maxData.sumProd === 100) {
          ElMessage.warning('总进度100不需要报工');
          return Promise.reject(new Error('总进度100不需要报工')); // 确保返回一个Promise
        }
        sumGs.value = res.data.reduce((sum, en) => sum + en.sjGs, 0);
        recordId.value = record.id;
        salNo.value = record.empNo;
        empName.value = record.empName;
        dptName.value = record.dep;
        dwgNo.value = record.wksDwg;
        prdName.value = record.cusName;
        moNum.value = record.oQty;
        mo.value = record.moNo;
        gsName.value = record.pName;
        gsNo.value = record.pType;
        prdGroup.value = record.prdGroup;
        okNum.value = record.qtyOk;
        ngNum.value = record.qtyNg;
        infoTableData.value = [];
        infoTableData.value.push(record);
        curProInput.value?.focus();
      }
      // 返回一个Promise，确保下个then等待这个Promise的解决
      return Promise.resolve();
    })
    .catch((err) => {
      console.log(err.message || err);
    });
};

onMounted(() => {
  console.log('MO12+0010');
});
</script>

<style scoped>
</style>
