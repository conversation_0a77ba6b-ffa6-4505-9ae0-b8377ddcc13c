<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="MO号" prop="moNo">
              <el-input v-model="queryParams.moNo" placeholder="请输入MO号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="项目号" prop="pmNo">
              <el-input v-model="queryParams.pmNo" placeholder="请输入项目号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备名称" prop="deviceDesc">
              <el-input v-model="queryParams.deviceDesc" placeholder="请输入设备名称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="设备项次" prop="sopMo">
              <el-input v-model="queryParams.sopMo" placeholder="请输入设备项次" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户代码" prop="custNo">
              <el-input v-model="queryParams.custNo" placeholder="请输入客户代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['sfc:zp:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['sfc:zp:edit']">修改 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['sfc:zp:remove']">删除 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['sfc:zp:export']">导出 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Download" @click="exportTemplate">导出模板</el-button>
          </el-col>
          <el-col :span="2">
            <el-upload accept=".xls,.xlsx" :http-request="uploadMoFile" :show-file-list="false" :before-upload="beforeUpload">
              <el-button type="primary" plain icon="Upload">批量导入</el-button>
            </el-upload>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Picture" @click="showPicDialog">图库/视频</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="zpList" @selection-change="handleSelectionChange" @cell-dblclick="dbclick">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="true" />
        <el-table-column label="项目号" align="center" prop="pmNo" />
        <el-table-column label="设备名称" align="center" prop="deviceDesc" />
        <el-table-column label="MO号" align="center" prop="moNo" />
        <el-table-column label="设备项次" align="center" prop="sopMo" />
        <el-table-column label="客户代码" align="center" prop="custNo" />
        <el-table-column label="实际交期" align="center" prop="actualDate" width="180">
          <!--          <template #default="scope">-->
          <!--            <span>{{ parseTime(scope.row.actualDate, '{y}-{m}-{d}') }}</span>-->
          <!--          </template>-->
        </el-table-column>
        <el-table-column label="客户现场" align="center" prop="custSite" />
        <el-table-column label="设备铭牌" align="center" prop="custSn" />
        <el-table-column label="SN" align="center" prop="sn" />
        <el-table-column label="装配地点" align="center" prop="zpSite" />
        <el-table-column label="订单数量" align="center" prop="orderQty" />
        <el-table-column label="机械技师" align="center" prop="technician" />
        <el-table-column label="电气技师" align="center" prop="electrician" />
        <el-table-column label="IPQC" align="center" prop="ipqc" />
        <el-table-column label="机械工程师" align="center" prop="mechanicalEngineer" />
        <el-table-column label="电气工程师" align="center" prop="electricalEngineer" />
        <el-table-column label="NPI" align="center" prop="npi" />
        <el-table-column label="PM" align="center" prop="pm" />
        <el-table-column label="OQC" align="center" prop="oqc" />
        <el-table-column label="其他" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['sfc:zp:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['sfc:zp:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 扫描记录对话框 -->
    <el-dialog v-model="infoVisible" title="扫描记录" width="1500px" :close-on-click-modal="false" draggable>
      <el-table :data="reportTableData" border size="small" @cell-dblclick="dbclick">
        <el-table-column prop="cusName" label="产品名" width="150" />
        <el-table-column prop="moNo" label="MO号" width="150" />
        <el-table-column prop="wksDwg" label="图号" width="150" />
        <el-table-column prop="floorNo" label="楼层" width="150" />
        <el-table-column prop="empNo" label="工号" width="150" />
        <el-table-column prop="empName" label="姓名" width="150" />
        <el-table-column prop="participantNo" label="参与人员工号" width="150" />
        <el-table-column prop="participantName" label="参与人员姓名" width="150" />
        <el-table-column prop="pType" label="工站号" width="150" />
        <el-table-column prop="basGs" label="标准工时" width="150" />
        <el-table-column prop="bpRy" label="标配人力" width="150" />
        <el-table-column prop="barCode" label="条码" width="150" />
        <el-table-column prop="pName" label="工站名称" width="150" />
        <el-table-column prop="bDtm" label="开始时间" width="150" />
        <el-table-column prop="eDtm" label="结束时间" width="150" />
        <el-table-column prop="sjGs" label="实际工时" width="150" />
        <el-table-column prop="qtyOk" label="良品数量" width="150" />
        <el-table-column prop="qtyNg" label="不良数量" width="150" />
        <el-table-column prop="reasonName" label="不良原因" width="150" />
        <el-table-column prop="curProd" label="当前进度" width="150" />
        <el-table-column prop="sumProd" label="总进度" width="150" />
        <el-table-column prop="mSta" label="状态" width="150" />
        <el-table-column prop="rem" label="备注" width="150" />
        <el-table-column prop="zpType" label="装配类型" width="150" />
        <el-table-column prop="arbpl" label="ARBPL" width="150" />
        <el-table-column prop="wkGs" label="累计工时" width="150" />
      </el-table>
      <template #footer>
        <el-button @click="infoVisible = false">退出</el-button>
      </template>
    </el-dialog>
    <!-- 图库/视频对话框 -->
    <el-dialog v-model="picDialogVisible" title="图库/视频" width="1200px" :close-on-click-modal="false" draggable>
      <el-space direction="vertical" style="width: 100%" alignment="flex-normal">
        <el-space>
          <el-button :type="activeIndex === 0 ? 'primary' : ''" @click="buttonSearch(0)"> 装配图库 </el-button>
          <el-button :type="activeIndex === 1 ? 'primary' : ''" @click="buttonSearch(1)"> IPQC图库 </el-button>
          <el-button :type="activeIndex === 2 ? 'primary' : ''" @click="buttonSearch(2)"> 售后图库 </el-button>
          <el-button :type="activeIndex === 3 ? 'primary' : ''" @click="buttonSearch(3)"> OQC图库 </el-button>
          <el-button :type="activeIndex === 4 ? 'primary' : ''" @click="buttonSearch(4)"> NPI视频 </el-button>
        </el-space>
        <el-table :data="picTableData" border v-loading="picLoading" size="small">
          <el-table-column prop="moNo" label="MO号" width="150" />
          <el-table-column prop="fileFolder" label="路径" width="150" />
          <el-table-column prop="fileName" label="文件名称" width="150" />
          <el-table-column prop="link" label="预览/下载" width="200">
            <template #default="scope">
              <el-image
                v-if="isImagePath(scope.row.fileName)"
                :src="scope.row.link"
                fit="cover"
                style="width: 120px; height: 80px; cursor: pointer"
                :preview-src-list="[scope.row.link]"
                :initial-index="0"
                preview-teleported
              >
              </el-image>
              <el-link type="primary" :href="scope.row.link" target="_blank" v-else>下载</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="createBy" label="上传工号" width="150" />
          <el-table-column prop="createTime" label="上传时间" width="150" />
        </el-table>
        <el-pagination
          v-model:current-page="picPagination.current"
          v-model:page-size="picPagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="picPagination.total"
          @size-change="picPageSizeChange"
          @current-change="picPageChange"
        />
        <el-upload
          ref="uploadRef"
          v-if="enableUpload"
          :http-request="handleUpload"
          :limit="5"
          :show-file-list="false"
          :on-exceed="handleExceed"
          multiple
        >
          <el-button type="primary" :loading="isUploading" v-hasPermi="['sfc:zp:upload']">上传</el-button>
        </el-upload>
      </el-space>
      <template #footer>
        <el-button @click="picDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
    <!-- 添加或修改装配现场图像对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="zpFormRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="设备项次" prop="sopMo">
          <el-input v-model="form.sopMo" placeholder="请输入设备项次" @keyup.enter="handleSopMoEnter" />
        </el-form-item>
        <el-form-item label="MO号" prop="moNo">
          <el-input v-model="form.moNo" placeholder="请输入MO号" />
        </el-form-item>
        <el-form-item label="项目号" prop="pmNo">
          <el-input v-model="form.pmNo" placeholder="请输入项目号" />
        </el-form-item>
        <el-form-item label="设备名称" prop="deviceDesc">
          <el-input v-model="form.deviceDesc" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="客户代码" prop="custNo">
          <el-input v-model="form.custNo" placeholder="请输入客户代码" />
        </el-form-item>
        <el-form-item label="实际交期" prop="actualDate">
          <el-date-picker clearable v-model="form.actualDate" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择实际交期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="客户现场" prop="custSite">
          <el-input v-model="form.custSite" placeholder="请输入客户现场" />
        </el-form-item>
        <el-form-item label="设备铭牌" prop="custSn">
          <el-input v-model="form.custSn" placeholder="请输入设备铭牌" />
        </el-form-item>
        <el-form-item label="SN" prop="sn">
          <el-input v-model="form.sn" placeholder="请输入SN" />
        </el-form-item>
        <el-form-item label="装配地点" prop="zpSite">
          <el-input v-model="form.zpSite" placeholder="请输入装配地点" />
        </el-form-item>
        <el-form-item label="订单数量" prop="orderQty">
          <el-input v-model="form.orderQty" placeholder="请输入订单数量" />
        </el-form-item>
        <el-form-item label="机械技师" prop="technician">
          <el-input v-model="form.technician" placeholder="请输入机械技师" />
        </el-form-item>
        <el-form-item label="电气技师" prop="electrician">
          <el-input v-model="form.electrician" placeholder="请输入电气技师" />
        </el-form-item>
        <el-form-item label="IPQC" prop="ipqc">
          <el-input v-model="form.ipqc" placeholder="请输入IPQC" />
        </el-form-item>
        <el-form-item label="机械工程师" prop="mechanicalEngineer">
          <el-input v-model="form.mechanicalEngineer" placeholder="请输入机械工程师" />
        </el-form-item>
        <el-form-item label="电气工程师" prop="electricalEngineer">
          <el-input v-model="form.electricalEngineer" placeholder="请输入电气工程师" />
        </el-form-item>
        <el-form-item label="NPI" prop="npi">
          <el-input v-model="form.npi" placeholder="请输入NPI" />
        </el-form-item>
        <el-form-item label="PM" prop="pm">
          <el-input v-model="form.pm" placeholder="请输入PM" />
        </el-form-item>
        <el-form-item label="OQC" prop="oqc">
          <el-input v-model="form.oqc" placeholder="请输入OQC" />
        </el-form-item>
        <el-form-item label="其他" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入其他" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Zp" lang="ts">
import { addZp, delZp, getZp, getZpMo, listZp, queryZpPicDataList, updateZp, uploadZpMo, uploadZpPic } from '@/api/sfc/zp';
import { ZpForm, ZpQuery, ZpVO } from '@/api/sfc/zp/types';
import { getInfosByBarcodeData } from '@/api/information/sfcInfo';
import { listEmpInfo } from '@/api/sfc/empInfo';
import type { UploadInstance, UploadProps, UploadRequestOptions } from 'element-plus';
import { ElMessage } from 'element-plus';

const uploadRef = ref<UploadInstance>();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const zpList = ref<ZpVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

// 扫描记录对话框相关状态
const infoVisible = ref(false);
const reportTableData = ref<any[]>([]);

// 图库/视频对话框相关状态
const picDialogVisible = ref(false);
const picLoading = ref(false);
const enableUpload = ref(true);
const uploading = ref(false);
const currentMoNo = ref('');
const picType = ref('装配图库');
const picTableData = ref<any[]>([]);
const activeIndex = ref(0);

const picPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

const queryFormRef = ref<ElFormInstance>();
const zpFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ZpForm = {
  id: undefined,
  moNo: undefined,
  pmNo: undefined,
  deviceDesc: undefined,
  sopMo: undefined,
  custNo: undefined,
  actualDate: undefined,
  custSite: undefined,
  custSn: undefined,
  sn: undefined,
  zpSite: undefined,
  orderQty: undefined,
  technician: undefined,
  electrician: undefined,
  ipqc: undefined,
  mechanicalEngineer: undefined,
  electricalEngineer: undefined,
  npi: undefined,
  pm: undefined,
  oqc: undefined,
  remark: undefined
};
const data = reactive<PageData<ZpForm, ZpQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    moNo: undefined,
    pmNo: undefined,
    deviceDesc: undefined,
    sopMo: undefined,
    custNo: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '不能为空', trigger: 'blur' }],
    sopMo: [{ required: true, message: '设备项次不能为空', trigger: 'blur' }],
    moNo: [{ required: true, message: 'MO不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

// 添加上传队列和锁机制
const uploadQueue = ref<{ file: File; resolve: (value: any) => void; reject: (reason?: any) => void }[]>([]);
const isUploading = ref(false);

/** 查询装配现场图像列表 */
const getList = async () => {
  loading.value = true;
  const res = await listZp(queryParams.value);
  zpList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 设备项次回车事件处理 */
const handleSopMoEnter = async () => {
  if (!form.value.sopMo) {
    ElMessage.warning('请输入设备项次');
    return;
  }

  try {
    const res = await getZpMo(form.value.sopMo);
    const record = res.data;
    if (record) {
      form.value.pmNo = record.pmNo;
      form.value.moNo = record.moNo;
      form.value.deviceDesc = record.deviceDesc;
      form.value.custNo = record.custNo;
      form.value.actualDate = record.actualDate;
      form.value.custSite = record.custSite;
      form.value.custSn = record.custSn;
      form.value.sn = record.sn;
      form.value.zpSite = record.zpSite;
      form.value.orderQty = record.orderQty;
      form.value.pm = record.pm;
      form.value.mechanicalEngineer = record.mechanicalEngineer;
    } else {
      ElMessage.warning('未找到相关设备信息');
    }
  } catch (error) {
    ElMessage.error('获取设备信息失败');
    console.error(error);
  }
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  zpFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: ZpVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加装配现场图像';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: ZpVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getZp(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改装配现场图像';
};

/** 提交按钮 */
const submitForm = () => {
  zpFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      try {
        if (form.value.id) {
          await updateZp(form.value);
        } else {
          await addZp(form.value);
        }
        proxy?.$modal.msgSuccess('操作成功');
        dialog.visible = false;
        await getList();
      } catch (error) {
        console.error(error);
        ElMessage.error('操作失败');
      } finally {
        buttonLoading.value = false;
      }
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: ZpVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除装配现场图像编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delZp(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'sfc/zp/export',
    {
      ...queryParams.value
    },
    `zp_${new Date().getTime()}.xlsx`
  );
};

/** 导出模板按钮操作 */
const exportTemplate = () => {
  proxy?.download('/sfc/zp/export/zpTemplate', {}, `导入模板.xlsx`);
};

/** 批量导入文件上传 */
const uploadMoFile = async (options: UploadRequestOptions) => {
  const formData = new FormData();
  formData.append('file', options.file);
  try {
    const res = await uploadZpMo(formData);
    ElMessage.info(res.msg);
    getList();
  } catch (error) {
    ElMessage.error(`上传失败 ${error}`);
  }
};

/** 上传前验证文件类型 */
const beforeUpload: UploadProps['beforeUpload'] = (file: File) => {
  if (!file.name.endsWith('.xls') && !file.name.endsWith('.xlsx')) {
    ElMessage.error('只能上传Excel文件');
    return false;
  }
  return true;
};

/** 显示扫描记录对话框 */
const showScanRecord = async (sopMo: string) => {
  infoVisible.value = true;
  try {
    const res = await getInfosByBarcodeData({ sopMo });
    reportTableData.value = res.data;
  } catch (error) {
    ElMessage.error('获取扫描记录失败');
    console.error(error);
  }
};

/** 显示图库/视频对话框 */
const showPicDialog = () => {
  if (ids.value.length === 0) {
    ElMessage.warning('请先选择一条记录');
    return;
  }

  picDialogVisible.value = true;
  const selectedRow = zpList.value.find((item) => item.id === ids.value[0]);
  if (selectedRow) {
    currentMoNo.value = selectedRow.sopMo || selectedRow.moNo || '';
    setActive(0);
    searchPics('装配图库');
  }
};

/** 设置当前活动类型 */
const setActive = (index: number) => {
  activeIndex.value = index;
};

/** 图库分页大小改变 */
const picPageSizeChange = (pageSize: number) => {
  picPagination.pageSize = pageSize;
  searchPics(picType.value);
};

/** 图库页码改变 */
const picPageChange = (current: number) => {
  picPagination.current = current;
  searchPics(picType.value);
};

/** 查询图库数据 */
const searchPics = async (type: string) => {
  picLoading.value = true;
  const page = {
    page: picPagination.current,
    size: picPagination.pageSize
  };
  const params = {
    moNo: currentMoNo.value,
    fileFolder: type,
    ...page
  };

  try {
    const res = await queryZpPicDataList(params);
    picTableData.value = res.rows;
    picPagination.total = res.total;
  } catch (error) {
    ElMessage.error(`查询失败: ${error}`);
  } finally {
    picLoading.value = false;
  }
};

/** 判断是否为图片路径 */
const isImagePath = (path: string) => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff'];
  const ext = path.split('.').pop()?.toLowerCase();
  return ext !== undefined && imageExtensions.includes(ext);
};

/** 处理图片链接 */
const pciLink = (link: string) => {
  try {
    const parsedUrl = new URL(link);
    const path = parsedUrl.pathname;
    return `${path}`;
  } catch (error) {
    console.error('Invalid URL:', error);
    return link;
  }
};

const picTypeMap = new Map<number, string>();

picTypeMap.set(0, '装配图库');
picTypeMap.set(1, 'IPQC图库');
picTypeMap.set(2, '售后图库');
picTypeMap.set(3, 'OQC图库');
picTypeMap.set(4, 'NPI视频');

/** 类型切换按钮点击 */
const buttonSearch = (type: number) => {
  setActive(type);
  const typeName = picTypeMap.get(type) || '装配图库';
  picType.value = typeName;
  searchPics(typeName);
};

/** 处理上传队列 */
const processUploadQueue = async () => {
  if (isUploading.value || uploadQueue.value.length === 0) return;

  isUploading.value = true;

  try {
    while (uploadQueue.value.length > 0) {
      const task = uploadQueue.value.shift();
      if (!task) continue;

      const { file, resolve, reject } = task;

      const formData = new FormData();
      formData.append('file', file);
      formData.append('picType', picType.value);
      formData.append('moNo', currentMoNo.value);

      try {
        const res = await uploadZpPic(formData);
        ElMessage.success(`文件 ${file.name} 上传成功`);
        resolve(res);
      } catch (error) {
        ElMessage.error(`文件 ${file.name} 上传失败: ${error}`);
        reject(error);
      }

      // 如果队列中还有任务，等待1秒再处理下一个
      if (uploadQueue.value.length > 0) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }
  } finally {
    isUploading.value = false;
    uploadRef.value?.clearFiles();
    // 上传完成后刷新列表
    searchPics(picType.value);
  }
};

/** 添加到上传队列 */
const addToUploadQueue = (file: File): Promise<any> => {
  return new Promise((resolve, reject) => {
    uploadQueue.value.push({ file, resolve, reject });
    // 触发队列处理
    processUploadQueue();
  });
};

/** 处理文件上传 */
const handleUpload = async (options: UploadRequestOptions) => {
  if (!options.file) return;

  try {
    if (options.file instanceof File) {
      await addToUploadQueue(options.file);
    } else {
      ElMessage.error('不支持的文件类型');
    }
  } finally {
    // 最后一个文件上传完成后，uploading状态会在processUploadQueue中被重置
    // 这里不能立即重置uploading状态
  }
};

const handleExceed: UploadProps['onExceed'] = (files, uploadFiles) => {
  ElMessage.warning(`一次最多上传5张图片, 已选${files.length}张`);
};

/** 双击单元格事件处理 */
const dbclick = async (row: any, column: any) => {
  if (column.property === 'sopMo') {
    await showScanRecord(row.sopMo);
  } else if (
    ['technician', 'electrician', 'ipqc', 'mechanicalEngineer', 'electricalEngineer', 'npi', 'pm', 'oqc', 'empNo'].includes(column.property)
  ) {
    await showEmpInfo(row[column.property]);
  }
};

/** 显示员工信息 */
async function showEmpInfo(data: string) {
  const empNo = data.match(/^\d+/);
  if (!empNo) {
    ElMessage.error('工号无效，未包含数字部分');
    return;
  }

  try {
    const res = await listEmpInfo({
      pageNum: 1,
      pageSize: 10,
      empNo: empNo[0]
    });

    if (res.rows.length > 0) {
      const empInfo = res.rows[0];
      ElMessage.success(`姓名: ${empInfo.empName}, 电话号码: ${empInfo.phone}`);
    } else {
      ElMessage.warning('该人员信息不存在');
    }
  } catch (error) {
    ElMessage.error('获取员工信息失败');
    console.error(error);
  }
}

onMounted(() => {
  getList();
});
</script>

<style scoped></style>
