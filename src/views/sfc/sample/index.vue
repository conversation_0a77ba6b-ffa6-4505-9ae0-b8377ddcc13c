<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="状态" prop="moState">
              <el-input v-model="queryParams.moState" placeholder="请输入状态" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="图号" prop="drawNumber">
              <el-input v-model="queryParams.drawNumber" placeholder="请输入图号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="下单员" prop="orderSalm">
              <el-input v-model="queryParams.orderSalm" placeholder="请输入下单员" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['sfc:sample:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['sfc:sample:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['sfc:sample:remove']"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['sfc:sample:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="sampleList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="" align="center" prop="id" v-if="true" />
        <el-table-column label="状态" align="center" prop="moState" />
        <el-table-column label="末工序时间" align="center" prop="lastOpTime" />
        <el-table-column label="客户代码" align="center" prop="custNo" />
        <el-table-column label="客户PO" align="center" prop="poNo" />
        <el-table-column label="图号" align="center" prop="drawNumber" />
        <el-table-column label="订单类别" align="center" prop="orderType" />
        <el-table-column label="材料" align="center" prop="material" />
        <el-table-column label="MO号" align="center" prop="moNo" />
        <el-table-column label="订单数量" align="center" prop="orderQty" />
        <el-table-column label="制造部门" align="center" prop="mfgDept" />
        <el-table-column label="当前工序" align="center" prop="currentProcess" />
        <el-table-column label="问题点" align="center" prop="issue" />
        <el-table-column label="解决方案" align="center" prop="solution" />
        <el-table-column label="完成时间" align="center" prop="completionTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.completionTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="责任人" align="center" prop="owner" />
        <el-table-column label="结果" align="center" prop="result" />
        <el-table-column label="接单日期" align="center" prop="bargainDate" />
        <el-table-column label="客户交期" align="center" prop="deliveryDate" />
        <el-table-column label="PMC交期" align="center" prop="pmcReqDate" />
        <el-table-column label="下单员" align="center" prop="orderSalm" />
        <el-table-column label="项目负责" align="center" prop="projectLeader" />
        <el-table-column label="计划类型" align="center" prop="planType" />
        <el-table-column label="订单状态" align="center" prop="status" />
        <el-table-column label="图片示例" align="center" prop="imagePathUrl" width="100">
          <template #default="scope">
            <image-preview :src="scope.row.imagePathUrl" :width="50" :height="50" />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['sfc:sample:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['sfc:sample:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改样品清单维护对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="sampleFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="状态" prop="moState">
          <el-input v-model="form.moState" placeholder="请输入状态" />
        </el-form-item>
        <el-form-item label="末工序时间" prop="lastOpTime">
          <el-input v-model="form.lastOpTime" placeholder="请输入末工序时间" />
        </el-form-item>
        <el-form-item label="客户代码" prop="custNo">
          <el-input v-model="form.custNo" placeholder="请输入客户代码" />
        </el-form-item>
        <el-form-item label="客户PO" prop="poNo">
          <el-input v-model="form.poNo" placeholder="请输入客户PO" />
        </el-form-item>
        <el-form-item label="图号" prop="drawNumber">
          <el-input v-model="form.drawNumber" placeholder="请输入图号" />
        </el-form-item>
        <el-form-item label="材料" prop="material">
          <el-input v-model="form.material" placeholder="请输入材料" />
        </el-form-item>
        <el-form-item label="MO号" prop="moNo">
          <el-input v-model="form.moNo" placeholder="请输入MO号" />
        </el-form-item>
        <el-form-item label="订单数量" prop="orderQty">
          <el-input v-model="form.orderQty" placeholder="请输入订单数量" />
        </el-form-item>
        <el-form-item label="制造部门" prop="mfgDept">
          <el-input v-model="form.mfgDept" placeholder="请输入制造部门" />
        </el-form-item>
        <el-form-item label="当前工序" prop="currentProcess">
          <el-input v-model="form.currentProcess" placeholder="请输入当前工序" />
        </el-form-item>
        <el-form-item label="问题点" prop="issue">
          <el-input v-model="form.issue" placeholder="请输入问题点" />
        </el-form-item>
        <el-form-item label="解决方案" prop="solution">
          <el-input v-model="form.solution" placeholder="请输入解决方案" />
        </el-form-item>
        <el-form-item label="完成时间" prop="completionTime">
          <el-date-picker clearable v-model="form.completionTime" type="datetime" value-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择完成时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="责任人" prop="owner">
          <el-input v-model="form.owner" placeholder="请输入责任人" />
        </el-form-item>
        <el-form-item label="结果" prop="result">
          <el-input v-model="form.result" placeholder="请输入结果" />
        </el-form-item>
        <el-form-item label="接单日期" prop="bargainDate">
          <el-input v-model="form.bargainDate" placeholder="请输入接单日期" />
        </el-form-item>
        <el-form-item label="客户交期" prop="deliveryDate">
          <el-input v-model="form.deliveryDate" placeholder="请输入客户交期" />
        </el-form-item>
        <el-form-item label="PMC交期" prop="pmcReqDate">
          <el-input v-model="form.pmcReqDate" placeholder="请输入PMC交期" />
        </el-form-item>
        <el-form-item label="下单员" prop="orderSalm">
          <el-input v-model="form.orderSalm" placeholder="请输入下单员" />
        </el-form-item>
        <el-form-item label="项目负责" prop="projectLeader">
          <el-input v-model="form.projectLeader" placeholder="请输入项目负责" />
        </el-form-item>
        <el-form-item label="计划类型" prop="planType">
          <el-input v-model="form.planType" placeholder="请输入计划类型" />
        </el-form-item>
        <el-form-item label="订单状态" prop="status">
          <el-input v-model="form.status" placeholder="请输入订单状态" />
        </el-form-item>
        <el-form-item label="图片示例" prop="imagePath">
          <image-upload :limit="1" v-model="form.imagePath" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Sample" lang="ts">
import { addSample, delSample, getSample, listSample, updateSample } from '@/api/sfc/sample';
import { SampleForm, SampleQuery, SampleVO } from '@/api/sfc/sample/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const sampleList = ref<SampleVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const sampleFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: SampleForm = {
  id: undefined,
  moState: undefined,
  lastOpTime: undefined,
  custNo: undefined,
  poNo: undefined,
  drawNumber: undefined,
  orderType: undefined,
  material: undefined,
  moNo: undefined,
  orderQty: undefined,
  mfgDept: undefined,
  currentProcess: undefined,
  issue: undefined,
  solution: undefined,
  completionTime: undefined,
  owner: undefined,
  result: undefined,
  bargainDate: undefined,
  deliveryDate: undefined,
  orderSalm: undefined,
  projectLeader: undefined,
  planType: undefined,
  status: undefined,
  imagePath: undefined,
  remark: undefined
};
const data = reactive<PageData<SampleForm, SampleQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    moState: undefined,
    drawNumber: undefined,
    orderType: undefined,
    orderSalm: undefined,
    params: {}
  },
  rules: {
    id: [{ required: true, message: '不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询样品清单维护列表 */
const getList = async () => {
  loading.value = true;
  const res = await listSample(queryParams.value);
  sampleList.value = res.rows;
  total.value = res.total;
  loading.value = false;
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  sampleFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: SampleVO[]) => {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加样品清单维护';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: SampleVO) => {
  reset();
  const _id = row?.id || ids.value[0];
  const res = await getSample(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改样品清单维护';
};

/** 提交按钮 */
const submitForm = () => {
  sampleFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateSample(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addSample(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: SampleVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除样品清单维护编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
  await delSample(_ids);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download(
    'sfc/sample/export',
    {
      ...queryParams.value
    },
    `sample_${new Date().getTime()}.xlsx`
  );
};

onMounted(() => {
  getList();
});
</script>
