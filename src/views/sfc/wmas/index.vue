<template>
  <div class="p-2">
    <el-card>
      <el-dialog v-model="modalVisible" title="工序信息" width="auto" :close-on-click-modal="false" draggable>
        <el-table :data="reportTableData" border>
          <el-table-column prop="vornr" label="工序号"></el-table-column>
          <el-table-column prop="ltxa1" label="工序名称"></el-table-column>
          <el-table-column prop="vgw02" label="总工时">
            <template #default="scope">
              <span>{{ scope.row.vgw02 / 60 }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-show="paginationModal.total > 0"
          v-model:current-page="paginationModal.current"
          v-model:page-size="paginationModal.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next"
          :total="paginationModal.total"
          @size-change="pageSizeChangeModal"
          @current-change="pageChangeModal"
        />
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="modalVisible = false">退出</el-button>
          </span>
        </template>
      </el-dialog>

      <div class="search-container">
        <el-card shadow="hover" class="search-card">
          <el-form :inline="true" class="search-form">
            <table class="search-table">
              <tbody>
                <tr>
                  <td rowspan="2" class="search-label">查询条件</td>
                  <td>
                    <div class="cell-container">
                      <span>结案状态</span>
                      <el-select v-model="moState" placeholder="选择结案状态">
                        <el-option label="N" value="N" />
                      </el-select>
                    </div>
                  </td>
                  <td>
                    <div class="cell-container">
                      <span>项目分类</span>
                      <el-select v-model="projectType" placeholder="选择项目分类">
                        <el-option label="精密零件和模组" value="精密零件和模组" />
                        <!--                        <el-option label="设备" value="设备" />-->
                        <!--                        <el-option label="精密配件" value="精密配件" />-->
                        <!--                        <el-option label="其他" value="其他" />-->
                      </el-select>
                    </div>
                  </td>
                  <td>
                    <div class="cell-container">
                      <span>客户代码</span>
                      <el-input v-model="custNo" placeholder="" clearable />
                    </div>
                  </td>
                  <td>
                    <div class="cell-container">
                      <span>MO号</span>
                      <el-input v-model="moNo" clearable />
                    </div>
                  </td>
                  <td>
                    <div class="cell-container">
                      <el-button type="primary" @click="buttonSearch">搜索</el-button>
                      <el-button type="primary" @click="reset">重置搜索</el-button>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2">
                    <div class="cell-container">
                      <span>接单日期</span>
                      <el-date-picker
                        v-model="rangeValue"
                        type="datetimerange"
                        format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss"
                        @change="onChange"
                      />
                    </div>
                  </td>
                  <td>
                    <div class="cell-container">
                      <span>订单类别</span>
                      <el-input v-model="orderType" clearable />
                    </div>
                  </td>
                  <td>
                    <div class="cell-container">
                      <span>图号</span>
                      <el-input v-model="drawNumber" clearable />
                    </div>
                  </td>
                  <td>
                    <div class="cell-container">
                      <el-button type="primary" @click="showPicBrowser">调取图纸</el-button>
                      <!--                    <el-button type="primary" @click="showPicPdf">调取图纸(PDF)</el-button>-->
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </el-form>
        </el-card>
      </div>

      <el-table
        :data="paginatedData"
        border
        style="width: 100%"
        size="default"
        :row-key="(row) => row.moNo"
        @row-dblclick="recordClick"
        v-loading="loading"
        @selection-change="handleRowSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" />
        <el-table-column prop="moState" label="订单状态" />
        <el-table-column prop="lastOpTime" label="末工序时间" />
        <el-table-column prop="custNo" label="客户代码" />
        <el-table-column prop="poNo" label="客户PO" />
        <el-table-column prop="drawNumber" label="图号" />
        <el-table-column prop="orderType" label="订单类别" />
        <el-table-column prop="material" label="材料" />
        <el-table-column prop="moNo" label="MO号" />
        <el-table-column prop="orderQty" label="订单数量" />
        <el-table-column prop="mfgDept" label="制造部门" />
        <el-table-column prop="currentProcess" label="当前工序" />
        <el-table-column prop="bargainDate" label="接单日期" />
        <el-table-column prop="deliveryDate" label="客户交期" />
        <el-table-column prop="pmcReqDate" label="PMC交期" />
        <el-table-column prop="orderSalm" label="下单员" />
      </el-table>

      <el-pagination
        v-show="pagination.total > 0"
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next"
        :total="pagination.total"
        @size-change="pageSizeChange"
        @current-change="pageChange"
      />

      <div style="margin-top: 15px; display: flex; gap: 10px">
        <el-tooltip content="保存勾选的记录" placement="bottom">
          <el-button type="primary" @click="saveSelect">保存</el-button>
        </el-tooltip>
        <el-tooltip content="保存查找出来的所有记录" placement="bottom">
          <el-button type="primary" @click="saveAll">全部保存</el-button>
        </el-tooltip>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { SfcInfoModel } from '@/api/information/sfcInfo';
import { getPicUrl, reportGx, saveWmasReport, searchWmasData } from '@/api/sfc/wmas';

const loading = ref(false);
const downLoading = ref(false);
const exportLoading = ref(false);
const moState = ref('N');
const projectType = ref('精密零件和模组');
const modalVisible = ref(false);
const moNo = ref('');
const drawNumber = ref('');
const custNo = ref('');
const orderType = ref('');
const rangeValue = ref([]);
const tableData = ref<SfcInfoModel[]>([]);
const allTableData = ref<SfcInfoModel[]>([]); // 存储所有数据
const reportTableHead = ref<any[]>([]);
const reportTableData = ref<any[]>([]);
const selectedRowKeys = ref<string[]>([]);

// 计算属性：获取当前页的数据
const paginatedData = computed(() => {
  const start = (pagination.current - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;
  return allTableData.value.slice(start, end);
});

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

const pageChange = (cur) => {
  pagination.current = cur;
  // 不再调用search，前端分页直接更新当前页码
};

const pageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize;
  // 改变每页数量时，重置到第一页
  pagination.current = 1;
};

// 查询按钮
const buttonSearch = () => {
  pagination.current = 1;
  search();
};

const search = async () => {
  loading.value = true;
  // 前端分页不需要传递分页参数
  const initParm = {
    moState: moState.value,
    projectType: projectType.value,
    custNo: custNo.value,
    orderType: orderType.value,
    moNo: moNo.value,
    drawNumber: drawNumber.value,
    rangeValue: rangeValue.value === undefined ? '' : rangeValue.value
  };

  await searchWmasData(initParm)
    .then((res) => {
      allTableData.value = res.data; // 保存所有数据
      pagination.total = allTableData.value.length; // 设置总数量
    })
    .catch((error) => {
      ElMessage.error(error);
    })
    .finally(() => {
      loading.value = false;
    });
};

// 处理行选择变化的回调
const handleRowSelectionChange = (selection) => {
  console.log('Selected rows:', selection);
  selectedRowKeys.value = selection.map((row) => row.moNo);
};

const reset = () => {
  moState.value = 'N';
  projectType.value = '精密零件和模组';
  custNo.value = '';
  orderType.value = '';
  moNo.value = '';
  drawNumber.value = '';
  rangeValue.value = [];
  tableData.value = [];
};

function onChange(val) {
  console.log('onChange: ', val);
}

const paginationModal = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

const pageChangeModal = (cur) => {
  paginationModal.current = cur;
};

const pageSizeChangeModal = (pageSize) => {
  paginationModal.pageSize = pageSize;
};

const recordClick = (row) => {
  console.log(row);
  reportGx(row.moNo).then((res) => {
    reportTableData.value = res.data;
    paginationModal.total = reportTableData.value.length;
  });
  modalVisible.value = true;
};

const showPicBrowser = async () => {
  if (selectedRowKeys.value.length === 0) {
    return;
  }
  console.log(selectedRowKeys.value);
  // url = "http://192.168.123.22:8018/" & @MO号 & ".pdf"
  const key = selectedRowKeys.value[0];
  await getPicUrl(key)
    .then((res) => {
      if (res.data === 'DATA_NOT_FOUND') {
        window.open(`https://tuku.world-machining.com/sap_drawing/${key}.pdf`, '_blank');
      } else {
        window.open(res.data, '_blank');
      }
    })
    .catch((error) => {
      ElMessage.error(error);
    });
};

const saveSelect = async () => {
  if (paginatedData.value.length === 0 || selectedRowKeys.value.length === 0) {
    return;
  }
  // 筛选出选中的的记录
  const data = paginatedData.value.filter((row) => selectedRowKeys.value.includes(row.moNo));
  await saveWmasReport(data)
    .then((res) => {
      ElMessage.success(res.msg);
    })
    .catch((error) => {
      ElMessage.error(error);
    });
};

const saveAll = async () => {
  if (allTableData.value.length === 0) {
    return;
  }
  await saveWmasReport(allTableData.value)
    .then((res) => {
      ElMessage.success(res.msg);
    })
    .catch((error) => {
      ElMessage.error(error);
    });
};

onMounted(() => {});
</script>

<style scoped>
.search-container {
  margin-bottom: 20px;
}

.search-card :deep(.el-card__body) {
  padding: 10px;
}

.search-form {
  width: 100%;
}

.search-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.search-table td {
  padding: 10px;
  border: 1px solid #dcdfe6;
  vertical-align: middle;
}

.search-label {
  width: 100px;
  text-align: center;
  font-weight: bold;
  background-color: #f5f7fa;
}

.cell-container {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  min-width: 80px;
}

.cell-container span {
  margin-right: 10px;
  font-weight: 500;
  min-width: 60px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-select) {
  width: 150px !important;
}

:deep(.el-input) {
  width: 150px !important;
}

:deep(.el-date-editor) {
  width: 370px !important;
}

:deep(.el-date-editor.el-input__wrapper) {
  width: 370px !important;
}

:deep(.el-select .el-input) {
  width: 150px !important;
}

:deep(.el-select__wrapper) {
  width: 100% !important;
}

:deep(.el-select .el-input__wrapper) {
  width: 100% !important;
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
}
</style>
