{"route": {"dashboard": "Dashboard", "document": "Document"}, "login": {"selectPlaceholder": "Please select/enter a company name", "username": "Username", "password": "Password", "login": "<PERSON><PERSON>", "logging": "Logging...", "code": "Verification Code", "rememberPassword": "Remember me", "switchRegisterPage": "Sign up now", "rule": {"tenantId": {"required": "Please enter your tenant id"}, "username": {"required": "Please enter your account"}, "password": {"required": "Please enter your password"}, "code": {"required": "Please enter a verification code"}}, "social": {"wechat": "<PERSON><PERSON><PERSON>", "maxkey": "<PERSON><PERSON><PERSON>", "topiam": "Top<PERSON>am <PERSON>", "gitee": "<PERSON><PERSON><PERSON>", "github": "<PERSON><PERSON><PERSON>"}}, "register": {"selectPlaceholder": "Please select/enter a company name", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "register": "Register", "registering": "Registering...", "registerSuccess": "Congratulations, your {username} account has been registered!", "code": "Verification Code", "switchLoginPage": "Log in with an existing account", "rule": {"tenantId": {"required": "Please enter your tenant id"}, "username": {"required": "Please enter your account", "length": "The length of the user account must be between {min} and {max}"}, "password": {"required": "Please enter your password", "length": "The user password must be between {min} and {max} in length", "pattern": "Can't contain illegal characters: {strings}"}, "code": {"required": "Please enter a verification code"}, "confirmPassword": {"required": "Please enter your password again", "equalToPassword": "The password entered twice is inconsistent"}}}, "navbar": {"full": "Full Screen", "language": "Language", "dashboard": "Dashboard", "document": "Document", "message": "Message", "layoutSize": "Layout Size", "selectTenant": "Select Tenant", "layoutSetting": "Layout Setting", "personalCenter": "Personal Center", "logout": "Logout"}, "check": {"step": "Please complete the first and second steps first", "select": "Please select", "input": "Please enter"}, "system": {"dept": {"deptName": "Department Name", "enterDeptName": "Please enter department name", "deptCategory": "Category Code", "enterDeptCategory": "Please enter category code", "status": "Status", "departmentStatus": "Department Status", "search": "Search", "reset": "Reset", "startDate": "Start Date", "endDate": "End Date", "refresh": "Reset", "operation": "Operation", "add": "Add", "edit": "Edit", "delete": "Delete", "more": "More", "export": "Export Data", "import": "Import Data", "downloadTemplate": "Download Template", "expandCollapse": "Expand/Collapse", "parentDept": "Parent Department", "selectParentDept": "Please select parent department", "order": "Order", "orderRequired": "Order cannot be empty", "leader": "Leader", "selectLeader": "Please select leader", "phone": "Phone", "enterPhone": "Please enter phone number", "phoneInvalid": "Please enter a valid phone number", "email": "Email", "enterEmail": "Please enter email", "emailInvalid": "Please enter a valid email address", "deptStatus": "Department Status", "submit": "Submit", "cancel": "Cancel", "createTime": "CreateTime", "updateTime": "UpdateTime", "deptCategoryRequired": "Category code cannot be empty", "leaderRequired": "Leader cannot be empty", "phoneRequired": "Phone number cannot be empty", "emailRequired": "Email cannot be empty", "deptStatusRequired": "Department status cannot be empty", "addDept": "Add Department", "editDept": "Edit Department", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "confirmDelete": "Are you sure you want to delete the department named \"{0}\"?", "deleteSuccess": "Department deleted successfully", "deleteFailed": "Failed to delete department", "parentDeptRequired": "Parent department cannot be empty", "deptNameRequired": "Department name cannot be empty", "fetchDeptListError": "Fetch Dept List Error", "fetchUsersError": "Fetch Users Error", "fetchDeptOptionsError": "Fetch Dept Options Error", "fetchDeptDataError": "Fetch Dept Data Error", "submitFormError": "Submit Form Error"}}, "message": {"system": {"id": "ID", "enterDeptName": "Enter department name", "enterUserName": "Enter user ID", "enterPhonenumber": "Enter phone number", "enterUserCode": "Enter user code", "userStatus": "User Status", "startDate": "Start Date", "endDate": "End Date", "search": "Search", "refresh": "Reset", "add": "Add", "edit": "Edit", "delete": "Delete", "more": "More", "export": "Export Data", "import": "Import Data", "downloadTemplate": "Download Template", "uploadText": "Drag the file here or <em>click to upload</em>", "updateSupport": "Update existing user data", "fileFormat": "Only xls, xlsx files are allowed.", "userNameRequired": "User ID is required", "userNameLength": "User ID length must be between 2 and 20", "passwordLength": "PassWord ID length must be between 2 and 20", "nickNameRequired": "Name is required", "passwordPattern": "PassWord format is incorrect", "emailFormat": "Please enter a valid email address", "phonenumberFormat": "Please enter a valid phone number", "roleIdsRequired": "User roles are required", "confirmDelete": "Are you sure to delete the item  \"{0}\"?", "confirmEnableDisable": "Confirm to \"{0}\" \"{1}\" user?", "enableDisableSuccess": "\"{0}\" successfully", "prompt": "Prompt", "newPassword": "Enter the new password for \"{0}\"", "resetSuccess": "Password reset successfully, new password is: \"{0}\"", "success": "Operation succeeded", "importResult": "Import Result", "createTime": "CreateTime", "nickName": "Name", "sex": "Sex", "post": "Post", "role": "Role", "remark": "备注", "userName": "UserName", "userCode": "UserCode", "email": "Email", "enterNickName": "Enter name", "enterEmail": "Enter email", "deptName": "Department", "phonenumber": "Phone", "status": "Status", "action": "Action", "selectDept": "Please select a department", "select": "Please select", "enterContent": "Please enter content", "selectPost": "Please select a post", "selectRole": "Please select roles", "submit": "Submit", "cancel": "Cancel", "resetPwd": "Reset Password", "authRole": "Authorize Role", "upload": "Upload", "enable": "Enable", "disable": "Disable"}}, "barcode": {"sopDebug": {"moNumber": "MO", "enterMoNumber": "Please enter MO", "quantity": "Quantity", "enterQuantity": "Please enter Quantity", "dwgno": "DWG Number", "enterDwgNo": "Please enter DWG Number", "generateBarcode": "Generate Barcode", "export": "Export", "debugProcessNo": "Debug Process No", "enterDebugProcessNo": "Please enter Debug Process No", "debugProcessName": "Debug Process Name", "enterDebugProcessName": "Please enter Debug Process Name", "standardHours": "Standard Hours", "enterStandardHours": "Please enter Standard Hours", "remark": "Remark", "enterRemark": "Please enter Remark", "fileName": "File Name", "enterFileName": "Please enter File Name", "operatorId": "Operator ID", "enterOperatorId": "Please enter Operator ID", "operatorName": "Operator Name", "enterOperatorName": "Please enter Operator Name", "host": "Host", "enterHost": "Please enter Host", "operationTime": "Operation Time", "selectOperationTime": "Please select Operation Time", "flag": "Flag", "enterFlag": "Please enter Flag", "submit": "Submit", "cancel": "Cancel", "addDebuggingSOPSetting": "Add Debugging SOP Setting", "editDebuggingSOPSetting": "Edit Debugging SOP Setting", "operationSuccess": "Operation successful", "deleteSuccess": "Delete successful", "idRequired": "Id is not null", "debugProcessNoRequired": "Debug Process Not Required", "debugProcessNameRequired": "Debug Process Name Required", "standardHoursRequired": "Standard Hours Required", "remarkRequired": "Remark Required", "fileNameRequired": "File Name Required", "confirmDelete": "Are you sure you want to delete the debugging SOP setting with ID \"{0}\"?"}, "ioSop": {"importSOP": "Import SOP", "moNumber": "MO", "enterMoNumber": "Enter MO", "quantity": "Quantity", "dwgno": "DWG Number", "enterDwgNo": "Enter DWG Number", "generateBarcode": "Generate Barcode", "export": "Export", "debugProcessNo": "Debugging Process No.", "debugProcessName": "Debugging Process Name", "standardHours": "Standard Hours", "fileName": "File Name", "remark": "Remark", "operatorId": "Operator ID", "operatorName": "Operator Name", "host": "Host", "operationTime": "Operation Time", "flag": "Flag", "addDebuggingSOPSetting": "Add Debugging SOP Setting", "editDebuggingSOPSetting": "Edit Debugging SOP Setting", "operationSuccess": "Operation Successful", "deleteSuccess": "Deletion Successful", "enterDebugProcessNo": "Enter Debugging Process Number", "enterDebugProcessName": "Enter Debugging Process Name", "enterStandardHours": "Enter Standard Hours", "enterFileName": "Enter File Name", "enterRemark": "Enter Remark", "enterOperatorId": "Enter Operator ID", "enterOperatorName": "Enter Operator Name", "enterHost": "Enter Host", "selectOperationTime": "Select Operation Time", "enterFlag": "Enter Flag", "submit": "Submit", "cancel": "Cancel", "importTitle": "Import SOP", "dragText": "Drag file here, or <em>click to upload</em>", "uploadTip": "Only xls, xlsx files are allowed.", "idRequired": "Id is not null", "debugProcessNoRequired": "Debug Process Not Required", "debugProcessNameRequired": "Debug Process Name Required", "standardHoursRequired": "Standard Hours Required", "remarkRequired": "Remark Required", "fileNameRequired": "File Name Required", "importResult": "Import Result"}}}