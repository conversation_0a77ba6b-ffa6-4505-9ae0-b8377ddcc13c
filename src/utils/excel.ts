import ExcelJS from 'exceljs';
import { ElLoading } from 'element-plus';

/**
 * 将JSON数据导出为Excel文件
 * @param data 要导出的数据数组
 * @param headers 表头配置，格式：{ key: '显示名称' }
 * @param fileName 文件名（不包含扩展名）
 * @param sheetName 工作表名称
 * @param headerColor 表头背景颜色（十六进制，如：'4472C4'）
 */
export async function exportToExcel(
  data: any[],
  headers: Record<string, string>,
  fileName: string = 'export',
  sheetName: string = 'Sheet1',
  headerColor: string = '4472C4' // 默认蓝色
) {
  const loading = ElLoading.service({
    text: '正在生成Excel文件...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    // 如果没有数据，显示提示
    if (!data || data.length === 0) {
      throw new Error('没有数据可以导出');
    }

    // 创建工作簿
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(sheetName);

    // 准备表头
    const headerKeys = Object.keys(headers);
    const headerValues = headerKeys.map(key => headers[key]);

    // 添加表头行
    const headerRow = worksheet.addRow(headerValues);

    // 设置表头样式
    headerRow.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FF' + headerColor }
      };
      cell.font = {
        bold: true,
        color: { argb: 'FFFFFFFF' },
        size: 12
      };
      cell.alignment = {
        horizontal: 'center',
        vertical: 'middle'
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // 添加数据行
    data.forEach(row => {
      const rowData = headerKeys.map(key => {
        const value = row[key];
        // 正确处理0值，只有undefined、null才转换为空字符串
        return value !== undefined && value !== null ? value : '';
      });
      const dataRow = worksheet.addRow(rowData);

      // 为数据行添加边框和对齐
      dataRow.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
        cell.alignment = {
          vertical: 'middle'
        };
      });
    });

    // 自动调整列宽
    headerKeys.forEach((key, index) => {
      const column = worksheet.getColumn(index + 1);
      const headerLength = headers[key].length;
      const maxDataLength = Math.max(
        ...data.map(row => {
          const value = row[key];
          // 正确处理0值，只有undefined、null才转换为空字符串
          const displayValue = value !== undefined && value !== null ? value : '';
          return String(displayValue).length;
        })
      );
      column.width = Math.max(headerLength, maxDataLength, 10) + 2;
    });

    // 生成Excel文件
    const buffer = await workbook.xlsx.writeBuffer();

    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${fileName}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    loading.close();
  } catch (error) {
    loading.close();
    throw error;
  }
}

/**
 * 将表格数据导出为Excel（简化版本）
 * @param data 表格数据
 * @param fileName 文件名
 * @param headerColor 表头背景颜色（十六进制，如：'4472C4'）
 */
export async function exportTableToExcel(
  data: any[],
  fileName: string = 'table_export',
  headerColor: string = '4472C4'
) {
  if (!data || data.length === 0) {
    throw new Error('没有数据可以导出');
  }

  // 自动生成表头（使用数据的所有键）
  const headers: Record<string, string> = {};
  const firstRow = data[0];
  Object.keys(firstRow).forEach(key => {
    headers[key] = key;
  });

  await exportToExcel(data, headers, fileName, 'Sheet1', headerColor);
}
