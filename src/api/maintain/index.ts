import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { MasterSnModel } from '@/api/maintain/types';

export const getMolist = (moNo: string): AxiosPromise<MasterSnModel[]> => {
    return request({
      url: '/sfc/masterSn/list',
      method: 'get',
      params: {moNo : moNo}
    });
  };

  export const saveCusSn = (query: MasterSnModel) => {
    return request({
      url: '/sfc/masterSn/save',
      method: 'post',
      data: query
    });
  };

  export const getAbnormalBarcodeNotScan = (query: any): AxiosPromise<any>=> {
    return request({
      url: '/sfc/information/barcode/notScan',
      method: 'get',
      params: query
    });
  };

  export const getAbnormalInfoNoFinish = (query: any): AxiosPromise<any>=> {
    return request({
      url: '/sfc/information/scanReport/noFinish',
      method: 'get',
      params: query
    });
  };

  export const getDict = (dictType: string): AxiosPromise<any> => {
    return request({
      url: `/system/dict/data/type/${dictType}`,
      method: 'get'
    });
  }

  export const getModuleBindings = (query: any): AxiosPromise<any> => {
    return request({
      url: `/sfc/information/ldmzlink/list`,
      method: 'get',
      params: query
    });
  }

  export const queryBarcodeInfo = (query: any): AxiosPromise<any> => {
    return request({
      url: `/sfc/information/barcode/query`,
      method: 'get',
      params: query
    });
  }

  // 添加保存模组绑定关系的方法
export const saveModuleBinding = (data: any[]): AxiosPromise<any> => {
  return request({
    url: '/sfc/information/ldmzlink/add',
    method: 'post',
    data: data
  });
}

