import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { KbWkXlForm, KbWkXlQuery, KbWkXlVO } from '@/api/assets/kbWkXl/types';

/**
 * 查询装配看板隐藏数据列表
 * @param query
 * @returns {*}
 */

export const listKbWkXl = (query?: KbWkXlQuery): AxiosPromise<KbWkXlVO[]> => {
  return request({
    url: '/sfc/assets/kbWkXl/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询装配看板隐藏数据详细
 * @param id
 */
export const getKbWkXl = (id: string | number): AxiosPromise<KbWkXlVO> => {
  return request({
    url: '/sfc/assets/kbWkXl/' + id,
    method: 'get'
  });
};

/**
 * 新增装配看板隐藏数据
 * @param data
 */
export const addKbWkXl = (data: KbWkXlForm) => {
  return request({
    url: '/sfc/assets/kbWkXl',
    method: 'post',
    data: data
  });
};

/**
 * 修改装配看板隐藏数据
 * @param data
 */
export const updateKbWkXl = (data: KbWkXlForm) => {
  return request({
    url: '/sfc/assets/kbWkXl',
    method: 'put',
    data: data
  });
};

/**
 * 删除装配看板隐藏数据
 * @param id
 */
export const delKbWkXl = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/assets/kbWkXl/' + id,
    method: 'delete'
  });
};
