export interface KbWkXlVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 统计日期
   */
  sysDt: string;

  /**
   * 楼层
   */
  floorNo: string;

  /**
   * 工站号
   */
  wkNo: string;

  /**
   * 工站名称
   */
  wkName: string;

  /**
   * 标准工时
   */
  basGs: number;

  /**
   * 扫描工时
   */
  smGs: number;

  /**
   * 工站效率
   */
  wkXl: number;

  /**
   * 月份
   */
  sysDm: string;
}

export interface KbWkXlForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 统计日期
   */
  sysDt?: string;

  /**
   * 楼层
   */
  floorNo?: string;

  /**
   * 工站号
   */
  wkNo?: string;

  /**
   * 工站名称
   */
  wkName?: string;

  /**
   * 标准工时
   */
  basGs?: number;

  /**
   * 扫描工时
   */
  smGs?: number;

  /**
   * 工站效率
   */
  wkXl?: number;

  /**
   * 月份
   */
  sysDm?: string;
}

export interface KbWkXlQuery extends PageQuery {
  /**
   * 统计日期
   */
  sysDt?: string;

  /**
   * 工站号
   */
  wkNo?: string;

  /**
   * 工站名称
   */
  wkName?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
