import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { InfoVO, InfoForm, InfoQuery } from '@/api/system/info/types';

/**
 * 查询项目信息列表
 * @param query
 * @returns {*}
 */

export const listInfo = (query?: InfoQuery): AxiosPromise<InfoVO[]> => {
  return request({
    url: '/system/info/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询项目信息详细
 * @param projectId
 */
export const getInfo = (projectId: string | number): AxiosPromise<InfoVO> => {
  return request({
    url: '/system/info/' + projectId,
    method: 'get'
  });
};

/**
 * 新增项目信息
 * @param data
 */
export const addInfo = (data: InfoForm) => {
  return request({
    url: '/system/info',
    method: 'post',
    data: data
  });
};

/**
 * 修改项目信息
 * @param data
 */
export const updateInfo = (data: InfoForm) => {
  return request({
    url: '/system/info',
    method: 'put',
    data: data
  });
};

/**
 * 删除项目信息
 * @param projectId
 */
export const delInfo = (projectId: string | number | Array<string | number>) => {
  return request({
    url: '/system/info/' + projectId,
    method: 'delete'
  });
};
