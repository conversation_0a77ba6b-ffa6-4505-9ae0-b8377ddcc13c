export interface InfoVO {
  /**
   * 项目ID
   */
  projectId: string | number;

  /**
   * 项目类型
   */
  projectType: string;

  /**
   * 名称
   */
  name: string;

  /**
   * 项目来源
   */
  projectSource: string;

  /**
   * 计划开始日期
   */
  startDate: string;

  /**
   * 计划完成日期
   */
  endDate: string;

  /**
   * 项目计划人
   */
  projectPlanner: string;

  /**
   * 项目经理
   */
  projectManager: string;

  /**
   * 负责内部机构
   */
  internalOrganization: string;

  /**
   * 客户代码
   */
  customerCode: string;

  /**
   * 项目阶段
   */
  projectPhase: string;

  /**
   * 项目等级
   */
  projectLevel: string;

  /**
   * 项目类别
   */
  projectCategory: string;

  /**
   * 数量
   */
  quantity: number;

  /**
   * 工厂
   */
  factory: string;

  /**
   * 行业类型
   */
  industryType: string;

  /**
   * 产品类型
   */
  productType: string;

  /**
   * 系统立项时间
   */
  systemInitiationDate: string;

  /**
   * 项目成本红线
   */
  projectCostLimit: number;

  /**
   * 项目售后成本红线
   */
  afterSalesCostLimit: number;

  /**
   * 项目成本超红线原因
   */
  costOverrunReason: string;

  /**
   * 区域
   */
  region: string;

  /**
   * 状态
   */
  status: string;
}

export interface InfoForm extends BaseEntity {
  /**
   * 项目ID
   */
  projectId?: string | number;

  /**
   * 项目类型
   */
  projectType?: string;

  /**
   * 名称
   */
  name?: string;

  /**
   * 项目来源
   */
  projectSource?: string;

  /**
   * 计划开始日期
   */
  startDate?: string;

  /**
   * 计划完成日期
   */
  endDate?: string;

  /**
   * 项目计划人
   */
  projectPlanner?: string;

  /**
   * 项目经理
   */
  projectManager?: string;

  /**
   * 负责内部机构
   */
  internalOrganization?: string;

  /**
   * 客户代码
   */
  customerCode?: string;

  /**
   * 项目阶段
   */
  projectPhase?: string;

  /**
   * 项目等级
   */
  projectLevel?: string;

  /**
   * 项目类别
   */
  projectCategory?: string;

  /**
   * 数量
   */
  quantity?: number;

  /**
   * 工厂
   */
  factory?: string;

  /**
   * 行业类型
   */
  industryType?: string;

  /**
   * 产品类型
   */
  productType?: string;

  /**
   * 系统立项时间
   */
  systemInitiationDate?: string;

  /**
   * 项目成本红线
   */
  projectCostLimit?: number;

  /**
   * 项目售后成本红线
   */
  afterSalesCostLimit?: number;

  /**
   * 项目成本超红线原因
   */
  costOverrunReason?: string;

  /**
   * 区域
   */
  region?: string;

  /**
   * 状态
   */
  status?: string;
}

export interface InfoQuery extends PageQuery {
  /**
   * 项目类型
   */
  projectType?: string;

  /**
   * 名称
   */
  name?: string;

  /**
   * 项目来源
   */
  projectSource?: string;

  /**
   * 计划开始日期
   */
  startDate?: string;

  /**
   * 计划完成日期
   */
  endDate?: string;

  /**
   * 项目计划人
   */
  projectPlanner?: string;

  /**
   * 项目经理
   */
  projectManager?: string;

  /**
   * 负责内部机构
   */
  internalOrganization?: string;

  /**
   * 客户代码
   */
  customerCode?: string;

  /**
   * 项目阶段
   */
  projectPhase?: string;

  /**
   * 项目等级
   */
  projectLevel?: string;

  /**
   * 项目类别
   */
  projectCategory?: string;

  /**
   * 数量
   */
  quantity?: number;

  /**
   * 工厂
   */
  factory?: string;

  /**
   * 行业类型
   */
  industryType?: string;

  /**
   * 产品类型
   */
  productType?: string;

  /**
   * 系统立项时间
   */
  systemInitiationDate?: string;

  /**
   * 项目成本红线
   */
  projectCostLimit?: number;

  /**
   * 项目售后成本红线
   */
  afterSalesCostLimit?: number;

  /**
   * 项目成本超红线原因
   */
  costOverrunReason?: string;

  /**
   * 区域
   */
  region?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
