import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { AutocodeForm, AutoCodePartForm, AutoCodePartQuery, AutoCodePartVO, AutocodeQuery, AutocodeVO } from '@/api/system/autocode/types';

/**
 * 查询编码生成规则列表
 * @param query
 * @returns {*}
 */

export const listAutocode = (query?: AutocodeQuery): AxiosPromise<AutocodeVO[]> => {
  return request({
    url: '/system/autocode/rule/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询编码生成规则详细
 * @param ruleId
 */
export const getAutocode = (ruleId: string | number): AxiosPromise<AutocodeVO> => {
  return request({
    url: '/system/autocode/rule/' + ruleId,
    method: 'get'
  });
};

/**
 * 新增编码生成规则
 * @param data
 */
export const addAutocode = (data: AutocodeForm) => {
  return request({
    url: '/system/autocode/rule',
    method: 'post',
    data: data
  });
};

/**
 * 修改编码生成规则
 * @param data
 */
export const updateAutocode = (data: AutocodeForm) => {
  return request({
    url: '/system/autocode/rule',
    method: 'put',
    data: data
  });
};

/**
 * 删除编码生成规则
 * @param ruleId
 */
export const delAutocode = (ruleId: string | number | Array<string | number>) => {
  return request({
    url: '/system/autocode/rule/' + ruleId,
    method: 'delete'
  });
};

/**
 * 查询编码生成规则组成列表
 * @param query
 * @returns {*}
 */

export const listAutoCodePart = (query?: AutoCodePartQuery): AxiosPromise<AutoCodePartVO[]> => {
  return request({
    url: '/system/autocode/part/list',
    method: 'get',
    params: query
  });
};
/**
 * 查询编码生成规则组成详细
 * @param partId
 */
export const getAutoCodePart = (partId: string | number): AxiosPromise<AutoCodePartVO> => {
  return request({
    url: '/system/autocode/part/' + partId,
    method: 'get'
  });
};

/**
 * 新增编码生成规则组成
 * @param data
 */
export const addAutoCodePart = (data: AutoCodePartForm) => {
  return request({
    url: '/system/autocode/part',
    method: 'post',
    data: data
  });
};

/**
 * 修改编码生成规则组成
 * @param data
 */
export const updateAutoCodePart = (data: AutoCodePartForm) => {
  return request({
    url: '/system/autocode/part',
    method: 'put',
    data: data
  });
};

/**
 * 删除编码生成规则组成
 * @param partId
 */
export const delAutoCodePart = (partId: string | number | Array<string | number>) => {
  return request({
    url: '/system/autocode/part/' + partId,
    method: 'delete'
  });
};

/**
 * 生成编码
 * @param ruleCode
 */
export function genCode(ruleCode: string | number) {
  return request({
    url: '/system/autocode/get/' + ruleCode,
    method: 'get'
  });
}
