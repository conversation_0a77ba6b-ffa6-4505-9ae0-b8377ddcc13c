export interface AutocodeVO {
  /**
   * 规则ID
   */
  ruleId: string | number;

  /**
   * 规则编码
   */
  ruleCode: string;

  /**
   * 规则名称
   */
  ruleName: string;

  /**
   * 描述
   */
  ruleDesc: string;

  /**
   * 最大长度
   */
  maxLength: number;

  /**
   * 是否补齐
   */
  isPadded: string;

  /**
   * 启用状态
   */
  enableFlag: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 预留字段1
   */
  attr1: string;

  /**
   * 预留字段2
   */
  attr2: string;

  /**
   * 预留字段3
   */
  attr3: number;

  /**
   * 预留字段4
   */
  attr4: number;

  /**
   * 创建时间
   */
  createTime: string;
}

export interface AutocodeForm extends BaseEntity {
  /**
   * 规则ID
   */
  ruleId?: string | number;

  /**
   * 规则编码
   */
  ruleCode?: string;

  /**
   * 规则名称
   */
  ruleName?: string;

  /**
   * 描述
   */
  ruleDesc?: string;

  /**
   * 最大长度
   */
  maxLength?: number;

  /**
   * 是否补齐
   */
  isPadded?: string;

  /**
   * 补齐字符
   */
  paddedChar?: string;

  /**
   * 补齐方式
   */
  paddedMethod?: string;

  /**
   * 启用状态
   */
  enableFlag?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 预留字段1
   */
  attr1?: string;

  /**
   * 预留字段2
   */
  attr2?: string;

  /**
   * 预留字段3
   */
  attr3?: number;

  /**
   * 预留字段4
   */
  attr4?: number;
}

export interface AutocodeQuery extends PageQuery {
  /**
   * 规则编码
   */
  ruleCode?: string;

  /**
   * 规则名称
   */
  ruleName?: string;

  /**
   * 启用状态
   */
  enableFlag?: string;

  /**
   * 预留字段4
   */
  attr4?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}

export interface SysAutoCodePartVo extends BaseEntity {
  partId: number | string;
  ruleId: number | string;
  partIndex: number | string;
  partLength: number | string;
  partType: string;
  partCode: string;
  partName: string;
  remark: string;
}
export interface AutoCodePartVO {
  /**
   * 分段ID
   */
  partId: string | number;

  /**
   * 规则ID
   */
  ruleId: string | number;

  /**
   * 分段序号
   */
  partIndex: number;

  /**
   * 分段类型，INPUTCHAR：输入字符，NOWDATE：当前日期时间，FIXCHAR：固定字符，SERIALNO：流水号
   */
  partType: string;

  /**
   * 分段编号
   */
  partCode: string;

  /**
   * 分段名称
   */
  partName: string;

  /**
   * 分段长度
   */
  partLength: number;

  /**
   *
   */
  dateFormat: string;

  /**
   * 输入字符
   */
  inputCharacter: string;

  /**
   * 固定字符
   */
  fixCharacter: string;

  /**
   * 流水号起始值
   */
  seriaStartNo: number;

  /**
   * 流水号步长
   */
  seriaStep: number;

  /**
   * 流水号当前值
   */
  seriaNowNo: number;

  /**
   * 流水号是否循环
   */
  cycleFlag: string;

  /**
   * 循环方式，YEAR：按年，MONTH：按月，DAY：按天，HOUR：按小时，MINITE：按分钟，OTHER：按传入字符变
   */
  cycleMethod: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 预留字段1
   */
  attr1: string;

  /**
   * 预留字段2
   */
  attr2: string;

  /**
   * 预留字段3
   */
  attr3: number;

  /**
   * 预留字段4
   */
  attr4: number;
}

export interface AutoCodePartForm extends BaseEntity {
  /**
   * 分段ID
   */
  partId?: string | number;

  /**
   * 规则ID
   */
  ruleId?: string | number;

  /**
   * 分段序号
   */
  partIndex?: number;

  /**
   * 分段类型，INPUTCHAR：输入字符，NOWDATE：当前日期时间，FIXCHAR：固定字符，SERIALNO：流水号
   */
  partType?: string;

  /**
   * 分段编号
   */
  partCode?: string;

  /**
   * 分段名称
   */
  partName?: string;

  /**
   * 分段长度
   */
  partLength?: number;

  /**
   *
   */
  dateFormat?: string;

  /**
   * 输入字符
   */
  inputCharacter?: string;

  /**
   * 固定字符
   */
  fixCharacter?: string;

  /**
   * 流水号起始值
   */
  seriaStartNo?: number;

  /**
   * 流水号步长
   */
  seriaStep?: number;

  /**
   * 流水号当前值
   */
  seriaNowNo?: number;

  /**
   * 流水号是否循环
   */
  cycleFlag?: string;

  /**
   * 循环方式，YEAR：按年，MONTH：按月，DAY：按天，HOUR：按小时，MINITE：按分钟，OTHER：按传入字符变
   */
  cycleMethod?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 预留字段1
   */
  attr1?: string;

  /**
   * 预留字段2
   */
  attr2?: string;

  /**
   * 预留字段3
   */
  attr3?: number;

  /**
   * 预留字段4
   */
  attr4?: number;
}

export interface AutoCodePartQuery extends PageQuery {
  /**
   * 规则ID
   */
  ruleId?: string | number;

  /**
   * 分段序号
   */
  partIndex?: number;

  /**
   * 分段类型，INPUTCHAR：输入字符，NOWDATE：当前日期时间，FIXCHAR：固定字符，SERIALNO：流水号
   */
  partType?: string;

  /**
   * 分段编号
   */
  partCode?: string;

  /**
   * 分段名称
   */
  partName?: string;

  /**
   * 分段长度
   */
  partLength?: number;

  /**
   *
   */
  dateFormat?: string;

  /**
   * 输入字符
   */
  inputCharacter?: string;

  /**
   * 固定字符
   */
  fixCharacter?: string;

  /**
   * 流水号起始值
   */
  seriaStartNo?: number;

  /**
   * 流水号步长
   */
  seriaStep?: number;

  /**
   * 流水号当前值
   */
  seriaNowNo?: number;

  /**
   * 流水号是否循环
   */
  cycleFlag?: string;

  /**
   * 循环方式，YEAR：按年，MONTH：按月，DAY：按天，HOUR：按小时，MINITE：按分钟，OTHER：按传入字符变
   */
  cycleMethod?: string;

  /**
   * 预留字段1
   */
  attr1?: string;

  /**
   * 预留字段2
   */
  attr2?: string;

  /**
   * 预留字段3
   */
  attr3?: number;

  /**
   * 预留字段4
   */
  attr4?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
