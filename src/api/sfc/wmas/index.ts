import request from '@/utils/request';

export const BASE_URL = '/sfc/sample/wmas';
export const REPORT_HEAD_URL = `${BASE_URL}/report`;
export const EXPORT_PIC_URL = `${BASE_URL}/export`;
export const UPLOAD_PIC_URL = `${BASE_URL}/pic/upload`;
export const MO_GX_URL = `${BASE_URL}/report/gx`;
export const MAINTAIN_URL = `${BASE_URL}/maintain`;

export function saveWmasReport(data: any): any {
  return request({
    url: `${REPORT_HEAD_URL}/save`,
    method: 'post',
    data: data
  });
}

export const searchWmasData = (query?: any): any => {
  return request({
    url: `${REPORT_HEAD_URL}`,
    method: 'get',
    params: query
  });
};

export const reportGx = (moNo?: any): any => {
  return request({
    url: `${MO_GX_URL}/${moNo}`,
    method: 'get',
  });
};

export const getPicUrl = (moNo?: any): any => {
  return request({
    url: `${EXPORT_PIC_URL}/${moNo}`,
    method: 'get',
  });
};
