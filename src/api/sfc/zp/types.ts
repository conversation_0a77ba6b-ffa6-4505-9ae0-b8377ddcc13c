export interface ZpVO {
  /**
   * 
   */
  id: string | number;

  /**
   * MO号
   */
  moNo: string;

  /**
   * 项目号
   */
  pmNo: string;

  /**
   * 设备名称
   */
  deviceDesc: string;

  /**
   * 设备项次
   */
  sopMo: string;

  /**
   * 客户代码
   */
  custNo: string;

  /**
   * 实际交期
   */
  actualDate: string;

  /**
   * 客户现场
   */
  custSite: string;

  /**
   * 设备铭牌
   */
  custSn: string;

  /**
   * SN
   */
  sn: string;

  /**
   * 装配地点
   */
  zpSite: string;

  /**
   * 订单数量
   */
  orderQty: number;

  /**
   * 机械技师
   */
  technician: string;

  /**
   * 电气技师
   */
  electrician: string;

  /**
   * IPQC
   */
  ipqc: string;

  /**
   * 机械工程师
   */
  mechanicalEngineer: string;

  /**
   * 电气工程师
   */
  electricalEngineer: string;

  /**
   * NPI
   */
  npi: string;

  /**
   * PM
   */
  pm: string;

  /**
   * OQC
   */
  oqc: string;

  /**
   * 其他
   */
  remark: string;

}

export interface ZpForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * MO号
   */
  moNo?: string;

  /**
   * 项目号
   */
  pmNo?: string;

  /**
   * 设备名称
   */
  deviceDesc?: string;

  /**
   * 设备项次
   */
  sopMo?: string;

  /**
   * 客户代码
   */
  custNo?: string;

  /**
   * 实际交期
   */
  actualDate?: string;

  /**
   * 客户现场
   */
  custSite?: string;

  /**
   * 设备铭牌
   */
  custSn?: string;

  /**
   * SN
   */
  sn?: string;

  /**
   * 装配地点
   */
  zpSite?: string;

  /**
   * 订单数量
   */
  orderQty?: number;

  /**
   * 机械技师
   */
  technician?: string;

  /**
   * 电气技师
   */
  electrician?: string;

  /**
   * IPQC
   */
  ipqc?: string;

  /**
   * 机械工程师
   */
  mechanicalEngineer?: string;

  /**
   * 电气工程师
   */
  electricalEngineer?: string;

  /**
   * NPI
   */
  npi?: string;

  /**
   * PM
   */
  pm?: string;

  /**
   * OQC
   */
  oqc?: string;

  /**
   * 其他
   */
  remark?: string;

}

export interface ZpQuery extends PageQuery {

  /**
   * MO号
   */
  moNo?: string;

  /**
   * 项目号
   */
  pmNo?: string;

  /**
   * 设备名称
   */
  deviceDesc?: string;

  /**
   * 设备项次
   */
  sopMo?: string;

  /**
   * 客户代码
   */
  custNo?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



