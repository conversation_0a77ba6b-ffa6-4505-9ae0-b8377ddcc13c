import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ZpForm, ZpQuery, ZpVO } from '@/api/sfc/zp/types';

/**
 * 查询装配现场图像列表
 * @param query
 * @returns {*}
 */

export const listZp = (query?: ZpQuery): AxiosPromise<ZpVO[]> => {
  return request({
    url: '/sfc/zp/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询装配现场图像详细
 * @param id
 */
export const getZp = (id: string | number): AxiosPromise<ZpVO> => {
  return request({
    url: '/sfc/zp/' + id,
    method: 'get'
  });
};

/**
 * 新增装配现场图像
 * @param data
 */
export const addZp = (data: ZpForm) => {
  return request({
    url: '/sfc/zp',
    method: 'post',
    data: data
  });
};

/**
 * 修改装配现场图像
 * @param data
 */
export const updateZp = (data: ZpForm) => {
  return request({
    url: '/sfc/zp',
    method: 'put',
    data: data
  });
};

/**
 * 删除装配现场图像
 * @param id
 */
export const delZp = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/zp/' + id,
    method: 'delete'
  });
};

export const BASE_URL = '/sfc/zp';
export const PIC_HEAD_URL = `${BASE_URL}/pic/data`;
export const UPLOAD_PIC_URL = `${BASE_URL}/pic/upload`;
export const DOWNLOAD_PIC_URL = `${BASE_URL}/pic/download`;
export const MAINTAIN_URL = `${BASE_URL}/maintain`;


export interface ZpPicModel {
  id?: string;
  pmNo: string;
  moNo: string;
  sopMo: string;
  deviceDesc: string;
  custNo: string;
  actualDate: string;
  custSite: string;
  custSn: string;
  sn: string;
  zpSite: string;
  orderQty: string;
  technician: string;
  electrician: string;
  ipqc: string;
  mechanicalEngineer: string;
  electricalEngineer: string;
  npi: string;
  pm: string;
  oqc: string;
  filePath?: string;
  remark: string;
}

export interface ZpPicDataModel {
  id?: string;
  moNo: string;
  fileFolder: string;
  fileName: string;
  remark: string;
}


export function getZpMo(mo: string): any {
  return request({
    url: `${BASE_URL}/pic/${mo}`,
    method: 'get'
  });
}

export function saveZpPic(data: any): any {
  return request({
    url: `${BASE_URL}/pic/save`,
    method: 'post',
    data: data
  });
}

export function saveZpPicData(data: any): any {
  return request({
    url: `${MAINTAIN_URL}/save`,
    method: 'post',
    data: data
  });
}

export function uploadZpMo(formData: FormData): any {
  return request({
    url: `${BASE_URL}/pic/mo/upload`,
    method: 'post',
    data: formData
  });
}

export function uploadZpPic(formData: FormData): any {
  return request({
    url: `${UPLOAD_PIC_URL}`,
    method: 'post',
    data: formData
  });
}

export function downloadZpPic(param: object = {}): any {
  return request({
    url: `${DOWNLOAD_PIC_URL}`,
    method: 'get',
    params: param
  });
}

export function queryZpPicDataList(data: object = {}) {
  return request({
    url: `${PIC_HEAD_URL}/list`,
    method: 'get',
    params: data
  });
}
