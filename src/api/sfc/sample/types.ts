export interface SampleVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 状态
   */
  moState: string;

  /**
   * 末工序时间
   */
  lastOpTime: string;

  /**
   * 客户代码
   */
  custNo: string;

  /**
   * 客户PO
   */
  poNo: string;

  /**
   * 图号
   */
  drawNumber: string;

  /**
   * 订单类别
   */
  orderType: string;

  /**
   * 材料
   */
  material: string;

  /**
   * MO号
   */
  moNo: string;

  /**
   * 订单数量
   */
  orderQty: number;

  /**
   * 制造部门
   */
  mfgDept: string;

  /**
   * 当前工序
   */
  currentProcess: string;

  /**
   * 问题点
   */
  issue: string;

  /**
   * 解决方案
   */
  solution: string;

  /**
   * 完成时间
   */
  completionTime: string;

  /**
   * 责任人
   */
  owner: string;

  /**
   * 结果
   */
  result: string;

  /**
   * 接单日期
   */
  bargainDate: string;

  /**
   * 客户交期
   */
  deliveryDate: string;

  /**
   * PMC交期
   */
  pmcReqDate: string;

  /**
   * 下单员
   */
  orderSalm: string;

  /**
   * 项目负责
   */
  projectLeader: string;

  /**
   * 计划类型
   */
  planType: string;

  /**
   * 订单状态
   */
  status: string;

  /**
   * 图片示例
   */
  imagePath: string;

  /**
   * 图片示例Url
   */
  imagePathUrl: string;
  /**
   * 备注
   */
  remark: string;

}

export interface SampleForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 状态
   */
  moState?: string;

  /**
   * 末工序时间
   */
  lastOpTime?: string;

  /**
   * 客户代码
   */
  custNo?: string;

  /**
   * 客户PO
   */
  poNo?: string;

  /**
   * 图号
   */
  drawNumber?: string;

  /**
   * 订单类别
   */
  orderType?: string;

  /**
   * 材料
   */
  material?: string;

  /**
   * MO号
   */
  moNo?: string;

  /**
   * 订单数量
   */
  orderQty?: number;

  /**
   * 制造部门
   */
  mfgDept?: string;

  /**
   * 当前工序
   */
  currentProcess?: string;

  /**
   * 问题点
   */
  issue?: string;

  /**
   * 解决方案
   */
  solution?: string;

  /**
   * 完成时间
   */
  completionTime?: string;

  /**
   * 责任人
   */
  owner?: string;

  /**
   * 结果
   */
  result?: string;

  /**
   * 接单日期
   */
  bargainDate?: string;

  /**
   * 客户交期
   */
  deliveryDate?: string;

  /**
   * PMC交期
   */
  pmcReqDate?: string;

  /**
   * 下单员
   */
  orderSalm?: string;

  /**
   * 项目负责
   */
  projectLeader?: string;

  /**
   * 计划类型
   */
  planType?: string;

  /**
   * 订单状态
   */
  status?: string;

  /**
   * 图片示例
   */
  imagePath?: string;

  /**
   * 备注
   */
  remark?: string;

}

export interface SampleQuery extends PageQuery {

  /**
   * 状态
   */
  moState?: string;

  /**
   * 图号
   */
  drawNumber?: string;

  /**
   * 订单类别
   */
  orderType?: string;

  /**
   * 下单员
   */
  orderSalm?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



