import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SampleForm, SampleQuery, SampleVO } from '@/api/sfc/sample/types';

/**
 * 查询样品清单维护列表
 * @param query
 * @returns {*}
 */

export const listSample = (query?: SampleQuery): AxiosPromise<SampleVO[]> => {
  return request({
    url: '/sfc/sample/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询样品清单维护详细
 * @param id
 */
export const getSample = (id: string | number): AxiosPromise<SampleVO> => {
  return request({
    url: '/sfc/sample/' + id,
    method: 'get'
  });
};

/**
 * 新增样品清单维护
 * @param data
 */
export const addSample = (data: SampleForm) => {
  return request({
    url: '/sfc/sample',
    method: 'post',
    data: data
  });
};

/**
 * 修改样品清单维护
 * @param data
 */
export const updateSample = (data: SampleForm) => {
  return request({
    url: '/sfc/sample',
    method: 'put',
    data: data
  });
};

/**
 * 删除样品清单维护
 * @param id
 */
export const delSample = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/sample/' + id,
    method: 'delete'
  });
};
