import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { EmpInfoForm, EmpInfoQuery, EmpInfoVO } from '@/api/sfc/empInfo/types';

/**
 * 查询人员信息配置列表
 * @param query
 * @returns {*}
 */

export const listEmpInfo = (query?: EmpInfoQuery): AxiosPromise<EmpInfoVO[]> => {
  return request({
    url: '/sfc/empInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询人员信息配置详细
 * @param id
 */
export const getEmpInfo = (id: string | number): AxiosPromise<EmpInfoVO> => {
  return request({
    url: '/sfc/empInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增人员信息配置
 * @param data
 */
export const addEmpInfo = (data: EmpInfoForm) => {
  return request({
    url: '/sfc/empInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改人员信息配置
 * @param data
 */
export const updateEmpInfo = (data: EmpInfoForm) => {
  return request({
    url: '/sfc/empInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除人员信息配置
 * @param id
 */
export const delEmpInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/empInfo/' + id,
    method: 'delete'
  });
};
