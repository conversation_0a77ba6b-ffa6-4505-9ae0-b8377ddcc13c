export interface EmpInfoVO {
  /**
   *
   */
  id: string | number;

  /**
   * 工号
   */
  empNo: string;

  /**
   * 姓名
   */
  empName: string;

  /**
   * 电话
   */
  phone: string;

  /**
   * EMAIL
   */
  eMail: string;

}

export interface EmpInfoForm extends BaseEntity {
  /**
   *
   */
  id?: string | number;

  /**
   * 工号
   */
  empNo?: string;

  /**
   * 姓名
   */
  empName?: string;

  /**
   * 电话
   */
  phone?: string;

  /**
   * EMAIL
   */
  eMail?: string;

}

export interface EmpInfoQuery extends PageQuery {

  /**
   * 工号
   */
  empNo?: string;

  /**
   * 姓名
   */
  empName?: string;

  /**
   * 电话
   */
  phone?: string;

  /**
   * EMAIL
   */
  eMail?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



