import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DeclarationForm, DeclarationQuery, DeclarationVO } from '@/api/sfc/declaration/types';

/**
 * 查询货物申报信息列表
 * @param query
 * @returns {*}
 */

export const listDeclaration = (query?: DeclarationQuery): AxiosPromise<DeclarationVO[]> => {
  return request({
    url: '/sfc/declaration/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询货物申报信息详细
 * @param id
 */
export const getDeclaration = (id: string | number): AxiosPromise<DeclarationVO> => {
  return request({
    url: '/sfc/declaration/' + id,
    method: 'get'
  });
};

/**
 * 新增货物申报信息
 * @param data
 */
export const addDeclaration = (data: DeclarationForm) => {
  return request({
    url: '/sfc/declaration',
    method: 'post',
    data: data
  });
};

/**
 * 修改货物申报信息
 * @param data
 */
export const updateDeclaration = (data: DeclarationForm) => {
  return request({
    url: '/sfc/declaration',
    method: 'put',
    data: data
  });
};

/**
 * 删除货物申报信息
 * @param id
 */
export const delDeclaration = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/declaration/' + id,
    method: 'delete'
  });
};
