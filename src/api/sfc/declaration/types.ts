export interface DeclarationVO {
  /**
   *
   */
  id: string | number;

  /**
   * 结构类型（RG）
   */
  structureType: string;

  /**
   * 单台净重（Net weigh）
   */
  netWeight: number;

  /**
   * 总净重/KG
   */
  totalNetWeightKg: number;

  /**
   * 货物图片（picture）
   */
  goodsPicture: string;

  /**
   * 型号图片
   */
  modelPicture: string;

  /**
   * 标签_名牌图片（nameplate picture）
   */
  nameplatePicture: string;

  /**
   * 备注
   */
  remarks: string;

  /**
   * 申报品名
   */
  declarationName: string;

  /**
   * HS CODE
   */
  hsCode: string;

  /**
   * 申报要素
   */
  declarationElements: string;
}

export interface DeclarationForm extends BaseEntity {
  /**
   *
   */
  id?: string | number;

  /**
   * 结构类型（RG）
   */
  structureType?: string;

  /**
   * 单台净重（Net weigh）
   */
  netWeight?: number;

  /**
   * 总净重/KG
   */
  totalNetWeightKg?: number;

  /**
   * 货物图片（picture）
   */
  goodsPicture?: string;

  /**
   * 型号图片
   */
  modelPicture?: string;

  /**
   * 标签_名牌图片（nameplate picture）
   */
  nameplatePicture?: string;

  /**
   * 备注
   */
  remarks?: string;

  /**
   * 申报品名
   */
  declarationName?: string;

  /**
   * HS CODE
   */
  hsCode?: string;

  /**
   * 申报要素
   */
  declarationElements?: string;
}

export interface DeclarationQuery extends PageQuery {
  /**
   * 结构类型（RG）
   */
  structureType?: string;

  /**
   * 单台净重（Net weigh）
   */
  netWeight?: number;

  /**
   * 总净重/KG
   */
  totalNetWeightKg?: number;

  /**
   * 货物图片（picture）
   */
  goodsPicture?: string;

  /**
   * 型号图片
   */
  modelPicture?: string;

  /**
   * 标签_名牌图片（nameplate picture）
   */
  nameplatePicture?: string;

  /**
   * 备注
   */
  remarks?: string;

  /**
   * 申报品名
   */
  declarationName?: string;

  /**
   * HS CODE
   */
  hsCode?: string;

  /**
   * 申报要素
   */
  declarationElements?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
