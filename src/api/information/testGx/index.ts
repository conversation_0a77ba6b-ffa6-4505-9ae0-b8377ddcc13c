import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TestGxForm, TestGxQuery, TestGxVO } from '@/api/information/testGx/types';

/**
 * 查询调试SOP设置列表
 * @param query
 * @returns {*}
 */

export const listTestGx = (query?: TestGxQuery): AxiosPromise<TestGxVO[]> => {
  return request({
    url: '/sfc/information/testGx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询调试SOP设置详细
 * @param id
 */
export const getTestGx = (id: string | number): AxiosPromise<TestGxVO> => {
  return request({
    url: '/sfc/information/testGx/' + id,
    method: 'get'
  });
};

/**
 * 新增调试SOP设置
 * @param data
 */
export const addTestGx = (data: TestGxForm) => {
  return request({
    url: '/sfc/information/testGx',
    method: 'post',
    data: data
  });
};

/**
 * 修改调试SOP设置
 * @param data
 */
export const updateTestGx = (data: TestGxForm) => {
  return request({
    url: '/sfc/information/testGx',
    method: 'put',
    data: data
  });
};

/**
 * 删除调试SOP设置
 * @param id
 */
export const delTestGx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/information/testGx/' + id,
    method: 'delete'
  });
};

export const queryMoTsGx = (data: object = {}) => {
  return request({
    url: '/sfc/information/moTsGx/query',
    method: 'get',
    params: data
  });
};
