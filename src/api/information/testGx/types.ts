export interface TestGxVO {
  /**
   * 调试工序号t_gx_no
   */
  tsGxNo: string;

  /**
   * 调试工序名t_gx_name
   */
  tsGxName: string;

  /**
   * 标准工时
   */
  basGs: number;

  /**
   * 备注
   */
  rem: string;

  /**
   * 文件名
   */
  sfileName: string;

  /**
   * 操作工号
   */
  usrId: string | number;

  /**
   * 操作姓名
   */
  usrName: string;

  /**
   * 主机
   */
  host: string;

  /**
   * 操作时间
   */
  sysdt: string;

  /**
   * 标志
   */
  flag: number;
}

export interface TestGxForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 调试工序号t_gx_no
   */
  tsGxNo?: string;

  /**
   * 调试工序名t_gx_name
   */
  tsGxName?: string;

  /**
   * 标准工时
   */
  basGs?: number;

  /**
   * 备注
   */
  rem?: string;

  /**
   * 文件名
   */
  sfileName?: string;

  /**
   * 操作工号
   */
  usrId?: string | number;

  /**
   * 操作姓名
   */
  usrName?: string;

  /**
   * 主机
   */
  host?: string;

  /**
   * 操作时间
   */
  sysdt?: string;

  /**
   * 标志
   */
  flag?: number;
}

export interface TestGxQuery extends PageQuery {
  /**
   * 调试工序号t_gx_no
   */
  tsGxNo?: string;

  /**
   * 调试工序名t_gx_name
   */
  tsGxName?: string;

  /**
   * 标准工时
   */
  basGs?: number;

  /**
   * 备注
   */
  rem?: string;

  /**
   * 文件名
   */
  sfileName?: string;

  /**
   * 操作工号
   */
  usrId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}
