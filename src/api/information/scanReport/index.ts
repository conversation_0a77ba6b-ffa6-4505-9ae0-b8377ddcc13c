import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ScanReportForm, ScanReportQuery, ScanReportVO } from '@/api/information/scanReport/types';

/**
 * 查询报工扫描记录列表
 * @param query
 * @returns {*}
 */

export const listScanReport = (query?: ScanReportQuery): AxiosPromise<ScanReportVO[]> => {
  return request({
    url: '/sfc/information/scanReport/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询报工扫描记录详细
 * @param id
 */
export const getScanReport = (id: string | number): AxiosPromise<ScanReportVO> => {
  return request({
    url: '/sfc/information/scanReport/' + id,
    method: 'get'
  });
};

/**
 * 根据类型获取数据
 * @param type
 */
export const getCodeRuleList = (type: string | number): any => {
  return request({
    url: `/sfc/information/codeRule/type/${type}`,
    method: 'get'
  });
};

/**
 * 新增报工扫描记录
 * @param data
 */
export const addScanReport = (data: ScanReportForm) => {
  return request({
    url: '/sfc/information/scanReport',
    method: 'post',
    data: data
  });
};

/**
 * 修改报工扫描记录
 * @param data
 */
export const updateScanReport = (data: ScanReportForm) => {
  return request({
    url: '/sfc/information/scanReport',
    method: 'put',
    data: data
  });
};

/**
 * 删除报工扫描记录
 * @param id
 */
export const delScanReport = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/information/scanReport/' + id,
    method: 'delete'
  });
};

/**
 * 手动查询报工扫描记录列表
 * @param query
 * @returns {*}
 */

export const queryScanReport = (query?: ScanReportQuery): AxiosPromise<ScanReportVO[]> => {
  return request({
    url: '/sfc/information/scanReport/search',
    method: 'get',
    params: query
  });
};

export const queryScanReportAll = (query?: ScanReportQuery): any => {
  return request({
    url: '/sfc/information/scanReport/report/search/all',
    method: 'get',
    params: query
  });
};

export const queryScanReportAll2 = (query?: ScanReportQuery): any => {
  return request({
    url: '/sfc/information/scanReport/report/search/all2',
    method: 'get',
    params: query
  });
};

export const queryScanReportAll3 = (query?: ScanReportQuery): any => {
  return request({
    url: '/sfc/information/scanReport/report/search/all3',
    method: 'get',
    params: query
  });
};

/**
 * 获取下载链接
 * @param queryParams
 */
export default function appendQueryParamsToUrl(queryParams: { [key: string]: any }): string {
  const baseUrl = '/sfc/information/scanReport/download';
  const queryString = Object.keys(queryParams)
    .filter((key) => queryParams[key] !== undefined)
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
    .join('&');
  return queryString ? `${baseUrl}?${queryString}` : baseUrl;
}

/**
 * 获取MO详情数据
 * @param moNo MO号
 */
export const getMoDetails = (moNo: string | number) => {
  return request({
    url: `/sfc/information/scanReport/mo/${moNo}/details`,
    method: 'get'
  });
};
