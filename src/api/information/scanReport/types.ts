export interface CodeRuleModel {
  id: string;
  codeType: string;
  acnCode: string;
  acnDesc: string;
  enab: string;
  rem: string;
  codingRule?: string;
  roleIds: Array<any>;
}

export interface InfoReportGsVO {
  /**
   * mo
   */
  moNo: string | number;

  /**
   * 客户
   */
  zpType: string;

  /**
   * 全部标准工时
   */
  basGsAll: string | number;

  /**
   * 扫描的全部工时
   */
  smGsAll: string | number;
  /**
   * 未扫描的全部工时
   */
  syGsAll: string | number;
}

/**
 * MO详情数据
 */
export interface MoDetailVO {
  /**
   * MO号
   */
  moNo: string;

  /**
   * 装配类型
   */
  zpType: string;

  /**
   * 工序名称
   */
  pname: string;

  /**
   * 标准工时
   */
  basGsAll: number;

  /**
   * 已扫描工时
   */
  smGsAll: number;

  /**
   * 未扫描工时
   */
  syGsAll: number;
}

/**
 * MO详情数据
 */
export interface ScanMoDetailVO {
  /**
   * MO号
   */
  moNo: string;

  /**
   * 装配类型
   */
  zpType: string;

  /**
   * 工序名称
   */
  pname: string;

  /**
   * 标准工时
   */
  basGsAll: number;

  bdtm: number;

  edtm: number;

  /**
   * 已扫描工时
   */
  smGsAll: number;

  /**
   * 未扫描工时
   */
  sjGs: number;
}

export interface ScanReportVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 客户
   */
  custNo: string;

  /**
   * 产品类型
   */
  prdType: string;

  /**
   * 产品名
   */
  cusName: string;

  /**
   * MO号
   */
  moNo: string;

  /**
   * 关联号
   */
  hMoNo: string;

  /**
   * DR号
   */
  drNo: string;

  /**
   * DR项
   */
  drItm: string;

  /**
   * SO号
   */
  soNo: string;

  /**
   * SO项次
   */
  soItm: string;

  /**
   * 品号
   */
  prdNo: string;

  /**
   * 品名
   */
  prdName: string;

  /**
   * 订单数量
   */
  oQty: number;

  /**
   * 生产数量
   */
  mQty: number;

  /**
   * 楼层
   */
  floorNo: string;

  /**
   * 模组序号
   */
  mzItm: string;

  /**
   * 模组名称
   */
  mzName: string;

  /**
   * 工站图号
   */
  wksDwg: string;

  /**
   * 统计类型
   */
  contType: string;

  /**
   * 工序号
   */
  pType: string;

  /**
   * 工站名称
   */
  pName: string;

  /**
   * 开始时间
   */
  bDtm: string;

  /**
   * 结束时间
   */
  eDtm: string;

  /**
   * 工号
   */
  empNo: string;

  /**
   * 姓名
   */
  empName: string;

  /**
   * 总进度
   */
  sumProd: number;

  /**
   * 当前进度
   */
  curProd: number;

  /**
   * 良品数量
   */
  qtyOk: number;

  /**
   * 不良数量
   */
  qtyNg: number;

  /**
   * 进度工时
   */
  gyGs: number;

  /**
   * 实际工时
   */
  sjGs: number;

  /**
   * 不良代码
   */
  reasonCode: string;

  /**
   * 不良原因
   */
  reasonName: string;

  /**
   * 条码
   */
  barCode: string;

  /**
   * 调试工时
   */
  testGs: number;

  /**
   * 标配人力
   */
  bpRy: number;

  /**
   * 标准工时
   */
  basGs: number;

  /**
   * 累计工时
   */
  wkGs: number;

  /**
   * 备注
   */
  rem: string;

  /**
   * 模组条码
   */
  mzBarcode: string;

  /**
   * 关联号
   */
  sopBatno: string;

  /**
   * 装配类型
   */
  zpType: string;

  /**
   * 部门
   */
  dep: string;

  /**
   * 人员类别
   */
  sType: string;

  /**
   * 间接直接
   */
  jjzjRy: number;

  /**
   * 工作中心
   */
  arbpl: string;

  /**
   * MO状态
   */
  act14: string;

  /**
   * 出货日期
   */
  act15: string;

  /**
   * 标志
   */
  flag: number;

  /**
   * 装配状态
   */
  mSta: string;

  /**
   * 主机
   */
  host: string;

  /**
   * 操作时间
   */
  sysdt: string;

  /**
   * 产品组
   */
  prdGroup: string;

  /**
   * 参与人员工号
   */
  participantNo: string;

  /**
   * 参与人员姓名
   */
  participantName: string;

  /**
   * 自定义备用
   */
  def0: number;
}

export interface ScanReportForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 工作单号
   */
  jobNo?: string;

  /**
   * 客户
   */
  custNo?: string;

  /**
   * 产品类型
   */
  prdType?: string;

  /**
   * 产品名
   */
  cusName?: string;

  /**
   * MO号
   */
  moNo?: string;

  /**
   * 关联号
   */
  hMoNo?: string;

  /**
   * DR号
   */
  drNo?: string;

  /**
   * DR项
   */
  drItm?: string;

  /**
   * SO号
   */
  soNo?: string;

  /**
   * SO项次
   */
  soItm?: string;

  /**
   * 品号
   */
  prdNo?: string;

  /**
   * 品名
   */
  prdName?: string;

  /**
   * 订单数量
   */
  oQty?: number;

  /**
   * 生产数量
   */
  mQty?: number;

  /**
   * 楼层
   */
  floorNo?: string;

  /**
   * 模组序号
   */
  mzItm?: string;

  /**
   * 模组名称
   */
  mzName?: string;

  /**
   * 工站图号
   */
  wksDwg?: string;

  /**
   * 统计类型
   */
  contType?: string;

  /**
   * 工序号
   */
  pType?: string;

  /**
   * 工站名称
   */
  pName?: string;

  /**
   * 开始时间
   */
  bDtm?: string;

  /**
   * 结束时间
   */
  eDtm?: string;

  /**
   * 工号
   */
  empNo?: string;

  /**
   * 姓名
   */
  empName?: string;

  /**
   * 总进度
   */
  sumProd?: number;

  /**
   * 当前进度
   */
  curProd?: number;

  /**
   * 良品数量
   */
  qtyOk?: number;

  /**
   * 不良数量
   */
  qtyNg?: number;

  /**
   * 进度工时
   */
  gyGs?: number;

  /**
   * 实际工时
   */
  sjGs?: number;

  /**
   * 不良代码
   */
  reasonCode?: string;

  /**
   * 不良原因
   */
  reasonName?: string;

  /**
   * 条码
   */
  barCode?: string;

  /**
   * 调试工时
   */
  testGs?: number;

  /**
   * 标配人力
   */
  bpRy?: number;

  /**
   * 标准工时
   */
  basGs?: number;

  /**
   * 累计工时
   */
  wkGs?: number;

  /**
   * 备注
   */
  rem?: string;

  /**
   * 模组条码
   */
  mzBarcode?: string;

  /**
   * 关联号
   */
  sopBatno?: string;

  /**
   * 装配类型
   */
  zpType?: string;

  /**
   * 部门
   */
  dep?: string;

  /**
   * 人员类别
   */
  sType?: string;

  /**
   * 间接直接
   */
  jjzjRy?: number;

  /**
   * 工作中心
   */
  arbpl?: string;

  /**
   * MO状态
   */
  act14?: string;

  /**
   * 出货日期
   */
  act15?: string;

  /**
   * 标志
   */
  flag?: number;

  /**
   * 装配状态
   */
  mSta?: string;

  /**
   * 主机
   */
  host?: string;

  /**
   * 操作时间
   */
  sysdt?: string;

  /**
   * 产品组
   */
  prdGroup?: string;

  /**
   * 参与人员工号
   */
  participantNo?: string;

  /**
   * 参与人员姓名
   */
  participantName?: string;

  /**
   * 自定义备用
   */
  def0?: number;
}

export interface ScanReportQuery extends PageQuery {
  /**
   * 开始时间
   */
  bDtm?: string;

  /**
   * 装配类型
   */
  zpType?: string;

  /**
   * 日期范围参数
   */
  params?: any;

  /**
   * 日期范围值
   */
  rangeValue?: string;

  /**
   * 查询类型1
   */
  selectType1?: string;

  /**
   * 查询值1
   */
  selectVal1?: string;

  /**
   * 查询类型2
   */
  selectType2?: string;

  /**
   * 查询值2
   */
  selectVal2?: string;

  /**
   * 查询类型3
   */
  selectType3?: string;

  /**
   * 查询值3
   */
  selectVal3?: string;

  /**
   * MO状态
   */
  msta?: string;

  /**
   * 楼层值1
   */
  floorValue1?: string;

  /**
   * 楼层值2
   */
  floorValue2?: string;

  /**
   * 楼层值3
   */
  floorValue3?: string;
}
