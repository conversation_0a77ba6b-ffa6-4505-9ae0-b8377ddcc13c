import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { IpqcEmpForm, IpqcEmpQuery, IpqcEmpVO } from '@/api/information/ipqcEmp/types';

/**
 * 查询IPQC人员设置列表
 * @param query
 * @returns {*}
 */

export const listIpqcEmp = (query?: IpqcEmpQuery): AxiosPromise<IpqcEmpVO[]> => {
  return request({
    url: '/sfc/information/ipqcEmp/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询IPQC人员设置详细
 * @param id
 */
export const getIpqcEmp = (id: string | number): AxiosPromise<IpqcEmpVO> => {
  return request({
    url: '/sfc/information/ipqcEmp/' + id,
    method: 'get'
  });
};

/**
 * 新增IPQC人员设置
 * @param data
 */
export const addIpqcEmp = (data: IpqcEmpForm) => {
  return request({
    url: '/sfc/information/ipqcEmp',
    method: 'post',
    data: data
  });
};

/**
 * 修改IPQC人员设置
 * @param data
 */
export const updateIpqcEmp = (data: IpqcEmpForm) => {
  return request({
    url: '/sfc/information/ipqcEmp',
    method: 'put',
    data: data
  });
};

/**
 * 删除IPQC人员设置
 * @param id
 */
export const delIpqcEmp = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/information/ipqcEmp/' + id,
    method: 'delete'
  });
};


export function getIpqcEmpOne(data: any): any {
  return request({
    url: '/sfc/information/ipqcEmp/one',
    method: 'get',
    params: data
  });
}
