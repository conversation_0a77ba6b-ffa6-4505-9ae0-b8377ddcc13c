export interface IpqcEmpVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 工号
   */
  empNo: string;

  /**
   * 姓名
   */
  empName: string;

  /**
   * 部门
   */
  dept: string;

  /**
   * 部门代号
   */
  dep: string;

  /**
   * 小组长
   */
  groupLeader: string;

}

export interface IpqcEmpForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 工号
   */
  empNo?: string;

  /**
   * 姓名
   */
  empName?: string;

  /**
   * 部门
   */
  dept?: string;

  /**
   * 部门代号
   */
  dep?: string;

  /**
   * 小组长
   */
  groupLeader?: string;

}

export interface IpqcEmpQuery extends PageQuery {

  /**
   * 工号
   */
  empNo?: string;

  /**
   * 姓名
   */
  empName?: string;

  /**
   * 部门
   */
  dept?: string;

  /**
   * 部门代号
   */
  dep?: string;

  /**
   * 小组长
   */
  groupLeader?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}



