import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CodeRuleForm, CodeRuleModel, CodeRuleQuery, CodeRuleVO } from '@/api/information/codeRule/types';

/**
 * 查询类别设置列表
 * @param query
 * @returns {*}
 */

export const listCodeRule = (query?: CodeRuleQuery): AxiosPromise<CodeRuleVO[]> => {
  return request({
    url: '/sfc/information/codeRule/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询类别设置详细
 * @param id
 */
export const getCodeRule = (id: string | number): AxiosPromise<CodeRuleVO> => {
  return request({
    url: '/sfc/information/codeRule/' + id,
    method: 'get'
  });
};

/**
 * 新增类别设置
 * @param data
 */
export const addCodeRule = (data: CodeRuleForm) => {
  return request({
    url: '/sfc/information/codeRule',
    method: 'post',
    data: data
  });
};

/**
 * 修改类别设置
 * @param data
 */
export const updateCodeRule = (data: CodeRuleForm) => {
  return request({
    url: '/sfc/information/codeRule',
    method: 'put',
    data: data
  });
};

/**
 * 删除类别设置
 * @param id
 */
export const delCodeRule = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/information/codeRule/' + id,
    method: 'delete'
  });
};

/**
 * 根据类型获取数据
 * @param type
 */
export function getCodeRuleList(type: string): AxiosPromise<CodeRuleModel[]> {
  return request({
    url: `/sfc/information/codeRule/type/${type}`,
    method: 'get'
  });
}
/**
 * 查询类别设置列表
 * @returns {*}
 */

export const getBarcodeTypes = (): AxiosPromise<Array<string>> => {
  return request({
    url: '/sfc/information/codeRule/barcode/types',
    method: 'get'
  });
};

/**
 * 查询标识设置列表
 * @returns {*}
 */

export const getBarcodeAncTypes = (): AxiosPromise<Array<string>> => {
  return request({
    url: '/sfc/information/codeRule/barcode/ancTypes',
    method: 'get'
  });
};
