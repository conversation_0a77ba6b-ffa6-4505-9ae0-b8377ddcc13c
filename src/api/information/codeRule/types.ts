export interface CodeRuleVO {
  /**
   * 条码类型
   */
  codeType: string;

  /**
   * 标志
   */
  acnCode: string;

  /**
   * 描述
   */
  acnDesc: string;

  /**
   * 是否可用
   */
  enab: string;

  codingRule: string;

  /**
   * 备注
   */
  rem: string;
}

export interface CodeRuleForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 条码类型
   */
  codeType?: string;

  /**
   * 标志
   */
  acnCode?: string;

  /**
   * 描述
   */
  acnDesc?: string;

  codingRule?: string;
  /**
   * 是否可用
   */
  enab?: string;

  /**
   * 备注
   */
  rem?: string;
}

export interface CodeRuleQuery extends PageQuery {
  /**
   * 条码类型
   */
  codeType?: string;

  /**
   * 标志
   */
  acnCode?: string;

  /**
   * 描述
   */
  acnDesc?: string;

  /**
   * 是否可用
   */
  enab?: string;

  /**
   * 备注
   */
  rem?: string;

  codingRule?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}

export interface CodeRuleModel {
  id: string;
  codeType: string;
  acnCode: string;
  acnDesc: string;
  enab: string;
  rem: string;
  codingRule?: string;
}
