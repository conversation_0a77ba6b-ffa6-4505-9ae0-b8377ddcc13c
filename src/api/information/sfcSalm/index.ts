import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SfcSalmForm, SfcSalmQuery, SfcSalmVO } from '@/api/information/sfcSalm/types';

/**
 * 查询人员设定记录列表
 * @param query
 * @returns {*}
 */

export const listSfcSalm = (query?: SfcSalmQuery): AxiosPromise<SfcSalmVO[]> => {
  return request({
    url: '/sfc/information/sfcSalm/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询人员设定记录详细
 * @param id
 */
export const getSfcSalm = (id: string | number): AxiosPromise<SfcSalmVO> => {
  return request({
    url: '/sfc/information/sfcSalm/' + id,
    method: 'get'
  });
};

/**
 * 新增人员设定记录
 * @param data
 */
export const addSfcSalm = (data: SfcSalmForm) => {
  return request({
    url: '/sfc/information/sfcSalm',
    method: 'post',
    data: data
  });
};

/**
 * 修改人员设定记录
 * @param data
 */
export const updateSfcSalm = (data: SfcSalmForm) => {
  return request({
    url: '/sfc/information/sfcSalm',
    method: 'put',
    data: data
  });
};

/**
 * 删除人员设定记录
 * @param id
 */
export const delSfcSalm = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/information/sfcSalm/' + id,
    method: 'delete'
  });
};
