export interface SfcSalmVO {
  /**
   *
   */
  id: string | number;

  /**
   * 工号
   */
  salNo: string;

  /**
   * 姓名
   */
  name: string;

  /**
   * 类型
   */
  sType: string;

  /**
   * 分组
   */
  sGroup: string;

  /**
   * 部门
   */
  dep: string;

  /**
   * 离职日期
   */
  dutOtD: string;

  /**
   * 操作姓名
   */
  usrName: string;

  /**
   * 主机
   */
  host: string;

  /**
   * 操作时间
   */
  sysdt: string;

  /**
   * 标志
   */
  flag: number;
}

export interface SfcSalmForm extends BaseEntity {
  /**
   *
   */
  id?: string | number;

  /**
   * 工号
   */
  salNo?: string;

  /**
   * 姓名
   */
  name?: string;

  /**
   * 类型
   */
  sType?: string;

  /**
   * 分组
   */
  sGroup?: string;

  /**
   * 部门
   */
  dep?: string;

  /**
   * 离职日期
   */
  dutOtD?: string;

  /**
   * 操作姓名
   */
  usrName?: string;

  /**
   * 主机
   */
  host?: string;

  /**
   * 操作时间
   */
  sysdt?: string;

  /**
   * 标志
   */
  flag?: number;
}

export interface SfcSalmQuery extends PageQuery {
  /**
   * 工号
   */
  salNo?: string;

  /**
   * 姓名
   */
  name?: string;

  /**
   * 类型
   */
  sType?: string;

  /**
   * 分组
   */
  sGroup?: string;

  /**
   * 部门
   */
  dep?: string;

  /**
   * 离职日期
   */
  dutOtD?: string;

  /**
   * 操作姓名
   */
  usrName?: string;

  /**
   * 主机
   */
  host?: string;

  /**
   * 操作时间
   */
  sysdt?: string;

  /**
   * 标志
   */
  flag?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
