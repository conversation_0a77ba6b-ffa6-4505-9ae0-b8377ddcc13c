import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RygstjbForm, RygstjbQuery, RygstjbVO } from '@/api/information/rygstjb/types';

/**
 * 查询装配工时统计表列表
 * @param query
 * @returns {*}
 */

export const listRygstjb = (query?: RygstjbQuery): AxiosPromise<RygstjbVO[]> => {
  return request({
    url: '/sfc/information/rygstjb/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询装配工时统计表统计数据
 * @param query
 */
export const statisticWork = (query?: RygstjbQuery): AxiosPromise<RygstjbVO[]> => {
  return request({
    url: '/sfc/information/rygstjb/statistic',
    method: 'get',
    params: query
  });
};

/**
 * 查询装配工时统计表详细
 * @param id
 */
export const getRygstjb = (id: string | number): AxiosPromise<RygstjbVO> => {
  return request({
    url: '/sfc/information/rygstjb/' + id,
    method: 'get'
  });
};

/**
 * 新增装配工时统计表
 * @param data
 */
export const addRygstjb = (data: RygstjbForm) => {
  return request({
    url: '/sfc/information/rygstjb',
    method: 'post',
    data: data
  });
};

/**
 * 修改装配工时统计表
 * @param data
 */
export const updateRygstjb = (data: RygstjbForm) => {
  return request({
    url: '/sfc/information/rygstjb',
    method: 'put',
    data: data
  });
};

/**
 * 删除装配工时统计表
 * @param id
 */
export const delRygstjb = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/information/rygstjb/' + id,
    method: 'delete'
  });
};
