export interface RygstjbVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 统计类型
   */
  cntType: string;

  /**
   * 统计日期
   */
  sysDt: string;

  /**
   * 直接标准人数
   */
  zjStandRs: number;

  /**
   * 间接标准人数
   */
  jjStandRs: number;

  /**
   * 标准总人数
   */
  sumStandRs: number;

  /**
   * 直接实际出勤人数
   */
  zjSjcqRs: number;

  /**
   * 间接实际出勤人数
   */
  jjSjcqRs: number;

  /**
   * 外援人数
   */
  wyRs: number;

  /**
   * 实际出勤部人数
   */
  sumSjcqRs: number;

  /**
   * 直接出勤工时
   */
  zjSjcqGs: number;

  /**
   * 直接扫描人数
   */
  zjSmgs: number;

  /**
   * 直接占比
   */
  zjZb: number;

  /**
   * 损失工时
   */
  ssGs: number;

  /**
   * 未扫描人数
   */
  wsmRs: number;

  /**
   * 实际出勤总工时
   */
  sumSjcqGs: number;

  /**
   * 直接扫描人数
   */
  zjSmRs: number;
}

export interface RygstjbForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 统计类型
   */
  cntType?: string;

  /**
   * 统计日期
   */
  sysDt?: string;

  /**
   * 直接标准人数
   */
  zjStandRs?: number;

  /**
   * 间接标准人数
   */
  jjStandRs?: number;

  /**
   * 标准总人数
   */
  sumStandRs?: number;

  /**
   * 直接实际出勤人数
   */
  zjSjcqRs?: number;

  /**
   * 间接实际出勤人数
   */
  jjSjcqRs?: number;

  /**
   * 外援人数
   */
  wyRs?: number;

  /**
   * 实际出勤部人数
   */
  sumSjcqRs?: number;

  /**
   * 直接出勤工时
   */
  zjSjcqGs?: number;

  /**
   * 直接扫描人数
   */
  zjSmgs?: number;

  /**
   * 直接占比
   */
  zjZb?: number;

  /**
   * 损失工时
   */
  ssGs?: number;

  /**
   * 未扫描人数
   */
  wsmRs?: number;

  /**
   * 实际出勤总工时
   */
  sumSjcqGs?: number;

  /**
   * 直接扫描人数
   */
  zjSmRs?: number;
}

export interface RygstjbQuery extends PageQuery {
  /**
   * 统计类型
   */
  cntType?: string;

  /**
   * 统计日期
   */
  sysDt?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
