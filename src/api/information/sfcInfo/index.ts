import { AxiosPromise } from 'axios';
import request from '@/utils/request';

export const BASE_URL = '/sfc/information/sfcInfo';
export const INFO_TS_HEAD_URL = `${BASE_URL}/ts`;
export const INFO_IPQC_HEAD_URL = `${BASE_URL}/ipqc`;
export const INFO_EQ_HEAD_URL = `${BASE_URL}/equipment`;
export const INFO_REPORT_HEAD_URL = `${BASE_URL}/report`;
export const INFO_NOFINISH_HEAD_URL = `${BASE_URL}/noFinish`;
export const INFO_TABLE_URL = `${BASE_URL}/data`;
export const INFO_REPORT_QUERY_URL = `${BASE_URL}/report/search`;
export const INFO_REPORT_QUERY_ALL_URL = `${BASE_URL}/report/search/all`;
export const INFO_REPORT_QUERY_ALL2_URL = `${BASE_URL}/report/search/all2`;
export const INFO_REPORT_QUERY_ALL3_URL = `${BASE_URL}/report/search/all3`;
export const INFO_REPORT_DL_URL = `${BASE_URL}/report/download`;
export const INFO_REPORT_EX_URL = `${BASE_URL}/report/export`;

/**
 * 保存信息
 * @returns 结果
 */
export interface SfcInfoModel {
  id?: string;
  tid?: number;
  zpType: string;
  floorNo: string;
  cusName: string;
  moNo: string;
  wksDwg: string;
  pType: string;
  pName: string;
  empNo: string;
  empName: string;
  bDtm: string;
  eDtm: string;
  sjGs: string;
  gyGs?: string;
  qtyOk: string;
  qtyNg?: string;
  reasonCode?: string;
  reasonName?: string;
  sumProd?: string;
  curProd?: string;
  barCode: string;
  flag: string;
  wkGs: string;
  basGs?: string;
  bpRy?: string;
  dep: string;
  arbpl: string;
  prdGroup: string;
  oQty: string;
}

/**
 * 保存
 * @param data
 * @returns
 */
export function saveInfos(data: any): AxiosPromise<any> {
  return request({
    url: `${BASE_URL}/add`,
    method: 'post',
    data: data
  });
}

/**
 * 保存
 * @param data
 * @returns
 */
export function saveAnotherInfos(data: any): AxiosPromise<any> {
  return request({
    url: `${BASE_URL}/add/another`,
    method: 'post',
    data: data
  });
}

// /**
//  * 保存
//  * @param data
//  * @returns
//  */
// export function updateInfos(data: any): Promise<HttpResponse.ResponseData> {
//   return axios.put(`${BASE_URL}/update`, data);
// }

/**
 * 查询记录
 * @returns
 * @param data
 */
export function getInfos(data: object = {}): AxiosPromise<any> {
  return request({
    url: `${INFO_TABLE_URL}`,
    method: 'get',
    params: data
  });
}

/**
 * 查询记录
 * @returns
 * @param data
 */
export function getInfosByBarcodeData(data: object = {}): any {
  return request({
    url: `${BASE_URL}/barcode`,
    method: 'get',
    params: data
  });
}

// /**
//  * 查询记录
//  * @returns
//  * @param data
//  */
// export function getAbnormalInfo(data: object = {}): Promise<HttpResponse.ResponseData> {
//   return axios.get(`${INFO_NOFINISH_HEAD_URL}`, {params: data});
// }


