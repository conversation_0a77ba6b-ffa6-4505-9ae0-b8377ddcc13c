export interface FileListVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 序号
   */
  fileItm: number;

  /**
   * 文件名
   */
  fileListName: string;

  /**
   * 类别
   */
  fileType: string;

  /**
   * 标识
   */
  flag: string;

  /**
   * 备注
   */
  rem: string;

}

export interface FileListForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 序号
   */
  fileItm?: number;

  /**
   * 文件名
   */
  fileListName?: string;

  /**
   * 类别
   */
  fileType?: string;

  /**
   * 标识
   */
  flag?: string;

  /**
   * 备注
   */
  rem?: string;

}

export interface FileListQuery extends PageQuery {

  /**
   * 序号
   */
  fileItm?: number;

  /**
   * 文件名
   */
  fileListName?: string;

  /**
   * 类别
   */
  fileType?: string;

  /**
   * 标识
   */
  flag?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}


export interface FileInfoModel {
  id?: string;
  moNo:string;
  fileItm: number;
  fileListName: string;
  flag: string;
  chkDept: string;
  host: string;
  sysdt: string;
}
