import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { FileListForm, FileListQuery, FileListVO } from '@/api/information/fileList/types';

/**
 * 查询文件清单设置列表
 * @param query
 * @returns {*}
 */

export const listFileList = (query?: FileListQuery): AxiosPromise<FileListVO[]> => {
  return request({
    url: '/sfc/information/fileList/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询文件清单设置详细
 * @param id
 */
export const getFileList = (id: string | number): AxiosPromise<FileListVO> => {
  return request({
    url: '/sfc/information/fileList/' + id,
    method: 'get'
  });
};

/**
 * 新增文件清单设置
 * @param data
 */
export const addFileList = (data: FileListForm) => {
  return request({
    url: '/sfc/information/fileList',
    method: 'post',
    data: data
  });
};

/**
 * 修改文件清单设置
 * @param data
 */
export const updateFileList = (data: FileListForm) => {
  return request({
    url: '/sfc/information/fileList',
    method: 'put',
    data: data
  });
};

/**
 * 删除文件清单设置
 * @param id
 */
export const delFileList = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/information/fileList/' + id,
    method: 'delete'
  });
};
