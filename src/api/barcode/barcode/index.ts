import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { BarcodeForm, BarcodeQuery, BarcodeVO } from '@/api/barcode/barcode/types';

/**
 * 查询非生产条码列表
 * @param query
 * @returns {*}
 */

export const listBarcode = (query?: BarcodeQuery): AxiosPromise<BarcodeVO[]> => {
  return request({
    url: '/sfc/information/barcode/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询非生产条码详细
 * @param id
 */
export const getBarcode = (id: string | number): AxiosPromise<BarcodeVO> => {
  return request({
    url: '/sfc/information/barcode/' + id,
    method: 'get'
  });
};

/**
 * 新增非生产条码
 * @param data
 */
export const addBarcode = (data: BarcodeForm) => {
  return request({
    url: '/sfc/information/barcode',
    method: 'post',
    data: data
  });
};

/**
 * 修改非生产条码
 * @param data
 */
export const updateBarcode = (data: BarcodeForm) => {
  return request({
    url: '/sfc/information/barcode',
    method: 'put',
    data: data
  });
};

/**
 * 删除非生产条码
 * @param id
 */
export const delBarcode = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/information/barcode/' + id,
    method: 'delete'
  });
};
