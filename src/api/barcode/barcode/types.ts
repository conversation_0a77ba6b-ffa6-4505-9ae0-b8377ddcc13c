export interface BarcodeVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 装配类型
   */
  actType: string;

  /**
   * 条码类型
   */
  barType: string;

  /**
   * SO号
   */
  soNo: string;

  /**
   * SO项次
   */
  soItm: string;

  /**
   * MO号
   */
  moNo: string;

  /**
   * 关联号
   */
  hmoNo: string;

  /**
   * 产品名
   */
  prdName: string;

  /**
   * 产品型号
   */
  prdType: string;

  /**
   * 导入图号
   */
  dwgNo: string;

  /**
   * 工序号p_type
   */
  proType: string;

  /**
   * 工序名称p_name
   */
  proName: string;

  /**
   * 数量
   */
  qty: number;

  /**
   * 单位
   */
  ut: string;

  /**
   * MO图号
   */
  moDwgno: string;

  /**
   * 条码
   */
  barCode: string;

  /**
   * 调试工时
   */
  testGs: number;

  /**
   * 标配人员
   */
  bpRy: number;

  /**
   * 标准工时
   */
  basGs: number;

  /**
   * 累计工时
   */
  wkGs: number;

  /**
   * 关联订单号
   */
  sopMo: string;

  /**
   * 关联号
   */
  sopBatno: string;

  /**
   * 主机
   */
  host: string;

  /**
   * 操作时间
   */
  sysdt: string;

  /**
   * 操作姓名
   */
  usrName: string;

  /**
   * 文件名
   */
  sfileName: string;

  /**
   * 失效日期
   */
  endDate: string;

  /**
   * 操作工号
   */
  usr: string;
}

export interface BarcodeForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 装配类型
   */
  actType?: string;

  /**
   * 条码类型
   */
  barType?: string;

  /**
   * SO号
   */
  soNo?: string;

  /**
   * SO项次
   */
  soItm?: string;

  /**
   * MO号
   */
  moNo?: string;

  /**
   * 关联号
   */
  hmoNo?: string;

  /**
   * 产品名
   */
  prdName?: string;

  /**
   * 产品型号
   */
  prdType?: string;

  /**
   * 导入图号
   */
  dwgNo?: string;

  /**
   * 工序号p_type
   */
  proType?: string;

  /**
   * 工序名称p_name
   */
  proName?: string;

  /**
   * 数量
   */
  qty?: number;

  /**
   * 单位
   */
  ut?: string;

  /**
   * MO图号
   */
  moDwgno?: string;

  /**
   * 条码
   */
  barCode?: string;

  /**
   * 调试工时
   */
  testGs?: number;

  /**
   * 标配人员
   */
  bpRy?: number;

  /**
   * 标准工时
   */
  basGs?: number;

  /**
   * 累计工时
   */
  wkGs?: number;

  /**
   * 关联订单号
   */
  sopMo?: string;

  /**
   * 关联号
   */
  sopBatno?: string;

  /**
   * 主机
   */
  host?: string;

  /**
   * 操作时间
   */
  sysdt?: string;

  /**
   * 操作姓名
   */
  usrName?: string;

  /**
   * 文件名
   */
  sfileName?: string;

  /**
   * 失效日期
   */
  endDate?: string;

  /**
   * 操作工号
   */
  usr?: string;
}

export interface BarcodeQuery extends PageQuery {
  /**
   * 装配类型
   */
  actType?: string;

  /**
   * MO号
   */
  moNo?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
