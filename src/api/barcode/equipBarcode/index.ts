import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { EquipBarcodeForm, EquipBarcodeVO } from '@/api/barcode/equipBarcode/types';

/**
 * 查询条码制作列表
 * @param query
 * @returns {*}
 */

export const listEquipBarcode = (query?: any): AxiosPromise<EquipBarcodeVO[]> => {
  return request({
    url: '/sfc/information/barcode/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询条码制作详细
 * @param id
 */
export const getEquipBarcode = (id: string | number): AxiosPromise<EquipBarcodeVO> => {
  return request({
    url: '/sfc/information/barcode/' + id,
    method: 'get'
  });
};

/**
 * 新增条码制作
 * @param data
 */
export const addEquipBarcode = (data: EquipBarcodeForm) => {
  return request({
    url: '/sfc/information/barcode',
    method: 'post',
    data: data
  });
};

/**
 * 修改条码制作
 * @param data
 */
export const updateEquipBarcode = (data: EquipBarcodeForm) => {
  return request({
    url: '/sfc/information/barcode',
    method: 'put',
    data: data
  });
};

/**
 * 删除条码制作
 * @param id
 */
export const delEquipBarcode = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/information/barcode/' + id,
    method: 'delete'
  });
};

const BASE_URL = '/sfc/information/barcode';

export const HEAD_URL = `${BASE_URL}/sop`;
export const CREATE_BARCODE_URL = `${BASE_URL}/createBarcode`;
export const EXPORT_BARCODE_URL = `${BASE_URL}/export`;
export const QUERY_BARCODE_URL = `${BASE_URL}/query`;
export const GENERATE_BARCODE_URL = `${BASE_URL}/generate`;
export const ABNORMAL_URL = `${BASE_URL}/notScan`;
export const BARCODE_HEAD_URL = `${BASE_URL}/list`;
export const UPLOAD_SOP_URL = `${BASE_URL}/upload`;

export interface BarcodeModel {
  id?: number;
  actType: string;
  barType: string;
  soNo: string;
  soItm: string;
  moNo: string;
  hmoNo: string;
  prdName: string;
  prdType: string;
  dwgNo: string;
  proType: string;
  proName: string;
  qty: string;
  ut?: string;
  moDwgno: string;
  barCode: string;
  testGs?: string;
  bpRy?: string;
  basGs?: string;
  wkGs?: string;
  sopBatno: string;
  host: string;
  sysdt: string;
  usrName?: string;
  sfileName?: string;
  endDate: string;
  usr: string;
}

/**
 * 生成条码
 * @returns
 */
export const createBarcodeInfo = (fileName: any, mo: string, qty: string, dwgno: string, batNo: any): AxiosPromise<any> => {
  return request({
    url: CREATE_BARCODE_URL,
    method: 'post',
    data: {
      'fileName': fileName,
      'mo': mo,
      'qty': qty,
      'dwgno': dwgno,
      'sopBatno': batNo
    }
  });
};

export const queryBarcodeInfo = (query: any): AxiosPromise<any> => {
  return request({
    url: `/sfc/information/barcode/query`,
    method: 'get',
    params: query
  });
};

export const queryAnotherBarcodeInfo = (query: any): AxiosPromise<any> => {
  return request({
    url: `/sfc/information/barcode/query/another`,
    method: 'get',
    params: query
  });
};

// export function getAbnormalBarcode(data: any): Promise<HttpResponse.ResponseData> {
//   return axios.get(ABNORMAL_URL, {params: data});
// }
