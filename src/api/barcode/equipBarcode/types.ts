export interface EquipBarcodeVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 装配类型
   */
  actType: string;

  /**
   * 条码类型
   */
  barType: string;

  /**
   * MO号
   */
  moNo: string;

  /**
   * 产品名
   */
  prdName: string;

  /**
   * 产品型号
   */
  prdType: string;

  /**
   * 工序号
   */
  proType: string;

  /**
   * 工序名称
   */
  proName: string;

  /**
   * 数量
   */
  qty: number;

  /**
   * 单位
   */
  ut: string;

  /**
   * MO图号
   */
  moDwgno: string;

  /**
   * 条码
   */
  barCode: string;

  /**
   * 标配人员
   */
  bpRy: number;

  /**
   * 标准工时
   */
  basGs: number;

  /**
   * 关联号
   */
  sopBatno: string;

  /**
   * 操作姓名
   */
  usrName: string;

  /**
   * 文件名
   */
  sfileName: string;

  /**
   * 失效日期
   */
  endDate: string;

  /**
   * 操作工号
   */
  usr: string;
}

export interface EquipBarcodeForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;
}

export interface EquipBarcodeQuery extends PageQuery {
  /**
   * MO号
   */
  moNo?: string;
  /**
   * 文件名
   */
  sfileName?: string;

  /**
   * 工站图号
   */
  dwgNo?: string;

  /**
   * 客户sn号
   */
  customerSn?: string;
  endDate?: any;

  /**
   * 日期范围参数
   */
  params?: any;
}
