import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TestGxForm, TestGxQuery, TestGxVO } from '@/api/information/testGx/types';
import { MoTsGx } from '@/views/barcode/ioSop/index.vue';

/**
 * 查询调试SOP设置列表
 * @param query
 * @returns {*}
 */

export const listTestGx = (query?: TestGxQuery): AxiosPromise<TestGxVO[]> => {
  return request({
    url: '/sfc/information/testGx/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询调试SOP设置详细
 * @param id
 */
export const getTestGx = (id: string | number): AxiosPromise<TestGxVO> => {
  return request({
    url: '/sfc/information/testGx/' + id,
    method: 'get'
  });
};

/**
 * 生成条码
 * @returns
 */
export const generateBarcode = (moTsGx: MoTsGx) => {
  return request({
    url: '/sfc/information/moTsGx/generateTsBarcode',
    method: 'post',
    data: moTsGx
  });
};

/**
 * 新增调试SOP设置
 * @param data
 */
export const addTestGx = (data: TestGxForm) => {
  return request({
    url: '/sfc/information/testGx',
    method: 'post',
    data: data
  });
};

/**
 * 修改调试SOP设置
 * @param data
 */
export const updateTestGx = (data: TestGxForm) => {
  return request({
    url: '/sfc/information/testGx',
    method: 'put',
    data: data
  });
};

/**
 * 删除调试SOP设置
 * @param id
 */
export const delTestGx = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/information/testGx/' + id,
    method: 'delete'
  });
};

/**
 * 下载,直接扔进去minio地址
 * @param urlStr 链接路径
 * @param fileName 文件名
 * @returns
 */
export function handleDownload(url: string, fileName: string) {
  fetch(url)
    .then((response) => response.blob())
    .then((blob) => {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.click();
      window.URL.revokeObjectURL(url);
    });
}
