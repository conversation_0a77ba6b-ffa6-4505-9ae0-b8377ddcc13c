export interface ScanMasterVO {
  /**
   * 自增id
   */
  id: string | number;

  /**
   * master表主键id
   */
  mid: string | number;

  /**
   * 状态
   */
  moState: string;

  /**
   * 备注
   */
  moRem: string;

  /**
   * CUS_REM
   */
  cusRem: string;

  /**
   * 接单日期
   */
  bargainDate: string;

  /**
   * 客户代码
   */
  custNo: string;

  /**
   * 项目号
   */
  pmNo: string;

  /**
   * 客户PO号
   */
  poNo: string;

  /**
   * DR号
   */
  drNo: string;

  /**
   * DR日期
   */
  drDd: string;

  /**
   * DR行号
   */
  drItm: string;

  /**
   * 客户要求交期
   */
  deliveryDate: string;

  /**
   * 承诺交期
   */
  promiseDate: string;

  /**
   * 再次承诺
   */
  secondDate: string;

  /**
   * 工厂
   */
  plant: string;

  /**
   * 行业
   */
  trade: string;

  /**
   * 产品组
   */
  productGroup: string;

  /**
   * 订单类别
   */
  orderType: string;

  /**
   * 类别描述
   */
  orderDescription: string;

  /**
   * 海外特急
   */
  extraUrgent: string;

  /**
   * SO号
   */
  soNo: string;

  /**
   * SO行号
   */
  soItm: string;

  /**
   * MO号
   */
  moNo: string;

  /**
   * 品号
   */
  partId: string | number;

  /**
   * 仓库
   */
  moWh: string;

  /**
   * 零件分类
   */
  partType: string;

  /**
   * 图号
   */
  drawNumber: string;

  /**
   * 版本
   */
  partVer: string;

  /**
   * 描述
   */
  desc: string;

  /**
   * PO行号
   */
  poItem: number;

  /**
   * 客户料号
   */
  custPartId: string | number;

  /**
   * 订单数量
   */
  orderQty: number;

  /**
   * 生产数量
   */
  productionQty: number;

  /**
   * 报废数量
   */
  scrapQty: number;

  /**
   * 计划类型
   */
  planType: string;

  /**
   * 制造部门
   */
  mfgDept: string;

  /**
   * 生产车间
   */
  workshop: string;

  /**
   * 合单标识
   */
  mergeMo: string;

  /**
   * 打样标识
   */
  proofMo: string;

  /**
   * 下单员
   */
  orderSalm: string;

  /**
   * 下单员姓名
   */
  orderName: string;

  /**
   * 跟单负责人
   */
  merchandiserCharge: string;

  /**
   * bom责任人
   */
  bomCharge: string;

  /**
   * 动作
   */
  act: string;

  /**
   * 当前工序
   */
  currentProcess: string;

  /**
   * 末工序时间
   */
  lastOpTime: string;

  /**
   * 工艺数量
   */
  technologyQty: number;

  /**
   * pmc要求交期
   */
  pmcReqDate: string;

  /**
   * 生产运营交期
   */
  multipleOpDate: string;

  /**
   * 要求完成日期
   */
  dueDate: string;

  /**
   * pmc要求外发日期
   */
  pmcWfDate: string;

  /**
   * 外发要求交期
   */
  wfRequestDate: string;

  /**
   * 已入仓数
   */
  inStorageQty: number;

  /**
   * 欠入仓数
   */
  outstandingInQty: number;

  /**
   * 入仓日期
   */
  inDate: string;

  /**
   * 实际交货数量
   */
  deliveryQty: number;

  /**
   * 欠交货数量
   */
  outstandingQty: number;

  /**
   * 实际交期
   */
  actualDate: string;

  /**
   * 已领出数
   */
  issueQty: number;

  /**
   * 现有库存数
   */
  stockQty: number;

  /**
   * 上层订单号
   */
  upMo: string;

  /**
   * 状态1
   */
  moState1: number;

  /**
   * 急单
   */
  urgent: string;

  /**
   * 责任工程师
   */
  dutyEngineer: string;

  /**
   * DN号
   */
  dnNo: string;

  /**
   * 成本中心
   */
  cca: string;

  /**
   * 多余件库存
   */
  surplus: number;

  /**
   * 利润中心
   */
  profitCenter: string;

  /**
   * PMC备注
   */
  pmcRem: string;

  /**
   * 项目类型
   */
  projectType: string;

  /**
   * 批号
   */
  batNo: string;

  /**
   * 是否放行
   */
  moPass: string;

  /**
   * 跟单人
   */
  merchandiser: string;

  /**
   * 更新时间
   */
  sysDt: string;

  /**
   * 单位净重
   */
  netWeight: number;

  /**
   * 材料
   */
  material: string;
}

export interface ScanMasterForm extends BaseEntity {
  /**
   * 自增id
   */
  id?: string | number;

  /**
   * master表主键id
   */
  mid?: string | number;

  /**
   * 状态
   */
  moState?: string;

  /**
   * 备注
   */
  moRem?: string;

  /**
   * CUS_REM
   */
  cusRem?: string;

  /**
   * 接单日期
   */
  bargainDate?: string;

  /**
   * 客户代码
   */
  custNo?: string;

  /**
   * 项目号
   */
  pmNo?: string;

  /**
   * 客户PO号
   */
  poNo?: string;

  /**
   * DR号
   */
  drNo?: string;

  /**
   * DR日期
   */
  drDd?: string;

  /**
   * DR行号
   */
  drItm?: string;

  /**
   * 客户要求交期
   */
  deliveryDate?: string;

  /**
   * 承诺交期
   */
  promiseDate?: string;

  /**
   * 再次承诺
   */
  secondDate?: string;

  /**
   * 工厂
   */
  plant?: string;

  /**
   * 行业
   */
  trade?: string;

  /**
   * 产品组
   */
  productGroup?: string;

  /**
   * 订单类别
   */
  orderType?: string;

  /**
   * 类别描述
   */
  orderDescription?: string;

  /**
   * 海外特急
   */
  extraUrgent?: string;

  /**
   * SO号
   */
  soNo?: string;

  /**
   * SO行号
   */
  soItm?: string;

  /**
   * MO号
   */
  moNo?: string;

  /**
   * 品号
   */
  partId?: string | number;

  /**
   * 仓库
   */
  moWh?: string;

  /**
   * 零件分类
   */
  partType?: string;

  /**
   * 图号
   */
  drawNumber?: string;

  /**
   * 版本
   */
  partVer?: string;

  /**
   * 描述
   */
  desc?: string;

  /**
   * PO行号
   */
  poItem?: number;

  /**
   * 客户料号
   */
  custPartId?: string | number;

  /**
   * 订单数量
   */
  orderQty?: number;

  /**
   * 生产数量
   */
  productionQty?: number;

  /**
   * 报废数量
   */
  scrapQty?: number;

  /**
   * 计划类型
   */
  planType?: string;

  /**
   * 制造部门
   */
  mfgDept?: string;

  /**
   * 生产车间
   */
  workshop?: string;

  /**
   * 合单标识
   */
  mergeMo?: string;

  /**
   * 打样标识
   */
  proofMo?: string;

  /**
   * 下单员
   */
  orderSalm?: string;

  /**
   * 下单员姓名
   */
  orderName?: string;

  /**
   * 跟单负责人
   */
  merchandiserCharge?: string;

  /**
   * bom责任人
   */
  bomCharge?: string;

  /**
   * 动作
   */
  act?: string;

  /**
   * 当前工序
   */
  currentProcess?: string;

  /**
   * 末工序时间
   */
  lastOpTime?: string;

  /**
   * 工艺数量
   */
  technologyQty?: number;

  /**
   * pmc要求交期
   */
  pmcReqDate?: string;

  /**
   * 生产运营交期
   */
  multipleOpDate?: string;

  /**
   * 要求完成日期
   */
  dueDate?: string;

  /**
   * pmc要求外发日期
   */
  pmcWfDate?: string;

  /**
   * 外发要求交期
   */
  wfRequestDate?: string;

  /**
   * 已入仓数
   */
  inStorageQty?: number;

  /**
   * 欠入仓数
   */
  outstandingInQty?: number;

  /**
   * 入仓日期
   */
  inDate?: string;

  /**
   * 实际交货数量
   */
  deliveryQty?: number;

  /**
   * 欠交货数量
   */
  outstandingQty?: number;

  /**
   * 实际交期
   */
  actualDate?: string;

  /**
   * 已领出数
   */
  issueQty?: number;

  /**
   * 现有库存数
   */
  stockQty?: number;

  /**
   * 上层订单号
   */
  upMo?: string;

  /**
   * 状态1
   */
  moState1?: number;

  /**
   * 急单
   */
  urgent?: string;

  /**
   * 责任工程师
   */
  dutyEngineer?: string;

  /**
   * DN号
   */
  dnNo?: string;

  /**
   * 成本中心
   */
  cca?: string;

  /**
   * 多余件库存
   */
  surplus?: number;

  /**
   * 利润中心
   */
  profitCenter?: string;

  /**
   * PMC备注
   */
  pmcRem?: string;

  /**
   * 项目类型
   */
  projectType?: string;

  /**
   * 批号
   */
  batNo?: string;

  /**
   * 是否放行
   */
  moPass?: string;

  /**
   * 跟单人
   */
  merchandiser?: string;

  /**
   * 更新时间
   */
  sysDt?: string;

  /**
   * 单位净重
   */
  netWeight?: number;

  /**
   * 材料
   */
  material?: string;
}

export interface ScanMasterQuery extends PageQuery {
  /**
   * 状态
   */
  moState?: string;

  /**
   * 备注
   */
  moRem?: string;

  /**
   * 客户代码
   */
  custNo?: string;

  /**
   * 项目号
   */
  pmNo?: string;

  /**
   * 客户PO号
   */
  poNo?: string;

  /**
   * DR号
   */
  drNo?: string;

  /**
   * MO号
   */
  moNo?: string;

  /**
   * 品号
   */
  partId?: string | number;

  /**
   * 日期范围参数
   */
  params?: any;
}
