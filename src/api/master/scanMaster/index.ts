import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ScanMasterForm, ScanMasterQuery, ScanMasterVO } from '@/api/master/scanMaster/types';

/**
 * 查询sapMaster信息列表
 * @param query
 * @returns {*}
 */

export const listScanMaster = (query?: ScanMasterQuery): AxiosPromise<ScanMasterVO[]> => {
  return request({
    url: '/sfc/master/scanMaster/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询sapMaster信息详细
 * @param id
 */
export const getScanMaster = (id: string | number): AxiosPromise<ScanMasterVO> => {
  return request({
    url: '/sfc/master/scanMaster/' + id,
    method: 'get'
  });
};

/**
 * 新增sapMaster信息
 * @param data
 */
export const addScanMaster = (data: ScanMasterForm) => {
  return request({
    url: '/sfc/master/scanMaster',
    method: 'post',
    data: data
  });
};

/**
 * 修改sapMaster信息
 * @param data
 */
export const updateScanMaster = (data: ScanMasterForm) => {
  return request({
    url: '/sfc/master/scanMaster',
    method: 'put',
    data: data
  });
};

/**
 * 删除sapMaster信息
 * @param id
 */
export const delScanMaster = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/master/scanMaster/' + id,
    method: 'delete'
  });
};


export const getOneOrderInfo = (mo: string | number): AxiosPromise<any> => {
  return request({
    url: '/sfc/master/scanMaster/one/' + mo,
    method: 'get'
  });
};
