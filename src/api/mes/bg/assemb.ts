import request from '@/utils/request';

// 获取动作列表
export function getActionListDo(data) {
    return request({
        url: '/bgExpandController/getActionList.do',
        method: 'post',
        data
    })
}

// 获取用户扫描信息
export function getUserScanInfoDo(data) {
    return request({
        url: '/bgExpandController/getUserScanInfo.do',
        method: 'post',
        data
    })
}

// 扫描工单号
export function scanMoNoDo(data) {
    return request({
        url: '/bgExpandController/scanMoNo.do',
        method: 'post',
        data
    })
}

// 获取用户信息
export function getBgUserInfoDo(data) {
    return request({
        url: '/bgExpandController/getBgUserInfo.do',
        method: 'post',
        data
    })
}

// 确认所有数据
export function updateTechnologyFileDo(data) {
    return request({
        url: '/processDoController/updateTechnologyFile.do',
        method: 'post',
        data
    })
}
