export interface ProcessVO {
  /**
   * 工序ID
   */
  processId: string | number;

  /**
   * 工序编码
   */
  processCode: string;

  /**
   * 工序名称
   */
  processName: string;

  /**
   * 工作中心
   */
  workCenter: string;

  /**
   * 是否启用
   */
  enableFlag: string;

  /**
   * 是否生成描述
   */
  descFlag: string;

  /**
   * 是否外托运
   */
  invoiceFlag: string;

  /**
   * 是否电镀
   */
  platingFlag: string;

  /**
   * 总机时是否为0
   */
  noYesHours: string;

  /**
   * 电镀类型
   */
  platingType: string;

  /**
   * 描述
   */
  description: string;

  /**
   * 急单等待时间
   */
  urgWaitTime: number;

  /**
   * 急单等待时间
   */
  waitTime: number;

  /**
   * 批量等待时间
   */
  batchWaitTime: number;

  /**
   * 备注
   */
  remark: string;
}

export interface ProcessForm extends BaseEntity {
  /**
   * 工序ID
   */
  processId?: string | number;

  /**
   * 工序编码
   */
  processCode?: string;

  /**
   * 工序名称
   */
  processName?: string;

  /**
   * 工作中心
   */
  workCenter?: string;

  /**
   * 是否启用
   */
  enableFlag?: string;

  /**
   * 是否生成描述
   */
  descFlag?: string;

  /**
   * 是否外托运
   */
  invoiceFlag?: string;

  /**
   * 是否电镀
   */
  platingFlag?: string;

  /**
   * 总机时是否为0
   */
  noYesHours?: string;

  /**
   * 电镀类型
   */
  platingType?: string;

  /**
   * 描述
   */
  description?: string;

  /**
   * 急单等待时间
   */
  urgWaitTime?: number;

  /**
   * 急单等待时间
   */
  waitTime?: number;

  /**
   * 批量等待时间
   */
  batchWaitTime?: number;

  /**
   * 备注
   */
  remark?: string;
}

export interface ProcessQuery extends PageQuery {
  /**
   * 工序编码
   */
  processCode?: string;

  /**
   * 工序名称
   */
  processName?: string;

  /**
   * 工作中心
   */
  workCenter?: string;

  /**
   * 是否启用
   */
  enableFlag?: string;

  /**
   * 是否生成描述
   */
  descFlag?: string;

  /**
   * 是否外托运
   */
  invoiceFlag?: string;

  /**
   * 是否电镀
   */
  platingFlag?: string;

  /**
   * 总机时是否为0
   */
  noYesHours?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
