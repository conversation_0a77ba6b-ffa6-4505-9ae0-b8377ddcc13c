import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ProcessForm, ProcessQuery, ProcessVO } from '@/api/mes/process/types';

/**
 * 查询工序管理列表
 * @param query
 * @returns {*}
 */

export const listProcess = (query?: ProcessQuery): AxiosPromise<ProcessVO[]> => {
  return request({
    url: '/mes/process/list',
    method: 'get',
    params: query
  });
};

export const listAllProcess = (query?: ProcessQuery): AxiosPromise<ProcessVO[]> => {
  return request({
    url: '/mes/process/listAll',
    method: 'get',
    params: query
  });
};

/**
 * 查询工序管理详细
 * @param processId
 */
export const getProcess = (processId: string | number): AxiosPromise<ProcessVO> => {
  return request({
    url: '/mes/process/' + processId,
    method: 'get'
  });
};

/**
 * 新增工序管理
 * @param data
 */
export const addProcess = (data: ProcessForm) => {
  return request({
    url: '/mes/process',
    method: 'post',
    data: data
  });
};

/**
 * 修改工序管理
 * @param data
 */
export const updateProcess = (data: ProcessForm) => {
  return request({
    url: '/mes/process',
    method: 'put',
    data: data
  });
};

/**
 * 删除工序管理
 * @param processId
 */
export const delProcess = (processId: string | number | Array<string | number>) => {
  return request({
    url: '/mes/process/' + processId,
    method: 'delete'
  });
};
