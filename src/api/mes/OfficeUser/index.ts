import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { OfficeUserForm, OfficeUserQuery, OfficeUserVO } from '@/api/mes/OfficeUser/types';

/**
 * 查询报工用户列表
 * @param query
 * @returns {*}
 */

export const listOfficeUser = (query?: OfficeUserQuery): AxiosPromise<OfficeUserVO[]> => {
  return request({
    url: '/mes/OfficeUser/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询报工用户详细
 * @param id
 */
export const getOfficeUser = (id: string | number): AxiosPromise<OfficeUserVO> => {
  return request({
    url: '/mes/OfficeUser/' + id,
    method: 'get'
  });
};

/**
 * 新增报工用户
 * @param data
 */
export const addOfficeUser = (data: OfficeUserForm) => {
  return request({
    url: '/mes/OfficeUser',
    method: 'post',
    data: data
  });
};

/**
 * 修改报工用户
 * @param data
 */
export const updateOfficeUser = (data: OfficeUserForm) => {
  return request({
    url: '/mes/OfficeUser',
    method: 'put',
    data: data
  });
};

/**
 * 删除报工用户
 * @param id
 */
export const delOfficeUser = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mes/OfficeUser/' + id,
    method: 'delete'
  });
};
