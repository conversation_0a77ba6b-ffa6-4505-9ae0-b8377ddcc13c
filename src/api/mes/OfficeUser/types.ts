export interface OfficeUserVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * 工号
   */
  workNum: string;

  /**
   * 工作人员
   */
  userName: string;

  /**
   * 联系电话
   */
  userPhone: string;

  /**
   * 办事处代码
   */
  officeLocId: string | number;

  /**
   * 办事处名称
   */
  officeLocName: string;

  /**
   * 负责人姓名
   */
  officeSupervisor: string;

}

export interface OfficeUserForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * 工号
   */
  workNum?: string;

  /**
   * 工作人员
   */
  userName?: string;

  /**
   * 联系电话
   */
  userPhone?: string;

  /**
   * 办事处代码
   */
  officeLocId?: string | number;

  /**
   * 办事处名称
   */
  officeLocName?: string;

  /**
   * 负责人姓名
   */
  officeSupervisor?: string;

}

export interface OfficeUserQuery extends PageQuery {

  /**
   * 工号
   */
  workNum?: string;

  /**
   * 工作人员
   */
  userName?: string;

  /**
   * 联系电话
   */
  userPhone?: string;

  /**
   * 办事处代码
   */
  officeLocId?: string | number;

  /**
   * 办事处名称
   */
  officeLocName?: string;

  /**
   * 负责人姓名
   */
  officeSupervisor?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



