import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { OfficeLocForm, OfficeLocQuery, OfficeLocVO } from '@/api/mes/officeLoc/types';

/**
 * 查询工作地点组列表
 * @param query
 * @returns {*}
 */

export const listOfficeLoc = (query?: OfficeLocQuery): AxiosPromise<OfficeLocVO[]> => {
  return request({
    url: '/mes/officeLoc/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询工作地点组详细
 * @param id
 */
export const getOfficeLoc = (id: string | number): AxiosPromise<OfficeLocVO> => {
  return request({
    url: '/mes/officeLoc/' + id,
    method: 'get'
  });
};

/**
 * 新增工作地点组
 * @param data
 */
export const addOfficeLoc = (data: OfficeLocForm) => {
  return request({
    url: '/mes/officeLoc',
    method: 'post',
    data: data
  });
};

/**
 * 修改工作地点组
 * @param data
 */
export const updateOfficeLoc = (data: OfficeLocForm) => {
  return request({
    url: '/mes/officeLoc',
    method: 'put',
    data: data
  });
};

/**
 * 删除工作地点组
 * @param id
 */
export const delOfficeLoc = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mes/officeLoc/' + id,
    method: 'delete'
  });
};
