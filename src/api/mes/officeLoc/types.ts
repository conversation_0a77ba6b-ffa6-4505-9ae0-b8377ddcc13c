export interface OfficeLocVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 办事处代码
   */
  officeCode: string;

  /**
   * 描述
   */
  officeDesc: string;

  /**
   * 工号
   */
  workNum: string;

  /**
   * 主管
   */
  officeSupervisor: string;

  /**
   * 工作中心
   */
  workCenter: string;

  /**
   * 办事处员工数
   */
  totalUser: string;

}

export interface OfficeLocForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 办事处代码
   */
  officeCode?: string;

  /**
   * 描述
   */
  officeDesc?: string;

  /**
   * 工号
   */
  workNum?: string;

  /**
   * 主管
   */
  officeSupervisor?: string;

  /**
   * 工作中心
   */
  workCenter?: string;

  /**
   * 办事处员工数
   */
  totalUser?: string;

}

export interface OfficeLocQuery extends PageQuery {

  /**
   * 办事处代码
   */
  officeCode?: string;

  /**
   * 描述
   */
  officeDesc?: string;

  /**
   * 工号
   */
  workNum?: string;

  /**
   * 主管
   */
  officeSupervisor?: string;

  /**
   * 工作中心
   */
  workCenter?: string;

  /**
   * 办事处员工数
   */
  totalUser?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



