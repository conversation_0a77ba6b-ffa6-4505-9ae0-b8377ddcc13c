export interface RouteVO {
  /**
   * 工艺路线ID
   */
  routeId: string | number;

  /**
   * 工艺路线编号
   */
  routeCode: string;

  /**
   * 工艺路线名称
   */
  routeName: string;

  /**
   * 工艺路线说明
   */
  routeDesc: string;

  /**
   * 是否启用
   */
  enableFlag: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 预留字段1
   */
  attr1: string;

  /**
   * 预留字段2
   */
  attr2: string;

  /**
   * 预留字段3
   */
  attr3: number;

  /**
   * 预留字段4
   */
  attr4: number;

}

export interface RouteForm extends BaseEntity {
  /**
   * 工艺路线ID
   */
  routeId?: string | number;

  /**
   * 工艺路线编号
   */
  routeCode?: string;

  /**
   * 工艺路线名称
   */
  routeName?: string;

  /**
   * 工艺路线说明
   */
  routeDesc?: string;

  /**
   * 是否启用
   */
  enableFlag?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 预留字段1
   */
  attr1?: string;

  /**
   * 预留字段2
   */
  attr2?: string;

  /**
   * 预留字段3
   */
  attr3?: number;

  /**
   * 预留字段4
   */
  attr4?: number;

}

export interface RouteQuery extends PageQuery {

  /**
   * 工艺路线编号
   */
  routeCode?: string;

  /**
   * 工艺路线名称
   */
  routeName?: string;

  /**
   * 工艺路线说明
   */
  routeDesc?: string;

  /**
   * 是否启用
   */
  enableFlag?: string;

  /**
   * 预留字段1
   */
  attr1?: string;

  /**
   * 预留字段2
   */
  attr2?: string;

  /**
   * 预留字段3
   */
  attr3?: number;

  /**
   * 预留字段4
   */
  attr4?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



