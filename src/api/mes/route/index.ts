import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RouteForm, RouteQuery, RouteVO } from '@/api/mes/route/types';

/**
 * 查询工艺路线列表
 * @param query
 * @returns {*}
 */

export const listRoute = (query?: RouteQuery): AxiosPromise<RouteVO[]> => {
  return request({
    url: '/mes/route/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询工艺路线详细
 * @param routeId
 */
export const getRoute = (routeId: string | number): AxiosPromise<RouteVO> => {
  return request({
    url: '/mes/route/' + routeId,
    method: 'get'
  });
};

/**
 * 新增工艺路线
 * @param data
 */
export const addRoute = (data: RouteForm) => {
  return request({
    url: '/mes/route',
    method: 'post',
    data: data
  });
};

/**
 * 修改工艺路线
 * @param data
 */
export const updateRoute = (data: RouteForm) => {
  return request({
    url: '/mes/route',
    method: 'put',
    data: data
  });
};

/**
 * 删除工艺路线
 * @param routeId
 */
export const delRoute = (routeId: string | number | Array<string | number>) => {
  return request({
    url: '/mes/route/' + routeId,
    method: 'delete'
  });
};
