export interface ConvertRecordVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * MO号
   */
  moNumber: string;

  /**
   * NO号
   */
  noNumber: string;

  /**
   * 原始文件名
   */
  originalFileName: string;

  /**
   * 转换后文件名
   */
  convertedFileName: string;

  /**
   * ZIP文件路径（MinIO）
   */
  zipFilePath: string;

  /**
   * ZIP文件URL
   */
  zipFileUrl: string;

  /**
   * 处理状态：0-处理中，1-成功，2-失败
   */
  status: number;

  /**
   * 错误信息
   */
  errorMessage: string;

  /**
   * 文件大小（字节）
   */
  fileSize: number;
}

export interface ConvertRecordForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * MO号
   */
  moNumber?: string;

  /**
   * NO号
   */
  noNumber?: string;

  /**
   * 原始文件名
   */
  originalFileName?: string;

  /**
   * 转换后文件名
   */
  convertedFileName?: string;

  /**
   * ZIP文件路径（MinIO）
   */
  zipFilePath?: string;

  /**
   * ZIP文件URL
   */
  zipFileUrl?: string;

  /**
   * 处理状态：0-处理中，1-成功，2-失败
   */
  status?: number;

  /**
   * 错误信息
   */
  errorMessage?: string;

  /**
   * 文件大小（字节）
   */
  fileSize?: number;
}

export interface ConvertRecordQuery extends PageQuery {
  /**
   * MO号
   */
  moNumber?: string;

  /**
   * NO号
   */
  noNumber?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
