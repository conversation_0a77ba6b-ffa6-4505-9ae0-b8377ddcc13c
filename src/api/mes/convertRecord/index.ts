import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ConvertRecordVO, ConvertRecordForm, ConvertRecordQuery } from '@/api/mes/convertRecord/types';

/**
 * 查询PDF转换记录列表
 * @param query
 * @returns {*}
 */

export const listConvertRecord = (query?: ConvertRecordQuery): AxiosPromise<ConvertRecordVO[]> => {
  return request({
    url: '/mes/convertRecord/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询PDF转换记录详细
 * @param id
 */
export const getConvertRecord = (id: string | number): AxiosPromise<ConvertRecordVO> => {
  return request({
    url: '/mes/convertRecord/' + id,
    method: 'get'
  });
};

/**
 * 新增PDF转换记录
 * @param data
 */
export const addConvertRecord = (data: ConvertRecordForm) => {
  return request({
    url: '/mes/convertRecord',
    method: 'post',
    data: data
  });
};

/**
 * 修改PDF转换记录
 * @param data
 */
export const updateConvertRecord = (data: ConvertRecordForm) => {
  return request({
    url: '/mes/convertRecord',
    method: 'put',
    data: data
  });
};

/**
 * 删除PDF转换记录
 * @param id
 */
export const delConvertRecord = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mes/convertRecord/' + id,
    method: 'delete'
  });
};
