import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { WorkcenterForm, WorkcenterQuery, WorkcenterVO } from '@/api/mes/workcenter/types';

/**
 * 查询工作中心列表
 * @param query
 * @returns {*}
 */

export const listWorkcenter = (query?: WorkcenterQuery): AxiosPromise<WorkcenterVO[]> => {
  return request({
    url: '/mes/workcenter/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询工作中心详细
 * @param id
 */
export const getWorkcenter = (id: string | number): AxiosPromise<WorkcenterVO> => {
  return request({
    url: '/mes/workcenter/' + id,
    method: 'get'
  });
};

/**
 * 新增工作中心
 * @param data
 */
export const addWorkcenter = (data: WorkcenterForm) => {
  return request({
    url: '/mes/workcenter',
    method: 'post',
    data: data
  });
};

/**
 * 修改工作中心
 * @param data
 */
export const updateWorkcenter = (data: WorkcenterForm) => {
  return request({
    url: '/mes/workcenter',
    method: 'put',
    data: data
  });
};

/**
 * 删除工作中心
 * @param id
 */
export const delWorkcenter = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mes/workcenter/' + id,
    method: 'delete'
  });
};

/**
 * 同步SAP数据
 */
export const syncSapToWorkCenter = (): AxiosPromise<WorkcenterVO> => {
  return request({
    url: '/mes/workcenter/syncSapToWorkCenter',
    method: 'get'
  });
};
