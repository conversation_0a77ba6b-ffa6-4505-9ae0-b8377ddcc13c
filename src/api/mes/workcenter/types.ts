export interface WorkcenterVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * 代号
   */
  code: string;

  /**
   * 说明
   */
  description: string;

  /**
   * 类型
   */
  type: string;

  /**
   * 公司代码
   */
  companyCode: string;

  /**
   * 工厂代码
   */
  factoryCode: string;

  /**
   * 人工费用
   */
  laborCost: number;

  /**
   * 制造费用
   */
  manufacturingCost: number;

  /**
   * 是否委外
   */
  isOutsourced: number;

  /**
   * 单价
   */
  unitPrice: number;

  /**
   * 部门
   */
  completionDepartment: string;

  /**
   * 备注信息
   */
  remarks: string;

  /**
   * 创建人
   */
  createBy: string;

  /**
   * 创建时间
   */
  createTime: string;
}

export interface WorkcenterForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * 代号
   */
  code?: string;

  /**
   * 说明
   */
  description?: string;

  /**
   * 类型
   */
  type?: string;

  /**
   * 公司代码
   */
  companyCode?: string;

  /**
   * 工厂代码
   */
  factoryCode?: string;

  /**
   * 人工费用
   */
  laborCost?: number;

  /**
   * 制造费用
   */
  manufacturingCost?: number;

  /**
   * 是否委外
   */
  isOutsourced?: number;

  /**
   * 单价
   */
  unitPrice?: number;

  /**
   * 部门
   */
  completionDepartment?: string;

  /**
   * 备注信息
   */
  remarks?: string;
}

export interface WorkcenterQuery extends PageQuery {
  /**
   * 代号
   */
  code?: string;

  /**
   * 说明
   */
  description?: string;

  /**
   * 日期范围参数
   */
  params?: any;

  orderByColumn?: any;

  isAsc?: any;
}
