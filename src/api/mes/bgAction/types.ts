export interface BgActionVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 序号
   */
  sortNum: number;

  /**
   * 动作代码
   */
  actionCode: string;

  /**
   * 动作名称
   */
  actionName: string;

  /**
   * 部门代码
   */
  deptCode: string;

  /**
   * 部门名称
   */
  deptName: string;

  /**
   * 在用
   */
  isUse: string;

  /**
   * 需同步SAP
   */
  isSap: string;

  /**
   * 需同步Master
   */
  isMaster: string;

  /**
   * 需要机床
   */
  needMachin: string;

  /**
   * 需单独关单
   */
  needClose: string;

  /**
   * 外发
   */
  isOut: string;

  /**
   * 多次报工
   */
  isMult: string;

}

export interface BgActionForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 序号
   */
  sortNum?: number;

  /**
   * 动作代码
   */
  actionCode?: string;

  /**
   * 动作名称
   */
  actionName?: string;

  /**
   * 部门代码
   */
  deptCode?: string;

  /**
   * 部门名称
   */
  deptName?: string;

  /**
   * 在用
   */
  isUse?: string;

  /**
   * 需同步SAP
   */
  isSap?: string;

  /**
   * 需同步Master
   */
  isMaster?: string;

  /**
   * 需要机床
   */
  needMachin?: string;

  /**
   * 需单独关单
   */
  needClose?: string;

  /**
   * 外发
   */
  isOut?: string;

  /**
   * 多次报工
   */
  isMult?: string;

}

export interface BgActionQuery extends PageQuery {

  /**
   * 序号
   */
  sortNum?: number;

  /**
   * 动作代码
   */
  actionCode?: string;

  /**
   * 动作名称
   */
  actionName?: string;

  /**
   * 部门代码
   */
  deptCode?: string;

  /**
   * 部门名称
   */
  deptName?: string;

  /**
   * 在用
   */
  isUse?: string;

  /**
   * 需同步SAP
   */
  isSap?: string;

  /**
   * 需同步Master
   */
  isMaster?: string;

  /**
   * 需要机床
   */
  needMachin?: string;

  /**
   * 需单独关单
   */
  needClose?: string;

  /**
   * 外发
   */
  isOut?: string;

  /**
   * 多次报工
   */
  isMult?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



