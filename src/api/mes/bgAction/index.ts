import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { BgActionForm, BgActionQuery, BgActionVO } from '@/api/mes/bgAction/types';

/**
 * 查询报工动作列表
 * @param query
 * @returns {*}
 */

export const listBgAction = (query?: BgActionQuery): AxiosPromise<BgActionVO[]> => {
  return request({
    url: '/mes/bgAction/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询报工动作详细
 * @param id
 */
export const getBgAction = (id: string | number): AxiosPromise<BgActionVO> => {
  return request({
    url: '/mes/bgAction/' + id,
    method: 'get'
  });
};

/**
 * 新增报工动作
 * @param data
 */
export const addBgAction = (data: BgActionForm) => {
  return request({
    url: '/mes/bgAction',
    method: 'post',
    data: data
  });
};

/**
 * 修改报工动作
 * @param data
 */
export const updateBgAction = (data: BgActionForm) => {
  return request({
    url: '/mes/bgAction',
    method: 'put',
    data: data
  });
};

/**
 * 删除报工动作
 * @param id
 */
export const delBgAction = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mes/bgAction/' + id,
    method: 'delete'
  });
};
