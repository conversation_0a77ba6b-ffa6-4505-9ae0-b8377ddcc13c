export interface SupplierVO {
  /**
   * 供应商名称
   */
  id: number;
  /**
   * 供应商名称
   */
  supplierName: string;

  /**
   * 供应商编号
   */
  supplierCode: string;

  /**
   * 联系人
   */
  contactPerson: string;

  /**
   * 联系人邮箱
   */
  contactEmail: string;

  /**
   * 联系人电话
   */
  contactPhone: string;

  /**
   * 地址
   */
  address: string;

  /**
   * 状态
   */
  status: string;
}

export interface SupplierForm extends BaseEntity {
  /**
   * 供应商ID
   */
  id?: string | number;

  /**
   * 供应商名称
   */
  supplierName?: string;

  /**
   * 供应商编号
   */
  supplierCode?: string;

  /**
   * 联系人
   */
  contactPerson?: string;

  /**
   * 联系人邮箱
   */
  contactEmail?: string;

  /**
   * 联系人电话
   */
  contactPhone?: string;

  /**
   * 地址
   */
  address?: string;

  /**
   * 状态
   */
  status?: string;
}

export interface SupplierQuery extends PageQuery {
  /**
   * 供应商名称
   */
  supplierName?: string;

  /**
   * 供应商编号
   */
  supplierCode?: string;

  /**
   * 联系人
   */
  contactPerson?: string;

  /**
   * 联系人邮箱
   */
  contactEmail?: string;

  /**
   * 联系人电话
   */
  contactPhone?: string;

  /**
   * 地址
   */
  address?: string;

  /**
   * 状态
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
