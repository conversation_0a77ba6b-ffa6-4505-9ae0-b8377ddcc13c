import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { SupplierForm, SupplierQuery, SupplierVO } from '@/api/mes/supplier/types';

/**
 * 查询供应商信息列表
 * @param query
 * @returns {*}
 */

export const listSupplier = (query?: SupplierQuery): AxiosPromise<SupplierVO[]> => {
  return request({
    url: '/mes/supplier/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询供应商信息详细
 * @param id
 */
export const getSupplier = (id: string | number): AxiosPromise<SupplierVO> => {
  return request({
    url: '/mes/supplier/' + id,
    method: 'get'
  });
};

/**
 * 新增供应商信息
 * @param data
 */
export const addSupplier = (data: SupplierForm) => {
  return request({
    url: '/mes/supplier',
    method: 'post',
    data: data
  });
};

/**
 * 修改供应商信息
 * @param data
 */
export const updateSupplier = (data: SupplierForm) => {
  return request({
    url: '/mes/supplier',
    method: 'put',
    data: data
  });
};

/**
 * 删除供应商信息
 * @param id
 */
export const delSupplier = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mes/supplier/' + id,
    method: 'delete'
  });
};
/**
 * 同步SAP数据
 */
export const syncSapToSupplier = (): AxiosPromise<SupplierVO> => {
  return request({
    url: '/mes/supplier/syncSapToSupplier',
    method: 'get'
  });
};
