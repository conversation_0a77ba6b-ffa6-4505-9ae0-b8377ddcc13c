export interface CostCenterVO {
  /**
   * 
   */
  id: string | number;

  /**
   * 成本中心代码
   */
  costCenterCode: string;

  /**
   * 成本中心名称
   */
  costCenterName: string;

  /**
   * 单价
   */
  costCenterPrice: number;

}

export interface CostCenterForm extends BaseEntity {
  /**
   * 
   */
  id?: string | number;

  /**
   * 成本中心代码
   */
  costCenterCode?: string;

  /**
   * 成本中心名称
   */
  costCenterName?: string;

  /**
   * 单价
   */
  costCenterPrice?: number;

}

export interface CostCenterQuery extends PageQuery {

  /**
   * 成本中心代码
   */
  costCenterCode?: string;

  /**
   * 成本中心名称
   */
  costCenterName?: string;

  /**
   * 单价
   */
  costCenterPrice?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}



