import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { CostCenterForm, CostCenterQuery, CostCenterVO } from '@/api/mes/costCenter/types';

/**
 * 查询售后成本中心列表
 * @param query
 * @returns {*}
 */

export const listCostCenter = (query?: CostCenterQuery): AxiosPromise<CostCenterVO[]> => {
  return request({
    url: '/mes/costCenter/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询售后成本中心详细
 * @param id
 */
export const getCostCenter = (id: string | number): AxiosPromise<CostCenterVO> => {
  return request({
    url: '/mes/costCenter/' + id,
    method: 'get'
  });
};

/**
 * 新增售后成本中心
 * @param data
 */
export const addCostCenter = (data: CostCenterForm) => {
  return request({
    url: '/mes/costCenter',
    method: 'post',
    data: data
  });
};

/**
 * 修改售后成本中心
 * @param data
 */
export const updateCostCenter = (data: CostCenterForm) => {
  return request({
    url: '/mes/costCenter',
    method: 'put',
    data: data
  });
};

/**
 * 删除售后成本中心
 * @param id
 */
export const delCostCenter = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mes/costCenter/' + id,
    method: 'delete'
  });
};
