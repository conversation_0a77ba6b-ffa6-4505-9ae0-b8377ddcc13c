import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { PartInfoForm, PartInfoQuery, PartInfoVO } from '@/api/mes/partInfo/types';

/**
 * 查询物料档案信息列表
 * @param query
 * @returns {*}
 */

export const listPartInfo = (query?: PartInfoQuery): AxiosPromise<PartInfoVO[]> => {
  return request({
    url: '/mes/partInfo/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询物料档案信息详细
 * @param id
 */
export const getPartInfo = (id: string | number): AxiosPromise<PartInfoVO> => {
  return request({
    url: '/mes/partInfo/' + id,
    method: 'get'
  });
};

/**
 * 新增物料档案信息
 * @param data
 */
export const addPartInfo = (data: PartInfoForm) => {
  return request({
    url: '/mes/partInfo',
    method: 'post',
    data: data
  });
};

/**
 * 修改物料档案信息
 * @param data
 */
export const updatePartInfo = (data: PartInfoForm) => {
  return request({
    url: '/mes/partInfo',
    method: 'put',
    data: data
  });
};

/**
 * 删除物料档案信息
 * @param id
 */
export const delPartInfo = (id: string | number | Array<string | number>) => {
  return request({
    url: '/mes/partInfo/' + id,
    method: 'delete'
  });
};
