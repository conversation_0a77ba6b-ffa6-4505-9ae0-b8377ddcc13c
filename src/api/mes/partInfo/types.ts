export interface PartInfoVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 创建时间
   */
  createDate: string;

  /**
   * 更新时间
   */
  updateDate: string;

  /**
   * 长度(mm)
   */
  length1: number;

  /**
   * 物料类型
   */
  partType: string;

  /**
   * 规格描述
   */
  spec: string;

  /**
   * 高度(mm)
   */
  high1: number;

  /**
   * 计量单位
   */
  unit: string;

  /**
   * 物料组
   */
  partGroup: string;

  /**
   * 宽度(mm)
   */
  width1: string | number;

  /**
   * 物料编码
   */
  partId: string | number;

  /**
   * 物料名称
   */
  partName: string;

  /**
   * 批次管理标识
   */
  batchManag: string;

  /**
   * 生产组
   */
  prodGroup: string;

  /**
   * 工厂代码
   */
  plantCode: string;

  /**
   * 品牌名称
   */
  brand: string;

  /**
   * 图号
   */
  drawNum: string;

  /**
   * 供应商代码
   */
  compCode: string;

  /**
   * 是否禁用
   */
  unable: string;

  /**
   * 国产/进口
   */
  domesticOrImport: string;

  /**
   * 正品/替代品
   */
  authenticAlterna: string;

  /**
   * 密度(g/cm³)
   */
  density: number;

  /**
   * 品牌备注
   */
  brandRemarks: string;

  /**
   * 图纸版本说明
   */
  drawNumVersion: string;

  /**
   * 客户代码
   */
  custCode: string;

  /**
   * 行业标准(Y/N)
   */
  industryStd: string;

  /**
   * 状态(0禁用/1启用)
   */
  status: string;

  /**
   * 米重(kg/m)
   */
  meterWeight: number;

  /**
   * 图纸等级
   */
  drawingLevel: string;

  /**
   * 标准工艺
   */
  stdProcess: string;

  /**
   * 直径(mm)
   */
  diameter: number;

  /**
   * 内径(mm)
   */
  insideDiameter: string | number;

  /**
   * 外径(mm)
   */
  outerDiameter: number;

  /**
   * 包装尺寸
   */
  boxSize: string;

  /**
   * SAP同步状态
   */
  sapSyncInfo: string;
}

export interface PartInfoForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 长度(mm)
   */
  length1?: number;

  /**
   * 物料类型
   */
  partType?: string;

  /**
   * 规格描述
   */
  spec?: string;

  /**
   * 高度(mm)
   */
  high1?: number;

  /**
   * 计量单位
   */
  unit?: string;

  /**
   * 物料组
   */
  partGroup?: string;

  /**
   * 宽度(mm)
   */
  width1?: string | number;

  /**
   * 物料编码
   */
  partId?: string | number;

  /**
   * 物料名称
   */
  partName?: string;

  /**
   * 批次管理标识
   */
  batchManag?: string;

  /**
   * 生产组
   */
  prodGroup?: string;

  /**
   * 工厂代码
   */
  plantCode?: string;

  /**
   * 品牌名称
   */
  brand?: string;

  /**
   * 图号
   */
  drawNum?: string;

  /**
   * 供应商代码
   */
  compCode?: string;

  /**
   * 是否禁用
   */
  unable?: string;

  /**
   * 国产/进口
   */
  domesticOrImport?: string;

  /**
   * 正品/替代品
   */
  authenticAlterna?: string;

  /**
   * 密度(g/cm³)
   */
  density?: number;

  /**
   * 品牌备注
   */
  brandRemarks?: string;

  /**
   * 图纸版本说明
   */
  drawNumVersion?: string;

  /**
   * 客户代码
   */
  custCode?: string;

  /**
   * 行业标准(Y/N)
   */
  industryStd?: string;

  /**
   * 状态(0禁用/1启用)
   */
  status?: string;

  /**
   * 米重(kg/m)
   */
  meterWeight?: number;

  /**
   * 图纸等级
   */
  drawingLevel?: string;

  /**
   * 标准工艺
   */
  stdProcess?: string;

  /**
   * 直径(mm)
   */
  diameter?: number;

  /**
   * 内径(mm)
   */
  insideDiameter?: string | number;

  /**
   * 外径(mm)
   */
  outerDiameter?: number;

  /**
   * 包装尺寸
   */
  boxSize?: string;

  /**
   * SAP同步状态
   */
  sapSyncInfo?: string;

  /**
   * 产线编号
   */
  lineNum?: string;

  /**
   * 需求订单号
   */
  reqOrderNum?: string;

  /**
   * SAP更新状态
   */
  sapUpdateInfo?: string;

  /**
   * 原始物料号
   */
  originalItem?: string;

  /**
   * 多标准管控
   */
  manyStandard?: string;

  /**
   * 研发估值
   */
  rdValuation?: number;
}

export interface PartInfoQuery extends PageQuery {
  /**
   * 创建时间
   */
  createDate?: string;

  /**
   * 物料类型
   */
  partType?: string;

  /**
   * 物料编码
   */
  partId?: string | number;

  /**
   * 物料名称
   */
  partName?: string;

  /**
   * 图号
   */
  drawNum?: string;

  drawNumVersion?: string;

  /**
   * 状态(0禁用/1启用)
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
