import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { RouteProcessForm, RouteProcessQuery, RouteProcessVO } from '@/api/mes/routeProcess/types';

/**
 * 查询工艺组成列表
 * @param query
 * @returns {*}
 */

export const listRouteProcess = (query?: RouteProcessQuery): AxiosPromise<RouteProcessVO[]> => {
  return request({
    url: '/mes/routeProcess/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询工艺组成详细
 * @param recordId
 */
export const getRouteProcess = (recordId: string | number): AxiosPromise<RouteProcessVO> => {
  return request({
    url: '/mes/routeProcess/' + recordId,
    method: 'get'
  });
};

/**
 * 新增工艺组成
 * @param data
 */
export const addRouteProcess = (data: RouteProcessForm) => {
  return request({
    url: '/mes/routeProcess',
    method: 'post',
    data: data
  });
};

/**
 * 修改工艺组成
 * @param data
 */
export const updateRouteProcess = (data: RouteProcessForm) => {
  return request({
    url: '/mes/routeProcess',
    method: 'put',
    data: data
  });
};

/**
 * 删除工艺组成
 * @param recordId
 */
export const delRouteProcess = (recordId: string | number | Array<string | number>) => {
  return request({
    url: '/mes/routeProcess/' + recordId,
    method: 'delete'
  });
};
