export interface RouteProcessVO {
  /**
   * 记录ID
   */
  recordId: string | number;

  /**
   * 工艺路线ID
   */
  routeId: string | number;

  /**
   * 工序ID
   */
  processId: string | number;

  /**
   * 工序编码
   */
  processCode: string;

  /**
   * 工序名称
   */
  processName: string;

  /**
   * 序号
   */
  orderNum: number;

  /**
   * 工序ID
   */
  nextProcessId: string | number;

  /**
   * 工序编码
   */
  nextProcessCode: string;

  /**
   * 工序名称
   */
  nextProcessName: string;

  /**
   * 与下一道工序关系
   */
  linkType: string;

  /**
   * 批量等待时间
   */
  defaultPreTime: number;

  /**
   * 等待时间
   */
  defaultSufTime: number;

  /**
   * 急单时间
   */
  urgWaitTime: number;

  /**
   * 甘特图显示颜色
   */
  colorCode: string;

  /**
   * 关键工序
   */
  keyFlag: string;

  /**
   * 是否检验
   */
  isCheck: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 预留字段1
   */
  attr1: string;

  /**
   * 预留字段2
   */
  attr2: string;

  /**
   * 预留字段3
   */
  attr3: number;

  /**
   * 预留字段4
   */
  attr4: number;
}

export interface RouteProcessForm extends BaseEntity {
  /**
   * 记录ID
   */
  recordId?: string | number;

  /**
   * 工艺路线ID
   */
  routeId?: string | number;

  /**
   * 工序ID
   */
  processId?: string | number;

  /**
   * 工序编码
   */
  processCode?: string;

  /**
   * 工序名称
   */
  processName?: string;

  /**
   * 序号
   */
  orderNum?: number;

  /**
   * 工序ID
   */
  nextProcessId?: string | number;

  /**
   * 工序编码
   */
  nextProcessCode?: string;

  /**
   * 工序名称
   */
  nextProcessName?: string;

  /**
   * 与下一道工序关系
   */
  linkType?: string;

  /**
   * 准备时间
   */
  defaultPreTime?: number;

  /**
   * 等待时间
   */
  defaultSufTime?: number;

  /**
   * 甘特图显示颜色
   */
  colorCode?: string;

  /**
   * 关键工序
   */
  keyFlag?: string;

  /**
   * 是否检验
   */
  isCheck?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 预留字段1
   */
  attr1?: string;

  /**
   * 预留字段2
   */
  attr2?: string;

  /**
   * 预留字段3
   */
  attr3?: number;

  /**
   * 预留字段4
   */
  attr4?: number;
}

export interface RouteProcessQuery extends PageQuery {
  /**
   * 工艺路线ID
   */
  routeId?: string | number;

  /**
   * 工序ID
   */
  processId?: string | number;

  /**
   * 工序编码
   */
  processCode?: string;

  /**
   * 工序名称
   */
  processName?: string;

  /**
   * 序号
   */
  orderNum?: number;

  /**
   * 工序ID
   */
  nextProcessId?: string | number;

  /**
   * 工序编码
   */
  nextProcessCode?: string;

  /**
   * 工序名称
   */
  nextProcessName?: string;

  /**
   * 与下一道工序关系
   */
  linkType?: string;

  /**
   * 准备时间
   */
  defaultPreTime?: number;

  /**
   * 等待时间
   */
  defaultSufTime?: number;

  /**
   * 甘特图显示颜色
   */
  colorCode?: string;

  /**
   * 关键工序
   */
  keyFlag?: string;

  /**
   * 是否检验
   */
  isCheck?: string;

  /**
   * 预留字段1
   */
  attr1?: string;

  /**
   * 预留字段2
   */
  attr2?: string;

  /**
   * 预留字段3
   */
  attr3?: number;

  /**
   * 预留字段4
   */
  attr4?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
