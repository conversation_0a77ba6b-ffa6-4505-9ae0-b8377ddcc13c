export interface DeclarationVO {
  /**
   * 主键ID
   */
  id: string | number;

  /**
   * MO
   */
  mo: string;

  /**
   * 单台净重
   */
  netWeight: number;

  /**
   * 货物图片
   */
  goodsPicture: string;

  /**
   * 型号图片
   */
  modelPicture: string;

  /**
   * 标签_名牌图片（nameplate picture）
   */
  nameplatePicture: string;

  /**
   * 备注
   */
  remarks: string;

  /**
   * 申报品名
   */
  declarationName: string;

  /**
   * HS CODE
   */
  hsCode: string;

  /**
   * 申报要素
   */
  declarationElements: string;

  /**
   * 料号
   */
  materialNumber: string;

  /**
   * 零件号
   */
  partNumber: string;

  /**
   * 中文品名 Chinese Name
   */
  chineseName: string;

  /**
   * 英文品名 English Name
   */
  englishName: string;

  /**
   * 生产原厂 manufacture
   */
  manufacturer: string;

  /**
   * 品牌
   */
  brand: string;

  /**
   * 型号
   */
  model: string;

  /**
   * 序列
   */
  serialNumber: string;

  /**
   * 设备情况 （旧/新）
   */
  equipmentCondition: string;

  /**
   * 原产地 Origin
   */
  origin: string;

  /**
   * 生产/购买年月 Date
   */
  productionDate: string;

  /**
   * 数量
   */
  quantity: number;

  /**
   * 单位
   */
  unit: string;

  /**
   * 单价
   */
  unitPrice: number;

  /**
   * 材质(非设备)
   */
  material: string;

  /**
   * 尺寸
   */
  dimension: string;

  /**
   * 工作原理（设备） working principle
   */
  workingPrinciple: string;

  /**
   * 用途
   */
  functionEn: string;

  /**
   * 功能
   */
  functionality: string;

  /**
   * 功率（如有） Power
   */
  power: string;

  /**
   * 电压（如有） Voltage
   */
  voltage: string;

  /**
   * 加工方法
   */
  processingMethod: string;

  /**
   * 是否有接头 （针对线材类）
   */
  hasConnector: string;

  /**
   * 结构类型
   */
  structureType: string;

  /**
   * 总净重/KG
   */
  totalNetWeight: number;

  /**
   * 创建者
   */
  createBy: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 胶管类的需确认以下5项
   */
  hydraulicHose: string;

  /**
   * 橡胶类的需确认以下5项
   */
  rubberMaterial: string;

  /**
   * 审批状态 0草稿，1已提交，2归档，3退回
   */
  status?: number;

  /**
   * 审批状态名称
   */
  statusName?: string;

  /**
   * 创建者姓名
   */
  createByName?: string;
}

export interface DeclarationForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: string | number;

  /**
   * MO
   */
  mo?: string;

  /**
   * 单台净重
   */
  netWeight?: number;

  /**
   * 货物图片
   */
  goodsPicture?: string;

  /**
   * 型号图片
   */
  modelPicture?: string;

  /**
   * 标签_名牌图片（nameplate picture）
   */
  nameplatePicture?: string;

  /**
   * 备注
   */
  remarks?: string;

  /**
   * 申报品名
   */
  declarationName?: string;

  /**
   * HS CODE
   */
  hsCode?: string;

  /**
   * 申报要素
   */
  declarationElements?: string;

  /**
   * 料号
   */
  materialNumber?: string;

  /**
   * 零件号
   */
  partNumber?: string;

  /**
   * 中文品名 Chinese Name
   */
  chineseName?: string;

  /**
   * 英文品名 English Name
   */
  englishName?: string;

  /**
   * 生产原厂 manufacture
   */
  manufacturer?: string;

  /**
   * 品牌
   */
  brand?: string;

  /**
   * 型号
   */
  model?: string;

  /**
   * 序列
   */
  serialNumber?: string;

  /**
   * 设备情况 （旧/新）
   */
  equipmentCondition?: string;

  /**
   * 原产地 Origin
   */
  origin?: string;

  /**
   * 生产/购买年月 Date
   */
  productionDate?: string;

  /**
   * 数量
   */
  quantity?: number;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 单价
   */
  unitPrice?: number;

  /**
   * 材质(非设备)
   */
  material?: string;

  /**
   * 尺寸
   */
  dimension?: string;

  /**
   * 工作原理（设备） working principle
   */
  workingPrinciple?: string;

  /**
   * 用途
   */
  functionEn?: string;

  /**
   * 功能
   */
  functionality?: string;

  /**
   * 功率（如有） Power
   */
  power?: string;

  /**
   * 电压（如有） Voltage
   */
  voltage?: string;

  /**
   * 加工方法
   */
  processingMethod?: string;

  /**
   * 是否有接头 （针对线材类）
   */
  hasConnector?: string;

  /**
   * 结构类型
   */
  structureType?: string;

  /**
   * 总净重/KG
   */
  totalNetWeight?: number;

  /**
   * 胶管类的需确认以下5项
   */
  hydraulicHose?: string;

  /**
   * 橡胶类的需确认以下5项
   */
  rubberMaterial?: string;

  /**
   * 审批状态 0草稿，1已提交，2归档，3退回
   */
  status?: string;
}

export interface DeclarationQuery extends PageQuery {
  /**
   * MO
   */
  mo?: string;

  /**
   * 申报品名
   */
  declarationName?: string;

  /**
   * HS CODE
   */
  hsCode?: string;

  /**
   * 申报要素
   */
  declarationElements?: string;

  /**
   * 料号
   */
  materialNumber?: string;

  /**
   * 零件号
   */
  partNumber?: string;

  /**
   * 中文品名 Chinese Name
   */
  chineseName?: string;

  /**
   * 品牌
   */
  brand?: string;

  /**
   * 胶管类的需确认以下5项
   */
  hydraulicHose?: string;

  /**
   * 橡胶类的需确认以下5项
   */
  rubberMaterial?: string;

  /**
   * 审批状态 0草稿，1已提交，2归档，3退回
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
