import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { DeclarationVO, DeclarationForm } from '@/api/mes/declaration/types';

/**
 * 查询所有备案信息列表（原有接口）
 * @param query
 * @returns {*}
 */
export const listDeclarationAll = (query) => {
  return request({
    url: '/mes/declaration/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询待审批列表
 * @param params
 */
export const listDeclaration = (params) => {
  return request({
    url: '/mes/declaration/pending',
    method: 'get',
    params
  });
};

/**
 * 查询备件备案信息详细
 * @param id
 */
export const getDeclaration = (id: string | number): AxiosPromise<DeclarationVO> => {
  return request({
    url: '/mes/declaration/' + id,
    method: 'get'
  });
};

/**
 * 新增备件备案信息
 * @param data
 */
export const addDeclaration = (data: DeclarationForm) => {
  return request({
    url: '/mes/declaration',
    method: 'post',
    data: data
  });
};

/**
 * 修改备件备案信息
 * @param data
 */
export const updateDeclaration = (data: DeclarationForm) => {
  return request({
    url: '/mes/declaration',
    method: 'put',
    data: data
  });
};

/**
 * 删除备件备案信息
 * @param id
 */
export const delDeclaration = (id: string | number | Array<string | number>) => {
  const ids = Array.isArray(id) ? id : [id];
  return request({
    url: '/mes/declaration/' + ids.join(','),
    method: 'delete'
  });
};

/**
 * 提交审批
 * @param id
 */
export const submitApproval = (id: string | number | Array<string | number>) => {
  const ids = Array.isArray(id) ? id : [id];
  return request({
    url: '/mes/declaration/submit/' + ids.join(','),
    method: 'put'
  });
};

/**
 * 归档备案信息（审批通过）
 * @param id
 */
export const archiveDeclaration = (id: string | number | Array<string | number>) => {
  const ids = Array.isArray(id) ? id : [id];
  return request({
    url: '/mes/declaration/archive/' + ids.join(','),
    method: 'put'
  });
};

/**
 * 退回备案信息
 * @param id
 * @param reasonStr
 */
export const rejectDeclaration = (id: string | number | Array<string | number>, reason) => {
  const ids = Array.isArray(id) ? id : [id];
  return request({
    url: `/mes/declaration/reject/${ids.join(',')}`,
    method: 'put',
    params: { reason }
  });
};
/**
 * 修改申报要素备件备案信息
 * @param data
 */
export const editDeclaration = (data: DeclarationForm) => {
  return request({
    url: '/mes/declaration/editDeclaration',
    method: 'post',
    data: data
  });
};
