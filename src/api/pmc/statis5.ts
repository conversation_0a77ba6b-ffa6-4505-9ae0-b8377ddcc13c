import request from '@/utils/request';

export const PMC_REPORT_BASE_URL = '/pmc/report';

/**
 * 根据MO号和图号查询图片地址
 * @param data
 */
export function getPicUrl(data: object = {}) {
  return request({
    url: `${PMC_REPORT_BASE_URL}/picUrl`,
    method: 'get',
    params: data
  });
}

/**
 *交叉工序
 * @param data
 */
export function getStatis5Data(data: object = {}) {
  return request({
    url: `${PMC_REPORT_BASE_URL}/statis5/search`,
    method: 'get',
    params: data,
    timeout: 3 * 60 * 1000
  });
}

/**
 *统计
 * @param data
 */
export function getStatis5Data2(data: object = {}) {
  return request({
    url: `${PMC_REPORT_BASE_URL}/statis5/search2`,
    method: 'get',
    params: data,
    timeout: 3 * 60 * 1000
  });
}

/**
 * 根据MO号查询扫描记录
 * @param data
 */
export function getStatis5Data3(data: object = {}) {
  return request({
    url: `${PMC_REPORT_BASE_URL}/statis5/search3`,
    method: 'get',
    params: data,
    timeout: 3 * 60 * 1000
  });
}

/**
 * 根据MO号查询工艺记录
 * @param data
 */
export function getStatis5Data4(data: object = {}) {
  return request({
    url: `${PMC_REPORT_BASE_URL}/statis5/search4`,
    method: 'get',
    params: data,
    timeout: 3 * 60 * 1000
  });
}
/**
 * 查询按钮
 * @param data
 */
export function getStatis5Data5(data: object = {}) {
  return request({
    url: `${PMC_REPORT_BASE_URL}/statis5/search5`,
    method: 'get',
    params: data,
    timeout: 3 * 60 * 1000
  });
}

