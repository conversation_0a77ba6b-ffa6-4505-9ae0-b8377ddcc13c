import request from '@/utils/request';
import { AxiosPromise } from 'axios';

export const getDataList = (query?: any): AxiosPromise<any> => {
  return request({
    url: '/pmc/order_taking_delivery/statistics',
    method: 'get',
    params: query
  });
};

export const getMerchandisers = (): AxiosPromise<any> => {
  return request({
    url: '/pmc/order_taking_delivery/merchandiser',
    method: 'get'
  });
};
