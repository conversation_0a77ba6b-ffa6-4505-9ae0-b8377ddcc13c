import request from '@/utils/request';

export const PMC_MO_MANAGER_BASE_URL = '/pmc/moManager';

/**
 * 加载装配订单管理数据
 */
export function loadStatis6Data() {
  return request({
    url: `${PMC_MO_MANAGER_BASE_URL}/statis6/loadData`,
    method: 'get'
  });
}

/**
 * 查询装配订单管理数据
 * @param data 查询参数
 */
export function getStatis6Data(data: object = {}) {
  return request({
    url: `${PMC_MO_MANAGER_BASE_URL}/statis6/search`,
    method: 'get',
    params: data
  });
}

/**
 * 保存装配订单管理数据
 * @param data 保存的数据
 */
export function saveStatis6Data(data: object = {}) {
  return request({
    url: `${PMC_MO_MANAGER_BASE_URL}/statis6/save`,
    method: 'post',
    data: data
  });
}