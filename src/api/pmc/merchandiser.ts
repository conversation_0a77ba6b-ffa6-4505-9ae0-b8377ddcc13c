import request from '@/utils/request';

export const MERCHANDISER_BASE_URL = '/pmc/merchandiser';
export const MERCHANDISER_DL_URL = `${MERCHANDISER_BASE_URL}/dl`;


/**
 * 获取跟单人
 */
export function getMerchandiserList() {
  return request({
    url: `${MERCHANDISER_BASE_URL}/list`,
    method: 'get'
  });
}

/**
 * 根据类型获取数据
 * @param data
 */
export function queryMerchandiserData(data: object = {}) {
  return request({
    url: `${MERCHANDISER_BASE_URL}/data`,
    method: 'get',
    params: data
  });
}

export function editMerchandiserData(data: object = {}) {
  return request({
    url: `${MERCHANDISER_BASE_URL}/edit`,
    method: 'post',
    data: data
  });
}
