/**
 * PMC项目进度表查询参数
 */
export interface ProjectScheduleQuery {
  /** 页码 */
  pageNum?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 客户代码 */
  customerCode?: string;
  /** 跟单负责人 */
  responsiblePerson?: string;
  /** DR单号 */
  drNo?: string;
  /** DR项次 */
  drItem?: string;
}

/**
 * PMC项目进度表数据
 */
export interface ProjectScheduleData {
  /** ID */
  id?: number;
  /** 订单类别 */
  orderType?: string;
  /** 出货地点 */
  shipmentLocation?: string;
  /** 项目名称 */
  projectName?: string;
  /** 客户代码 */
  customerCode?: string;
  /** 客户PO号 */
  customerPo?: string;
  /** 接单日期 */
  orderDate?: string;
  /** DR单号 */
  drNo?: string;
  /** DR项次 */
  drItem?: string;
  /** PMC要求交期 */
  pmcRequiredDate?: string;
  /** 状态 */
  status?: string;
  /** MO单号 */
  moNo?: string;
  /** 图号 */
  drawingNo?: string;
  /** 描述 */
  description?: string;
  /** 订单数量 */
  orderQty?: number;
  /** 客户要求交期 */
  customerRequiredDate?: string;
  /** 进度概况及备注 */
  progressRemark?: string;
  /** 交货风险描述 */
  deliveryRiskDesc?: string;
  /** 自制欠数 */
  selfMadeShortage?: number;
  /** 钣金欠数 */
  sheetMetalShortage?: number;
  /** 外发欠数 */
  outsourcingShortage?: number;
  /** 标准件欠数 */
  standardPartsShortage?: number;
  /** 时间节点 */
  timeNode?: string;
  /** 下图日期 */
  drawingReleaseDate?: string;
  /** 机架A */
  frameA?: string;
  /** 机架B */
  frameB?: string;
  /** 零件齐料时间 */
  partsReadyTime?: string;
  /** 标准件齐料时间 */
  standardPartsReadyTime?: string;
  /** 装配开始 */
  assemblyStart?: string;
  /** 装配结束 */
  assemblyEnd?: string;
  /** 调试开始 */
  debugStart?: string;
  /** 调试结束 */
  debugEnd?: string;
  /** 出货ETD */
  shipmentETD?: string;
  /** 装配车间 */
  assemblyWorkshop?: string;
  /** 项目经理 */
  projectManager?: string;
  /** 下单员姓名 */
  orderPersonName?: string;
  /** 跟单负责人 */
  responsiblePerson?: string;
  /** BOM责任人 */
  bomResponsible?: string;
  /** 计划类型 */
  planType?: string;
  /** 品号 */
  partNo?: string;
  /** SJJD */
  sjjd?: number;

  usr: string;

  usName: string;
}

/**
 * 责任负责人数据
 */
export interface ResponsiblePerson {
  /** 负责人ID */
  id: number;
  /** 负责人姓名 */
  name: string;
}