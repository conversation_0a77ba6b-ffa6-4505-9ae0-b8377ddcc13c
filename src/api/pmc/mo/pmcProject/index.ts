import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { ProjectScheduleQuery, ProjectScheduleData, ResponsiblePerson } from './types';

export const PMC_REPORTS_BASE_URL = '/pmc/mo/report';

/**
 * 查询PMC项目进度表列表
 * @param query 查询参数
 */
export function getProjectScheduleList(query: ProjectScheduleQuery): AxiosPromise<{
  rows: ProjectScheduleData[];
  total: number;
}> {
  return request({
    url: `${PMC_REPORTS_BASE_URL}/project-schedule/list`,
    method: 'get',
    params: query
  });
}

/**
 * 查询PMC项目进度表列表
 * @param query 查询参数
 */
export function loadDataFromMaster(): AxiosPromise<{
  rows: ProjectScheduleData[];
  total: number;
}> {
  return request({
    url: `${PMC_REPORTS_BASE_URL}/load/data`,
    method: 'get',
  });
}

/**
 * 获取PMC项目进度表详细信息
 * @param id 项目进度表ID
 */
export function getProjectScheduleDetail(id: string | number): AxiosPromise<ProjectScheduleData> {
  return request({
    url: `${PMC_REPORTS_BASE_URL}/project-schedule/${id}`,
    method: 'get'
  });
}

/**
 * 新增PMC项目进度表
 * @param data 项目进度表数据
 */
export function addProjectSchedule(data: ProjectScheduleData): AxiosPromise<any> {
  return request({
    url: `${PMC_REPORTS_BASE_URL}/project-schedule`,
    method: 'post',
    data: data
  });
}

/**
 * 修改PMC项目进度表
 * @param data 项目进度表数据
 */
export function updateProjectSchedule(data: ProjectScheduleData): AxiosPromise<any> {
  return request({
    url: `${PMC_REPORTS_BASE_URL}/project-schedule`,
    method: 'put',
    data: data
  });
}

/**
 * 删除PMC项目进度表
 * @param id 项目进度表ID或ID数组
 */
export function deleteProjectSchedule(id: string | number | Array<string | number>): AxiosPromise<any> {
  return request({
    url: `${PMC_REPORTS_BASE_URL}/project-schedule/${id}`,
    method: 'delete'
  });
}

/**
 * 导出PMC项目进度表
 * @param query 查询参数
 */
export function exportProjectSchedule(query: ProjectScheduleQuery): AxiosPromise<Blob> {
  return request({
    url: `${PMC_REPORTS_BASE_URL}/project-schedule/export`,
    method: 'get',
    params: query,
    responseType: 'blob'
  });
}

/**
 * 获取责任负责人列表
 */
export function getResponsiblePersonList(): AxiosPromise<ResponsiblePerson[]> {
  return request({
    url: `${PMC_REPORTS_BASE_URL}/responsible-person/list`,
    method: 'get'
  });
}