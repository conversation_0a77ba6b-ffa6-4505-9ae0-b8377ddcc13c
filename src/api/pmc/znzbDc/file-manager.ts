import request from '@/utils/request';

export const FILE_MANAGER_BASE_URL = '/pmc/znzbDc';


/**
 * 根据类型获取数据
 * @param data
 */
export function queryFileList(data: object = {}) {
  return request({
    url: `${FILE_MANAGER_BASE_URL}/list`,
    method: 'get',
    params: data
  });
}

/**
 * 上传文件
 * @param formData
 */
export function uploadManagerFile(formData: FormData): any {
  return request({
    url: `${FILE_MANAGER_BASE_URL}/upload`,
    method: 'post',
    data: formData
  });
}

/**
 * 审核
 * @param data
 */
export function auditFile(data: any): any {
  return request({
    url: `${FILE_MANAGER_BASE_URL}/audit`,
    method: 'post',
    data: data
  });
}

export function deleteFile(id: string | number | Array<string | number>): any {
  return request({
    url: `${FILE_MANAGER_BASE_URL}/remove/` + id,
    method: 'delete'
  });
}
