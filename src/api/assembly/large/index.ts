import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { KanbanForm, KanbanQuery, KanbanVO } from '@/api/assembly/kanban/types';

/**
 * 查询装配看板记录列表
 * @param query
 * @returns {*}
 */

export const listKanban = (query?: KanbanQuery): AxiosPromise<KanbanVO[]> => {
  return request({
    url: '/sfc/assembly/kanban/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询装配看板记录详细
 * @param id
 */
export const getKanban = (id: string | number): AxiosPromise<KanbanVO> => {
  return request({
    url: '/sfc/assembly/kanban/' + id,
    method: 'get'
  });
};

/**
 * 新增装配看板记录
 * @param data
 */
export const addKanban = (data: KanbanForm) => {
  return request({
    url: '/sfc/assembly/kanban',
    method: 'post',
    data: data
  });
};

/**
 * 修改装配看板记录
 * @param data
 */
export const updateKanban = (data: KanbanForm) => {
  return request({
    url: '/sfc/assembly/kanban',
    method: 'put',
    data: data
  });
};

/**
 * 删除装配看板记录
 * @param id
 */
export const delKanban = (id: string | number | Array<string | number>) => {
  return request({
    url: '/sfc/assembly/kanban/' + id,
    method: 'delete'
  });
};
/**
 * 查询sfcnewkb记录
 * @returns
 */
export const getInfos = (query?: KanbanQuery): AxiosPromise<KanbanVO[]> => {
  return request({
    url: '/sfc/assembly/kanban/getList',
    method: 'get',
    params: query
  });
};

/**
 * 获取echars数据
 *
 * @return
 */
export const getEcharsData = (): AxiosPromise<KanbanVO> => {
  return request({
    url: '/sfc/assembly/kanban/wk-xl',
    method: 'get'
  });
};
