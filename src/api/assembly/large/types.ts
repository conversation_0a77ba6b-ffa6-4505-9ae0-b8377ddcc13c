export interface KanbanVO {
  /**
   * ID
   */
  id: string | number;

  /**
   * 日期
   */
  sysdt: string;

  /**
   * MO号
   */
  moNo: string;

  /**
   * 设备名称
   */
  sbName: string;

  /**
   * 装配状态
   */
  sbSta: string;

  /**
   * 图号
   */
  dwgNo: string;

  /**
   * 订单数量
   */
  oQty: number;

  /**
   * 装配数量
   */
  zpQty: number;

  /**
   * 当前进度
   */
  curJd: number;

  /**
   * 当前进度
   */
  curJd2: number;

  /**
   * 产品组
   */
  prdGroup: string;

  /**
   * 负责人
   */
  mUsr: string;

  /**
   * 负责人2
   */
  mUsr2: string;

  /**
   * 标记
   */
  flag: string;

  /**
   * MO状态
   */
  moSta: string;
}

export interface KanbanForm extends BaseEntity {
  /**
   * ID
   */
  id?: string | number;

  /**
   * 日期
   */
  sysdt?: string;

  /**
   * MO号
   */
  moNo?: string;

  /**
   * 设备名称
   */
  sbName?: string;

  /**
   * 装配状态
   */
  sbSta?: string;

  /**
   * 图号
   */
  dwgNo?: string;

  /**
   * 订单数量
   */
  oQty?: number;

  /**
   * 装配数量
   */
  zpQty?: number;

  /**
   * 当前进度
   */
  curJd?: number;

  /**
   * 当前进度
   */
  curJd2?: number;

  /**
   * 产品组
   */
  prdGroup?: string;

  /**
   * 负责人
   */
  mUsr?: string;

  /**
   * 负责人2
   */
  mUsr2?: string;

  /**
   * 标记
   */
  flag?: string;

  /**
   * MO状态
   */
  moSta?: string;
}

export interface KanbanQuery extends PageQuery {
  /**
   * MO号
   */
  moNo?: string;

  /**
   * 设备名称
   */
  sbName?: string;

  /**
   * 负责人
   */
  mUsr?: string;

  /**
   * 负责人2
   */
  mUsr2?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
