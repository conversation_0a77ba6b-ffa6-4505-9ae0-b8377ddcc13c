import { to } from 'await-to-js';
import { getToken, removeToken, setToken } from '@/utils/auth';
import { login as loginApi, logout as logoutApi, getInfo as getUserInfo, goSsoAuthUrl, doLoginByTicket } from '@/api/login';
import { LoginData } from '@/api/types';
import defAva from '@/assets/images/profile.jpeg';
import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useUserStore = defineStore('user', () => {
  const token = ref(getToken());
  const name = ref('');
  const nickname = ref('');
  const userId = ref<string | number>('');
  const tenantId = ref<string>('');
  const avatar = ref('');
  const roles = ref<Array<string>>([]); // 用户角色编码集合 → 判断路由权限
  const permissions = ref<Array<string>>([]); // 用户权限编码集合 → 判断按钮权限

  /**
   * 登录
   * @param userInfo
   * @returns
   */
  const login = async (userInfo: LoginData): Promise<void> => {
    const [err, res] = await to(loginApi(userInfo));
    if (res) {
      const data = res.data;
      setToken(data.access_token);
      token.value = data.access_token;
      return Promise.resolve();
    }
    return Promise.reject(err);
  };

  // 获取用户信息
  const getInfo = async (): Promise<void> => {
    const [err, res] = await to(getUserInfo());
    if (res) {
      const data = res.data;
      const user = data.user;
      const profile = user.avatar == '' || user.avatar == null ? defAva : user.avatar;

      if (data.roles && data.roles.length > 0) {
        // 验证返回的roles是否是一个非空数组
        roles.value = data.roles;
        permissions.value = data.permissions;
      } else {
        roles.value = ['ROLE_DEFAULT'];
      }
      name.value = user.userName;
      nickname.value = user.nickName;
      avatar.value = profile;
      userId.value = user.userId;
      tenantId.value = user.tenantId;
      return Promise.resolve();
    }
    return Promise.reject(err);
  };

  // 注销
  const logout = async (): Promise<void> => {
    const [err, res] = await to(logoutApi());
    token.value = '';
    roles.value = [];
    permissions.value = [];
    removeToken();
  };

  //获取sso登录认证地址
  const goAuthUrlSso = async (path: string): Promise<void> => {
    const [err, res] = await to(goSsoAuthUrl(path));
    if (res) {
      const data = res.data;
      location.href = data;
    }
  };

  //根据ticket登录
  const doLoginByTicketSso = async (ticket: string, back: string): Promise<void> => {
    const [err, res] = await to(doLoginByTicket(ticket));
    if (res.code === 200) {
      setToken(res.data);
      location.href = decodeURIComponent(back);
    } else {
      alert(res.msg);
    }
  };

  const setAvatar = (value: string) => {
    avatar.value = value;
  };

  return {
    userId,
    tenantId,
    token,
    nickname,
    avatar,
    roles,
    permissions,
    login,
    getInfo,
    logout,
    setAvatar,
    goAuthUrlSso,
    doLoginByTicketSso
  };
});
